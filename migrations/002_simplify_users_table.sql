-- 简化用户表结构 - 移除不需要的字段
-- 执行时间: 2025-06-21

-- 备份当前数据（可选）
-- CREATE TABLE users_backup AS SELECT * FROM users;

-- 删除不需要的字段
ALTER TABLE users 
DROP COLUMN email,
DROP COLUMN username,
DROP COLUMN password_hash,
DROP COLUMN first_name,
DROP COLUMN last_name,
DROP COLUMN nickname,
DROP COLUMN gender,
DROP COLUMN city,
DROP COLUMN province,
DROP COLUMN country,
DROP COLUMN language,
DROP COLUMN last_login_at,
DROP COLUMN last_login_ip;

-- 更新表结构说明
-- 保留的字段:
-- - id: 用户唯一标识
-- - phone: 手机号（微信授权获取）
-- - avatar: 头像URL
-- - role: 用户角色
-- - is_active: 是否激活
-- - wechat_open_id: 微信OpenID（主要标识）
-- - wechat_union_id: 微信UnionID
-- - wechat_session_key: 微信会话密钥
-- - created_at: 创建时间
-- - updated_at: 更新时间
