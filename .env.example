# 宝丰后台管理系统环境配置示例
# 复制此文件为 .env 并修改相应的值

# 应用配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=baofeng123456
DB_NAME=baofeng_admin
DB_CHARSET=utf8mb4

# JWT 配置 (请在生产环境中使用强密钥)
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=wx3263a06336cf0b6b
WECHAT_APP_SECRET=825d2600516af087542644467cf25fb8

# API 配置
API_VERSION=v1
API_PREFIX=/api

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS 配置
CORS_ORIGIN=*

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
BCRYPT_ROUNDS=12
