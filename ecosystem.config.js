module.exports = {
  apps: [
    {
      name: 'baofeng-rd-api',
      script: 'dist/index.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000
      }
    },
    {
      name: 'price-rankings-cron',
      script: 'dist/scripts/calculatePriceRankings.js',
      instances: 1,
      autorestart: false,
      cron_restart: '0 8 * * *', // 每天早上 8:00 运行
      watch: false,
      env: {
        NODE_ENV: 'production'
      }
    }
  ]
};
