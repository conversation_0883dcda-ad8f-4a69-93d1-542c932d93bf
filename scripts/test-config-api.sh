#!/bin/bash

# 微信小程序配置API测试脚本
# 测试公开接口功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:3000"
API_URL="$BASE_URL/api/v1"

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  微信小程序配置API测试"
    echo "=================================================="
    echo -e "${NC}"
}

print_test() {
    echo -e "${YELLOW}[测试] $1${NC}"
}

print_success() {
    echo -e "${GREEN}[成功] $1${NC}"
}

print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

print_info() {
    echo -e "${BLUE}[信息] $1${NC}"
}

# 测试健康检查
test_health() {
    print_test "健康检查..."
    
    response=$(curl -s "$BASE_URL/health")
    if echo "$response" | grep -q '"success":true'; then
        print_success "健康检查通过"
        return 0
    else
        print_error "健康检查失败"
        echo "$response"
        return 1
    fi
}

# 测试获取所有有效配置
test_get_all_configs() {
    print_test "获取所有有效配置..."
    
    response=$(curl -s "$API_URL/miniprogram/configs/active")
    if echo "$response" | grep -q '"success":true'; then
        count=$(echo "$response" | grep -o '"id":[0-9]*' | wc -l)
        print_success "获取所有配置成功，共 $count 个配置"
        return 0
    else
        print_error "获取所有配置失败"
        echo "$response"
        return 1
    fi
}

# 测试按类型获取配置
test_get_configs_by_type() {
    local config_type="$1"
    print_test "获取 $config_type 类型配置..."
    
    response=$(curl -s "$API_URL/miniprogram/configs/active?type=$config_type")
    if echo "$response" | grep -q '"success":true'; then
        count=$(echo "$response" | grep -o '"id":[0-9]*' | wc -l)
        print_success "获取 $config_type 配置成功，共 $count 个配置"
        
        # 显示配置详情
        if [ "$count" -gt 0 ]; then
            echo "$response" | python3 -m json.tool 2>/dev/null | head -20 || echo "$response"
        fi
        return 0
    else
        print_error "获取 $config_type 配置失败"
        echo "$response"
        return 1
    fi
}

# 测试按分组获取配置
test_get_configs_by_group() {
    local group_name="$1"
    print_test "获取 $group_name 分组配置..."
    
    response=$(curl -s "$API_URL/miniprogram/configs/active?group=$group_name")
    if echo "$response" | grep -q '"success":true'; then
        count=$(echo "$response" | grep -o '"id":[0-9]*' | wc -l)
        print_success "获取 $group_name 分组配置成功，共 $count 个配置"
        return 0
    else
        print_error "获取 $group_name 分组配置失败"
        echo "$response"
        return 1
    fi
}

# 测试错误处理
test_error_handling() {
    print_test "测试错误处理..."
    
    # 测试无效的配置类型
    response=$(curl -s "$API_URL/miniprogram/configs/active?type=INVALID_TYPE")
    if echo "$response" | grep -q '"success":true'; then
        count=$(echo "$response" | grep -o '"id":[0-9]*' | wc -l)
        if [ "$count" -eq 0 ]; then
            print_success "无效类型处理正确，返回空结果"
        else
            print_error "无效类型处理异常，返回了 $count 个配置"
        fi
    else
        print_error "无效类型请求失败"
        echo "$response"
    fi
}

# 显示配置统计
show_config_statistics() {
    print_info "配置统计信息："
    
    # 统计各类型配置数量
    types=("CAROUSEL" "ANNOUNCEMENT" "CONTACT_PHONE" "WECHAT_COPY" "SHARE_CONFIG" "SYSTEM_CONFIG")
    
    for type in "${types[@]}"; do
        response=$(curl -s "$API_URL/miniprogram/configs/active?type=$type")
        if echo "$response" | grep -q '"success":true'; then
            count=$(echo "$response" | grep -o '"id":[0-9]*' | wc -l)
            echo "  📋 $type: $count 个配置"
        fi
    done
}

# 主测试函数
main() {
    print_header
    
    # 检查服务器是否运行
    if ! curl -s "$BASE_URL/health" > /dev/null; then
        print_error "服务器未运行，请先启动服务器："
        print_info "npm run dev"
        exit 1
    fi
    
    # 执行测试
    test_health || exit 1
    echo ""
    
    test_get_all_configs || exit 1
    echo ""
    
    # 测试各种配置类型
    test_get_configs_by_type "CAROUSEL"
    echo ""
    
    test_get_configs_by_type "ANNOUNCEMENT"
    echo ""
    
    test_get_configs_by_type "CONTACT_PHONE"
    echo ""
    
    test_get_configs_by_type "WECHAT_COPY"
    echo ""
    
    test_get_configs_by_type "SHARE_CONFIG"
    echo ""
    
    test_get_configs_by_type "SYSTEM_CONFIG"
    echo ""
    
    # 测试分组
    test_get_configs_by_group "home_carousel"
    echo ""
    
    test_get_configs_by_group "contact_info"
    echo ""
    
    # 测试错误处理
    test_error_handling
    echo ""
    
    # 显示统计信息
    show_config_statistics
    echo ""
    
    print_success "所有测试完成！"
    print_info "API接口工作正常，可以开始集成到小程序中"
    echo ""
    print_info "小程序集成示例："
    echo "  获取轮播图: GET $API_URL/miniprogram/configs/active?type=CAROUSEL"
    echo "  获取公告: GET $API_URL/miniprogram/configs/active?type=ANNOUNCEMENT"
    echo "  获取联系方式: GET $API_URL/miniprogram/configs/active?type=CONTACT_PHONE"
    echo "  获取微信号: GET $API_URL/miniprogram/configs/active?type=WECHAT_COPY"
    echo "  获取分享配置: GET $API_URL/miniprogram/configs/active?type=SHARE_CONFIG"
}

# 运行测试
main "$@"
