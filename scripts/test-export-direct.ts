import { ExportService } from '../src/services/exportService';
import { initializeDatabase } from '../src/config/database';
import logger from '../src/config/logger';

/**
 * 直接测试导出功能（不依赖服务器）
 */
async function testExportDirect() {
  try {
    // 初始化数据库
    await initializeDatabase();
    logger.info('Database initialized');

    // 获取用户统计
    logger.info('Getting user statistics...');
    const stats = await ExportService.getUserStats();
    
    console.log('\n=== 用户数据统计 ===');
    console.log(`总用户数: ${stats.total}`);
    console.log(`激活用户: ${stats.active}`);
    console.log(`未激活用户: ${stats.inactive}`);
    console.log(`有手机号: ${stats.withPhone}`);
    console.log(`绑定微信: ${stats.withWechat}`);
    console.log('角色分布:', stats.byRole);

    // 生成Excel文件（不发送邮件）
    console.log('\n=== 生成Excel文件 ===');
    const { UserModel } = await import('../src/models/User');
    const users = await UserModel.findAll();
    
    // 调用私有方法生成Excel（通过反射）
    const ExportServiceClass = ExportService as any;
    const filePath = await ExportServiceClass.generateUsersExcel(users);
    
    console.log(`Excel文件已生成: ${filePath}`);
    console.log('文件内容包含:');
    console.log(`- ${users.length} 个用户记录`);
    console.log('- 用户ID、手机号、角色、状态、微信信息、时间等字段');
    
    // 显示文件大小
    const fs = await import('fs');
    const stats_file = fs.statSync(filePath);
    console.log(`文件大小: ${(stats_file.size / 1024).toFixed(2)} KB`);

    console.log('\n=== 测试完成 ===');
    console.log('Excel文件已成功生成，可以手动查看文件内容。');
    console.log('如需测试邮件发送功能，请配置正确的SMTP设置。');

    // 清理文件
    fs.unlinkSync(filePath);
    console.log('临时文件已清理');

  } catch (error) {
    logger.error('Direct export test failed:', error);
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testExportDirect().then(() => {
    console.log('\n✅ 导出功能测试完成！');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
}

export { testExportDirect };
