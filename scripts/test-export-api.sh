#!/bin/bash

# 测试用户数据导出API
# 使用方法: ./scripts/test-export-api.sh

BASE_URL="http://localhost:3000/api/v1"

echo "=== 宝丰系统用户数据导出API测试 ==="
echo ""

# 1. 首先需要登录获取token（这里使用之前的token作为示例）
# 请替换为有效的JWT token
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5YjY2Y2FiNC1lZjY5LTRjY2QtYmJhYS1lOThmOTEyNzE3YTgiLCJyb2xlIjoidXNlciIsImlhdCI6MTc1MDUwMDEyMywiZXhwIjoxNzUwNTg2NTIzfQ.1H4w4dnfTWvbbET9g_LEqDU9jwVhX_oPySzmtc5nDq8"

echo "1. 测试获取用户统计信息..."
curl -s -X GET "${BASE_URL}/export/users/stats" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "2. 测试用户数据邮件导出..."
echo "请输入接收Excel文件的邮箱地址:"
read -p "邮箱: " EMAIL

if [ -n "$EMAIL" ]; then
  curl -s -X POST "${BASE_URL}/export/users" \
    -H "Authorization: Bearer ${JWT_TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"${EMAIL}\"}" | jq '.'
else
  echo "未输入邮箱地址，跳过邮件导出测试"
fi

echo ""
echo "3. 测试下载用户数据..."
curl -s -X GET "${BASE_URL}/export/users/download" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "=== 测试完成 ==="
echo ""
echo "注意事项："
echo "1. 确保服务器正在运行 (npm run dev)"
echo "2. 确保JWT token有效且具有管理员权限"
echo "3. 确保邮件配置正确（在.env文件中）"
echo "4. 如果需要测试邮件功能，请使用有效的邮箱地址"
