import mysql from 'mysql2/promise';
import config from '../src/config/index';
import logger from '../src/config/logger';

// 创建专门连接到 baofeng_recycle 数据库的连接
const recycleDbConfig = {
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: 'baofeng_recycle', // 使用 baofeng_recycle 数据库
  charset: config.database.charset,
  timezone: '+08:00',
};

const recyclePool = mysql.createPool(recycleDbConfig);

// 数据库查询函数
async function query(sql: string, params?: any[]): Promise<any> {
  const connection = await recyclePool.getConnection();
  try {
    const [rows] = await connection.execute(sql, params || []);
    return rows;
  } catch (error) {
    logger.error('Database query error:', { sql, params, error });
    throw error;
  } finally {
    connection.release();
  }
}

/**
 * 清理测试数据并插入真实门店数据
 */
async function updateStoreData() {
  try {
    logger.info('开始更新门店数据...');

    // 1. 查看当前数据
    logger.info('查看当前门店数据:');
    const currentStores = await query('SELECT * FROM recycle_stores WHERE status = 1');
    console.log('当前门店数据:', currentStores);

    // 2. 删除所有测试数据（软删除）
    logger.info('删除测试数据...');
    await query('UPDATE recycle_stores SET status = 0, updated_at = NOW() WHERE status = 1');
    
    // 3. 插入真实门店数据
    logger.info('插入真实门店数据...');
    const realStoreData = {
      storeCode: 'YC001',
      storeName: '宝丰回收运城盐湖店',
      province: '山西省',
      city: '运城市',
      district: '盐湖区',
      detailedAddress: '禹都大厦西5号',
      fullAddress: '山西省运城市盐湖区禹都大厦西5号',
      salesPhone: '***********',
      salesWechat: '***********',
      recyclePhone: '***********',
      recycleWechat: '***********',
      mainPhone: '***********',
      businessStatus: 'OPEN',
      storeType: 'STANDARD',
      serviceRadius: 10
    };

    const insertSql = `
      INSERT INTO recycle_stores (
        store_code, store_name, province, city, district, detailed_address, full_address,
        sales_phone, sales_wechat, recycle_phone, recycle_wechat, main_phone,
        business_status, store_type, service_radius, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;

    const values = [
      realStoreData.storeCode,
      realStoreData.storeName,
      realStoreData.province,
      realStoreData.city,
      realStoreData.district,
      realStoreData.detailedAddress,
      realStoreData.fullAddress,
      realStoreData.salesPhone,
      realStoreData.salesWechat,
      realStoreData.recyclePhone,
      realStoreData.recycleWechat,
      realStoreData.mainPhone,
      realStoreData.businessStatus,
      realStoreData.storeType,
      realStoreData.serviceRadius
    ];

    const result = await query(insertSql, values);
    const insertId = (result as any).insertId;

    logger.info(`真实门店数据插入成功，ID: ${insertId}`);

    // 4. 验证插入结果
    const newStores = await query('SELECT * FROM recycle_stores WHERE status = 1');
    console.log('更新后的门店数据:', newStores);

    logger.info('门店数据更新完成！');
    
    return {
      success: true,
      message: '门店数据更新成功',
      insertedStoreId: insertId,
      storeData: realStoreData
    };

  } catch (error) {
    logger.error('更新门店数据失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接池
    await recyclePool.end();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  updateStoreData()
    .then((result) => {
      console.log('✅ 更新成功:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 更新失败:', error);
      process.exit(1);
    });
}

export { updateStoreData };
