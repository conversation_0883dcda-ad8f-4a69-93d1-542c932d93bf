#!/bin/bash

# 微信小程序配置系统初始化脚本
# 用于创建数据库表结构和初始化示例数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"3306"}
DB_USER=${DB_USER:-"root"}
DB_PASSWORD=${DB_PASSWORD:-"baofeng123456"}
DB_NAME=${DB_NAME:-"baofeng_admin"}

# 函数定义
print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  微信小程序配置系统初始化脚本"
    echo "=================================================="
    echo -e "${NC}"
}

print_step() {
    echo -e "${YELLOW}[步骤] $1${NC}"
}

print_success() {
    echo -e "${GREEN}[成功] $1${NC}"
}

print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

print_info() {
    echo -e "${BLUE}[信息] $1${NC}"
}

# 检查MySQL连接
check_mysql_connection() {
    print_step "检查MySQL连接..."
    
    if ! command -v mysql &> /dev/null; then
        print_error "MySQL客户端未安装，请先安装MySQL"
        exit 1
    fi
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null; then
        print_success "MySQL连接正常"
    else
        print_error "无法连接到MySQL数据库"
        print_info "请检查以下配置："
        print_info "  主机: $DB_HOST"
        print_info "  端口: $DB_PORT"
        print_info "  用户: $DB_USER"
        print_info "  密码: [已隐藏]"
        exit 1
    fi
}

# 检查数据库是否存在
check_database_exists() {
    print_step "检查数据库是否存在..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" &> /dev/null; then
        print_success "数据库 $DB_NAME 已存在"
        return 0
    else
        print_info "数据库 $DB_NAME 不存在，将创建新数据库"
        return 1
    fi
}

# 创建数据库
create_database() {
    print_step "创建数据库 $DB_NAME..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    if [ $? -eq 0 ]; then
        print_success "数据库创建成功"
    else
        print_error "数据库创建失败"
        exit 1
    fi
}

# 执行SQL脚本
execute_sql_script() {
    local script_path="$1"
    local description="$2"
    
    print_step "$description..."
    
    if [ ! -f "$script_path" ]; then
        print_error "SQL脚本文件不存在: $script_path"
        exit 1
    fi
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$script_path"
    
    if [ $? -eq 0 ]; then
        print_success "$description 完成"
    else
        print_error "$description 失败"
        exit 1
    fi
}

# 检查表是否存在
check_table_exists() {
    local table_name="$1"
    
    local count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$DB_NAME' AND table_name = '$table_name';" -s -N)
    
    if [ "$count" -eq 1 ]; then
        return 0
    else
        return 1
    fi
}

# 验证安装
verify_installation() {
    print_step "验证安装结果..."
    
    local tables=("miniprogram_configs" "miniprogram_config_logs")
    local all_tables_exist=true
    
    for table in "${tables[@]}"; do
        if check_table_exists "$table"; then
            print_success "表 $table 创建成功"
        else
            print_error "表 $table 不存在"
            all_tables_exist=false
        fi
    done
    
    if [ "$all_tables_exist" = true ]; then
        # 检查示例数据
        local config_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) FROM miniprogram_configs;" -s -N 2>/dev/null || echo "0")
        print_info "当前配置数量: $config_count"
        
        print_success "微信小程序配置系统安装完成！"
        return 0
    else
        print_error "安装验证失败"
        return 1
    fi
}

# 显示使用说明
show_usage_info() {
    print_info "系统已安装完成，您可以："
    echo ""
    echo "1. 启动应用服务器："
    echo "   npm run dev"
    echo ""
    echo "2. 访问API文档："
    echo "   http://localhost:3000/api-docs"
    echo ""
    echo "3. 测试配置系统："
    echo "   node scripts/test-miniprogram-config.js"
    echo ""
    echo "4. 查看配置管理界面："
    echo "   GET /api/v1/miniprogram/configs"
    echo ""
    echo "5. 小程序获取配置："
    echo "   GET /api/v1/miniprogram/configs/active"
    echo ""
}

# 主函数
main() {
    print_header
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
    SQL_SCRIPT="$PROJECT_ROOT/docs/database/miniprogram-config-schema.sql"
    
    print_info "项目根目录: $PROJECT_ROOT"
    print_info "SQL脚本路径: $SQL_SCRIPT"
    print_info "数据库配置:"
    print_info "  主机: $DB_HOST:$DB_PORT"
    print_info "  数据库: $DB_NAME"
    print_info "  用户: $DB_USER"
    echo ""
    
    # 检查MySQL连接
    check_mysql_connection
    
    # 检查并创建数据库
    if ! check_database_exists; then
        create_database
    fi
    
    # 执行SQL脚本
    execute_sql_script "$SQL_SCRIPT" "创建表结构和初始化数据"
    
    # 验证安装
    if verify_installation; then
        echo ""
        show_usage_info
    else
        exit 1
    fi
}

# 帮助信息
show_help() {
    echo "微信小程序配置系统初始化脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -H, --host HOST         MySQL主机地址 (默认: localhost)"
    echo "  -P, --port PORT         MySQL端口 (默认: 3306)"
    echo "  -u, --user USER         MySQL用户名 (默认: root)"
    echo "  -p, --password PASS     MySQL密码 (默认: baofeng123456)"
    echo "  -d, --database DB       数据库名称 (默认: baofeng_admin)"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST                 MySQL主机地址"
    echo "  DB_PORT                 MySQL端口"
    echo "  DB_USER                 MySQL用户名"
    echo "  DB_PASSWORD             MySQL密码"
    echo "  DB_NAME                 数据库名称"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 -H ************* -u admin -p mypassword"
    echo "  DB_HOST=************* DB_USER=admin $0"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -H|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -P|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -p|--password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        -d|--database)
            DB_NAME="$2"
            shift 2
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主函数
main
