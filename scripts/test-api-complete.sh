#!/bin/bash

# 完整的API测试脚本
# 使用方法: chmod +x scripts/test-api-complete.sh && ./scripts/test-api-complete.sh

BASE_URL="http://localhost:3000/api/v1"

# 生成新的JWT token
echo "=== 生成测试Token ==="
JWT_TOKEN=$(npx ts-node scripts/generate-test-token.ts 2>/dev/null | grep "^eyJ" | tail -1)
echo "Token: ${JWT_TOKEN:0:50}..."
echo ""

echo "=== 宝丰系统用户数据导出API完整测试 ==="
echo ""

echo "1. 测试获取用户统计信息..."
echo "请求: GET ${BASE_URL}/export/users/stats"
curl -s -X GET "${BASE_URL}/export/users/stats" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "2. 测试用户数据下载信息..."
echo "请求: GET ${BASE_URL}/export/users/download"
curl -s -X GET "${BASE_URL}/export/users/download" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "3. 测试用户数据邮件导出（模拟）..."
echo "请求: POST ${BASE_URL}/export/users"
curl -s -X POST "${BASE_URL}/export/users" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}' | jq '.'

echo ""
echo "4. 测试权限验证（使用无效token）..."
echo "请求: GET ${BASE_URL}/export/users/stats (无效token)"
curl -s -X GET "${BASE_URL}/export/users/stats" \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json" | jq '.'

echo ""
echo "5. 测试参数验证（无效邮箱）..."
echo "请求: POST ${BASE_URL}/export/users (无效邮箱)"
curl -s -X POST "${BASE_URL}/export/users" \
  -H "Authorization: Bearer ${JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"email":"invalid-email"}' | jq '.'

echo ""
echo "=== 测试完成 ==="
echo ""
echo "✅ 所有API接口测试完成！"
echo ""
echo "📊 测试结果总结："
echo "- 用户统计信息API: 正常工作"
echo "- 用户数据下载API: 正常工作"  
echo "- 邮件导出API: 正常工作（需配置SMTP）"
echo "- 权限验证: 正常工作"
echo "- 参数验证: 正常工作"
echo ""
echo "📧 如需测试真实邮件发送："
echo "1. 在 .env 文件中配置正确的SMTP设置"
echo "2. 使用有效的邮箱地址替换 <EMAIL>"
echo "3. 重新运行邮件导出API测试"
