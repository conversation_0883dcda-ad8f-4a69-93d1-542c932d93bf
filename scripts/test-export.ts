import { UserModel } from '../src/models/User';
import { ExportService } from '../src/services/exportService';
import { UserRole } from '../src/types';
import { initializeDatabase } from '../src/config/database';
import logger from '../src/config/logger';

/**
 * 测试数据导出功能
 */
async function testExport() {
  try {
    // 初始化数据库
    await initializeDatabase();
    logger.info('Database initialized');

    // 创建测试用户数据
    const testUsers = [
      {
        phone: '13800138001',
        role: UserRole.ADMIN,
        wechatOpenId: 'test_openid_1',
        wechatUnionId: 'test_unionid_1',
        wechatSessionKey: 'test_session_1',
      },
      {
        phone: '13800138002',
        role: UserRole.USER,
        wechatOpenId: 'test_openid_2',
        wechatUnionId: 'test_unionid_2',
        wechatSessionKey: 'test_session_2',
      },
      {
        phone: '13800138003',
        role: UserRole.USER,
        wechatOpenId: 'test_openid_3',
        avatar: 'https://example.com/avatar3.jpg',
      },
      {
        role: UserRole.USER,
        wechatOpenId: 'test_openid_4',
        // 没有手机号的用户
      },
    ];

    // 创建测试用户
    logger.info('Creating test users...');
    for (const userData of testUsers) {
      try {
        const user = await UserModel.create(userData);
        logger.info(`Created user: ${user.id}`);
      } catch (error) {
        logger.warn(`Failed to create user with phone ${userData.phone}:`, error);
      }
    }

    // 获取用户统计
    logger.info('Getting user statistics...');
    const stats = await ExportService.getUserStats();
    logger.info('User statistics:', stats);

    // 测试邮件导出（需要配置有效的邮箱）
    const testEmail = '<EMAIL>'; // 请替换为有效的邮箱地址
    
    console.log('\n=== 用户数据统计 ===');
    console.log(`总用户数: ${stats.total}`);
    console.log(`激活用户: ${stats.active}`);
    console.log(`未激活用户: ${stats.inactive}`);
    console.log(`有手机号: ${stats.withPhone}`);
    console.log(`绑定微信: ${stats.withWechat}`);
    console.log('角色分布:', stats.byRole);

    console.log('\n=== 导出功能测试 ===');
    console.log('如需测试邮件导出功能，请：');
    console.log('1. 在 .env 文件中配置正确的邮件服务器信息');
    console.log('2. 将下面的邮箱地址替换为有效邮箱');
    console.log('3. 取消注释下面的代码');
    
    // 取消注释下面的代码来测试邮件导出
    // logger.info(`Testing email export to: ${testEmail}`);
    // await ExportService.exportUsersToExcelAndEmail(testEmail);
    // logger.info('Email export completed');

    logger.info('Test completed successfully');

  } catch (error) {
    logger.error('Test failed:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testExport().then(() => {
    console.log('\n测试完成！');
    process.exit(0);
  }).catch((error) => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

export { testExport };
