import { generateToken } from '../src/utils/jwt';
import { JwtPayload, UserRole } from '../src/types';

/**
 * 生成测试用的JWT token
 */
function generateTestToken() {
  // 使用我们创建的管理员用户
  const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
    userId: 'e0738c13-9c5a-4be4-a732-1adb1afdac09', // 管理员用户ID
    phone: '13800138001',
    role: UserRole.ADMIN,
  };

  const token = generateToken(payload);
  
  console.log('=== 测试Token生成 ===');
  console.log('用户ID:', payload.userId);
  console.log('手机号:', payload.phone);
  console.log('角色:', payload.role);
  console.log('');
  console.log('JWT Token:');
  console.log(token);
  console.log('');
  console.log('=== 使用方法 ===');
  console.log('将上面的token复制，然后在API请求中使用：');
  console.log('Authorization: Bearer ' + token);
  
  return token;
}

// 运行脚本
if (require.main === module) {
  generateTestToken();
}

export { generateTestToken };
