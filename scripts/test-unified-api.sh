#!/bin/bash

# 微信小程序统一配置API测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:3000"
API_URL="$BASE_URL/api/v1"

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  微信小程序统一配置API测试"
    echo "=================================================="
    echo -e "${NC}"
}

print_test() {
    echo -e "${YELLOW}[测试] $1${NC}"
}

print_success() {
    echo -e "${GREEN}[成功] $1${NC}"
}

print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

print_info() {
    echo -e "${BLUE}[信息] $1${NC}"
}

# 测试统一接口
test_unified_api() {
    print_test "测试统一配置接口..."
    
    response=$(curl -s "$API_URL/miniprogram/configs/all")
    if echo "$response" | grep -q '"success":true'; then
        print_success "统一接口调用成功"
        
        # 统计各类型配置数量
        carousel_count=$(echo "$response" | grep -o '"CAROUSEL":\[' | wc -l)
        announcement_count=$(echo "$response" | grep -o '"ANNOUNCEMENT":\[' | wc -l)
        contact_count=$(echo "$response" | grep -o '"CONTACT_PHONE":\[' | wc -l)
        wechat_count=$(echo "$response" | grep -o '"WECHAT_COPY":\[' | wc -l)
        share_count=$(echo "$response" | grep -o '"SHARE_CONFIG":\[' | wc -l)
        system_count=$(echo "$response" | grep -o '"SYSTEM_CONFIG":\[' | wc -l)
        
        print_info "配置统计："
        echo "  🎠 轮播图配置: $(echo "$response" | grep -o '"type":"CAROUSEL"' | wc -l) 个"
        echo "  📢 公告配置: $(echo "$response" | grep -o '"type":"ANNOUNCEMENT"' | wc -l) 个"
        echo "  📞 电话配置: $(echo "$response" | grep -o '"type":"CONTACT_PHONE"' | wc -l) 个"
        echo "  💬 微信配置: $(echo "$response" | grep -o '"type":"WECHAT_COPY"' | wc -l) 个"
        echo "  📤 分享配置: $(echo "$response" | grep -o '"type":"SHARE_CONFIG"' | wc -l) 个"
        echo "  ⚙️  系统配置: $(echo "$response" | grep -o '"type":"SYSTEM_CONFIG"' | wc -l) 个"
        
        return 0
    else
        print_error "统一接口调用失败"
        echo "$response"
        return 1
    fi
}

# 显示配置详情
show_config_details() {
    print_test "获取配置详情..."
    
    response=$(curl -s "$API_URL/miniprogram/configs/all")
    
    print_info "轮播图配置："
    echo "$response" | python3 -c "
import sys, json
data = json.load(sys.stdin)
if 'data' in data and 'CAROUSEL' in data['data']:
    for config in data['data']['CAROUSEL']:
        print(f\"  - {config['name']}: {config['value']['title']}\")
" 2>/dev/null || echo "  解析失败"

    print_info "公告配置："
    echo "$response" | python3 -c "
import sys, json
data = json.load(sys.stdin)
if 'data' in data and 'ANNOUNCEMENT' in data['data']:
    for config in data['data']['ANNOUNCEMENT']:
        print(f\"  - {config['name']}: {config['value']['title']}\")
" 2>/dev/null || echo "  解析失败"

    print_info "联系方式配置："
    echo "$response" | python3 -c "
import sys, json
data = json.load(sys.stdin)
if 'data' in data and 'CONTACT_PHONE' in data['data']:
    for config in data['data']['CONTACT_PHONE']:
        print(f\"  - {config['name']}: {config['value']['phone_number']}\")
" 2>/dev/null || echo "  解析失败"

    print_info "微信配置："
    echo "$response" | python3 -c "
import sys, json
data = json.load(sys.stdin)
if 'data' in data and 'WECHAT_COPY' in data['data']:
    for config in data['data']['WECHAT_COPY']:
        print(f\"  - {config['name']}: {config['value']['wechat_id']}\")
" 2>/dev/null || echo "  解析失败"
}

# 测试小程序集成示例
show_miniprogram_integration() {
    print_info "小程序集成示例："
    echo ""
    echo "// 小程序中一次性获取所有配置"
    echo "wx.request({"
    echo "  url: '$API_URL/miniprogram/configs/all',"
    echo "  method: 'GET',"
    echo "  success: (res) => {"
    echo "    if (res.data.success) {"
    echo "      const configs = res.data.data;"
    echo "      "
    echo "      // 设置轮播图"
    echo "      this.setData({"
    echo "        carouselList: configs.CAROUSEL || []"
    echo "      });"
    echo "      "
    echo "      // 设置公告"
    echo "      if (configs.ANNOUNCEMENT && configs.ANNOUNCEMENT.length > 0) {"
    echo "        this.setData({"
    echo "          announcement: configs.ANNOUNCEMENT[0].value"
    echo "        });"
    echo "      }"
    echo "      "
    echo "      // 设置联系方式"
    echo "      this.setData({"
    echo "        contactInfo: configs.CONTACT_PHONE || [],"
    echo "        wechatInfo: configs.WECHAT_COPY || [],"
    echo "        shareConfig: configs.SHARE_CONFIG?.[0]?.value || {}"
    echo "      });"
    echo "      "
    echo "      // 设置系统配置"
    echo "      if (configs.SYSTEM_CONFIG) {"
    echo "        const appInfo = configs.SYSTEM_CONFIG.find(c => c.key === 'app_info');"
    echo "        const features = configs.SYSTEM_CONFIG.find(c => c.key === 'feature_switches');"
    echo "        this.setData({"
    echo "          appInfo: appInfo?.value || {},"
    echo "          features: features?.value || {}"
    echo "        });"
    echo "      }"
    echo "    }"
    echo "  }"
    echo "});"
}

# 性能测试
test_performance() {
    print_test "性能测试..."
    
    start_time=$(date +%s%N)
    response=$(curl -s "$API_URL/miniprogram/configs/all")
    end_time=$(date +%s%N)
    
    duration=$(( (end_time - start_time) / 1000000 ))
    
    if echo "$response" | grep -q '"success":true'; then
        print_success "性能测试通过，响应时间: ${duration}ms"
        
        # 计算数据大小
        data_size=$(echo "$response" | wc -c)
        print_info "响应数据大小: ${data_size} 字节"
        
        if [ $duration -lt 500 ]; then
            print_success "响应时间优秀 (< 500ms)"
        elif [ $duration -lt 1000 ]; then
            print_info "响应时间良好 (< 1s)"
        else
            print_error "响应时间较慢 (> 1s)"
        fi
    else
        print_error "性能测试失败"
    fi
}

# 主测试函数
main() {
    print_header
    
    # 检查服务器是否运行
    if ! curl -s "$BASE_URL/health" > /dev/null; then
        print_error "服务器未运行，请先启动服务器："
        print_info "npm run dev"
        exit 1
    fi
    
    # 执行测试
    test_unified_api || exit 1
    echo ""
    
    show_config_details
    echo ""
    
    test_performance
    echo ""
    
    show_miniprogram_integration
    echo ""
    
    print_success "统一配置API测试完成！"
    print_info "现在小程序只需要调用一个接口就能获取所有配置："
    print_info "GET $API_URL/miniprogram/configs/all"
    echo ""
    print_info "优势："
    echo "  ✅ 减少网络请求次数"
    echo "  ✅ 提高加载性能"
    echo "  ✅ 简化前端代码"
    echo "  ✅ 数据结构清晰"
    echo "  ✅ 按类型自动分组"
}

# 运行测试
main "$@"
