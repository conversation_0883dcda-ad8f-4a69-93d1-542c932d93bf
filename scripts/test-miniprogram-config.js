#!/usr/bin/env node

/**
 * 微信小程序配置系统测试脚本
 * 用于测试配置的创建、查询、更新等功能
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000/api/v1';
const TEST_TOKEN = 'your-test-token-here'; // 需要替换为实际的测试token

// API客户端
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${TEST_TOKEN}`
  }
});

// 测试数据
const testConfigs = [
  {
    config_type: 'CAROUSEL',
    config_key: 'test_carousel_1',
    config_name: '测试轮播图1',
    config_value: {
      image_url: 'https://example.com/test1.jpg',
      title: '测试轮播图标题',
      subtitle: '测试轮播图副标题',
      link_type: 'page',
      link_value: '/pages/test/index',
      background_color: '#FF6B6B'
    },
    display_order: 1,
    group_name: 'test_carousel',
    description: '测试用轮播图配置'
  },
  {
    config_type: 'ANNOUNCEMENT',
    config_key: 'test_announcement_1',
    config_name: '测试公告1',
    config_value: {
      title: '测试公告标题',
      content: '这是一个测试公告的内容，用于验证公告功能是否正常工作。',
      type: 'info',
      show_icon: true,
      closable: true,
      auto_close_time: 5000
    },
    display_order: 1,
    group_name: 'test_announcement',
    description: '测试用公告配置'
  },
  {
    config_type: 'CONTACT_PHONE',
    config_key: 'test_phone_1',
    config_name: '测试客服电话',
    config_value: {
      phone_number: '************',
      display_name: '测试客服热线',
      service_time: '9:00-18:00',
      description: '测试客服电话描述',
      show_confirm: true,
      confirm_text: '确定要拨打测试客服电话吗？'
    },
    display_order: 1,
    group_name: 'test_contact',
    description: '测试用客服电话配置'
  },
  {
    config_type: 'WECHAT_COPY',
    config_key: 'test_wechat_1',
    config_name: '测试微信号',
    config_value: {
      wechat_id: 'test_wechat_123',
      display_name: '测试客服微信',
      qr_code_url: 'https://example.com/test_qr.jpg',
      description: '测试微信号描述',
      copy_success_text: '测试微信号已复制',
      show_qr_code: true
    },
    display_order: 1,
    group_name: 'test_wechat',
    description: '测试用微信号配置'
  },
  {
    config_type: 'SHARE_CONFIG',
    config_key: 'test_share_1',
    config_name: '测试分享配置',
    config_value: {
      title: '测试分享标题',
      description: '测试分享描述内容',
      image_url: 'https://example.com/test_share.jpg',
      share_path: '/pages/test/share',
      enable_timeline: true,
      enable_session: true,
      enable_qq: false
    },
    display_order: 1,
    group_name: 'test_share',
    description: '测试用分享配置'
  }
];

// 测试函数
async function testHealthCheck() {
  console.log('\n🏥 测试健康检查...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查通过:', response.data.message);
    return true;
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message);
    return false;
  }
}

async function testCreateConfigs() {
  console.log('\n📝 测试创建配置...');
  const createdConfigs = [];
  
  for (const config of testConfigs) {
    try {
      const response = await api.post('/miniprogram/configs', config);
      console.log(`✅ 创建配置成功: ${config.config_name} (ID: ${response.data.data.id})`);
      createdConfigs.push(response.data.data);
    } catch (error) {
      console.error(`❌ 创建配置失败: ${config.config_name}`, error.response?.data?.message || error.message);
    }
  }
  
  return createdConfigs;
}

async function testGetActiveConfigs() {
  console.log('\n🔍 测试获取有效配置...');
  try {
    const response = await axios.get(`${BASE_URL}/miniprogram/configs/active`);
    console.log(`✅ 获取有效配置成功，共 ${response.data.data.length} 个配置`);
    
    // 按类型分组显示
    const configsByType = response.data.data.reduce((acc, config) => {
      if (!acc[config.type]) acc[config.type] = [];
      acc[config.type].push(config);
      return acc;
    }, {});
    
    Object.entries(configsByType).forEach(([type, configs]) => {
      console.log(`  📋 ${type}: ${configs.length} 个配置`);
    });
    
    return response.data.data;
  } catch (error) {
    console.error('❌ 获取有效配置失败:', error.response?.data?.message || error.message);
    return [];
  }
}

async function testGetConfigsByType() {
  console.log('\n🏷️ 测试按类型获取配置...');
  const types = ['CAROUSEL', 'ANNOUNCEMENT', 'CONTACT_PHONE', 'WECHAT_COPY', 'SHARE_CONFIG'];
  
  for (const type of types) {
    try {
      const response = await api.get(`/miniprogram/configs/type/${type}`);
      console.log(`✅ 获取 ${type} 配置成功，共 ${response.data.data.length} 个`);
    } catch (error) {
      console.error(`❌ 获取 ${type} 配置失败:`, error.response?.data?.message || error.message);
    }
  }
}

async function testUpdateConfig(configId) {
  console.log('\n✏️ 测试更新配置...');
  try {
    const updateData = {
      description: '已更新的测试配置描述',
      is_enabled: false
    };
    
    const response = await api.put(`/miniprogram/configs/${configId}`, updateData);
    console.log(`✅ 更新配置成功: ID ${configId}`);
    return response.data.data;
  } catch (error) {
    console.error(`❌ 更新配置失败: ID ${configId}`, error.response?.data?.message || error.message);
    return null;
  }
}

async function testBatchUpdate(configIds) {
  console.log('\n📦 测试批量更新配置...');
  try {
    const batchData = {
      config_ids: configIds.slice(0, 3), // 只更新前3个
      updates: {
        is_enabled: true,
        group_name: 'batch_updated'
      },
      operation_reason: '批量测试更新'
    };
    
    const response = await api.post('/miniprogram/configs/batch', batchData);
    console.log(`✅ 批量更新成功，影响 ${response.data.data.updated_count} 个配置`);
    return response.data.data;
  } catch (error) {
    console.error('❌ 批量更新失败:', error.response?.data?.message || error.message);
    return null;
  }
}

async function testGetStatistics() {
  console.log('\n📊 测试获取统计信息...');
  try {
    const response = await api.get('/miniprogram/configs/statistics');
    const stats = response.data.data;
    
    console.log('✅ 获取统计信息成功:');
    console.log(`  📈 总配置数: ${stats.total_configs}`);
    console.log(`  ✅ 启用配置: ${stats.enabled_configs}`);
    console.log(`  ❌ 禁用配置: ${stats.disabled_configs}`);
    console.log(`  🕒 最近更新: ${stats.recent_updates}`);
    
    return stats;
  } catch (error) {
    console.error('❌ 获取统计信息失败:', error.response?.data?.message || error.message);
    return null;
  }
}

async function testDeleteConfig(configId) {
  console.log('\n🗑️ 测试删除配置...');
  try {
    await api.delete(`/miniprogram/configs/${configId}`);
    console.log(`✅ 删除配置成功: ID ${configId}`);
    return true;
  } catch (error) {
    console.error(`❌ 删除配置失败: ID ${configId}`, error.response?.data?.message || error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始微信小程序配置系统测试...\n');
  
  // 1. 健康检查
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ 服务不可用，终止测试');
    return;
  }
  
  // 2. 创建测试配置
  const createdConfigs = await testCreateConfigs();
  if (createdConfigs.length === 0) {
    console.log('❌ 无法创建测试配置，终止测试');
    return;
  }
  
  const configIds = createdConfigs.map(config => config.id);
  
  // 3. 获取有效配置
  await testGetActiveConfigs();
  
  // 4. 按类型获取配置
  await testGetConfigsByType();
  
  // 5. 更新配置
  if (configIds.length > 0) {
    await testUpdateConfig(configIds[0]);
  }
  
  // 6. 批量更新
  if (configIds.length > 2) {
    await testBatchUpdate(configIds);
  }
  
  // 7. 获取统计信息
  await testGetStatistics();
  
  // 8. 清理测试数据
  console.log('\n🧹 清理测试数据...');
  for (const configId of configIds) {
    await testDeleteConfig(configId);
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 测试过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testHealthCheck,
  testCreateConfigs,
  testGetActiveConfigs
};
