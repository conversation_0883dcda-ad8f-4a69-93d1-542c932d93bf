# 宝丰后台管理系统

基于 Node.js、TypeScript 和 MySQL 构建的综合后台管理系统，支持用户管理、类目管理和价格涨跌榜功能。

## 🚀 核心功能

- **双端认证系统**: C端用户和B端管理员独立认证
- **用户管理**: 完整的用户CRUD操作和权限控制
- **类目管理**: 品牌-型号-子型号三级分类体系
- **价格涨跌榜**: 自动计算和展示价格趋势排行榜
- **RESTful API**: 标准化API接口设计
- **数据库集成**: MySQL + Knex.js 查询构建器
- **输入验证**: 基于Joi的请求参数验证
- **错误处理**: 统一错误处理和日志记录
- **API文档**: Swagger自动生成文档
- **安全防护**: CORS、限流和安全头设置

## 📋 系统要求

- Node.js (v18+)
- MySQL (v8.0+)
- npm 或 yarn

## 🛠️ 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 环境配置
```bash
cp .env.example .env
# 编辑 .env 文件配置数据库连接等信息
```

### 3. 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE baofeng_recycle;"

# 导入表结构
mysql -u root -p baofeng_recycle < dataBase.md
```

### 4. 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm run build && npm start
```

## 📚 API文档和访问地址

- **API文档**: http://localhost:3000/api-docs
- **健康检查**: http://localhost:3000/health
- **基础URL**: http://localhost:3000/api/v1

## 🏗️ 项目结构

```
src/
├── config/          # 配置文件 (数据库、JWT、Swagger等)
├── controllers/     # 控制器 (业务逻辑处理)
├── middleware/      # 中间件 (认证、验证、错误处理)
├── routes/          # 路由定义
├── scripts/         # 脚本文件 (定时任务等)
├── utils/           # 工具函数
├── types/           # TypeScript类型定义
└── tests/           # 测试文件
```

## 🔧 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm start` - 启动生产服务器
- `npm test` - 运行测试
- `npm run calculate-rankings` - 手动计算价格涨跌榜

## 🔐 认证说明

系统使用JWT Token进行认证，请在请求头中包含：

```
Authorization: Bearer <your-jwt-token>
```

### 权限级别
- **user**: 普通用户
- **admin**: 管理员
- **super_admin**: 超级管理员

## 📖 主要API接口

### C端用户认证
- `POST /auth/register` - 用户注册
- `POST /auth/login` - 用户登录
- `POST /auth/logout` - 用户登出
- `GET /auth/profile` - 获取用户资料

### B端管理员认证
- `POST /admin/auth/login` - 管理员登录
- `GET /admin/auth/profile` - 获取管理员资料
- `POST /admin/auth/create` - 创建管理员

### 类目管理
- `GET /categories/tree` - 获取类目树
- `GET /categories/brands` - 获取品牌列表
- `GET /categories/{categoryId}/brands` - 获取品牌树结构
- `GET /categories/{table}/{id}/price` - 获取价格信息

### 价格涨跌榜
- `GET /categories/price-rankings` - 获取涨跌榜
- `GET /categories/price-rankings/stats` - 获取统计数据
- `POST /categories/price-rankings/calculate` - 手动触发计算

完整API文档请访问 `/api-docs` 或查看 `API_COMPLETE_DOCUMENTATION.md`

## 🗄️ 数据库设计

### 核心表结构
- **phone_categories**: 分层类目表 (品牌/型号/子型号)
- **memory_specs**: 内存规格表
- **price_tags**: 价格标签表
- **price_trend_rankings**: 涨跌榜表
- **operation_logs**: 操作日志表

### 分类层级
1. **Level 1**: 品牌 (苹果、华为、小米等)
2. **Level 2**: 型号 (iPhone 15、Mate 60等)
3. **Level 3**: 子型号 (iPhone 15 Pro Max等)

## 💰 价格涨跌榜功能

### 自动计算
- 每天凌晨2点自动计算涨跌榜
- 支持涨幅榜和跌幅榜
- 提供详细的统计数据

### 定时任务设置
```bash
# 添加到 crontab
0 2 * * * cd /path/to/baofeng-rd && npm run calculate-rankings:prod >> /var/log/price-rankings.log 2>&1
```

### 手动执行
```bash
npm run calculate-rankings
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

## 🚀 部署

### 生产环境部署
1. **构建应用**
   ```bash
   npm run build
   ```

2. **设置环境变量**
   ```bash
   export NODE_ENV=production
   export DB_HOST=your-db-host
   export DB_USER=your-db-user
   export DB_PASSWORD=your-db-password
   ```

3. **启动服务**
   ```bash
   npm start
   ```

### 使用PM2部署
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start dist/index.js --name baofeng-api

# 设置开机自启
pm2 startup
pm2 save
```

## 📝 环境变量配置

```env
NODE_ENV=development
PORT=3000
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=baofeng123456
DB_NAME=baofeng_recycle
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-secret
REFRESH_TOKEN_EXPIRES_IN=30d
```

## 🔍 使用示例

### 获取涨幅榜
```bash
curl "http://localhost:3000/api/v1/categories/price-rankings?type=RISE_RANKING&limit=10"
```

### 管理员登录
```bash
curl -X POST "http://localhost:3000/api/v1/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123456"}'
```

### 获取类目树
```bash
curl "http://localhost:3000/api/v1/categories/tree"
```

## 📄 文档说明

- `API_COMPLETE_DOCUMENTATION.md` - 完整API接口文档
- `dataBase.md` - 数据库表结构设计
- `src/routes/README.md` - 路由配置详细说明
- `src/controllers/README.md` - 控制器功能说明

## 🆘 技术支持

如有问题或需要支持，请联系开发团队或在项目中创建Issue。
