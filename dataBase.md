-- 重新设计的数据库表结构
-- 基于易站回收API数据结构优化设计
-- 移除不必要的full_path字段，优化数据关系

USE baofeng_recycle;

-- 1. 手机分类表（统一存储所有层级的分类数据）
CREATE TABLE phone_categories (
    id INT PRIMARY KEY COMMENT '分类ID，直接使用API返回的id',
    parent_id INT DEFAULT 0 COMMENT '父级ID',
    level TINYINT NOT NULL COMMENT '分类级别：0-根分类，1-品牌，2-型号，3-子型号',
    name VARCHAR(200) NOT NULL COMMENT '分类名称',
    sort_index INT DEFAULT 0 COMMENT '排序索引',
    remake TEXT COMMENT '备注信息',
    wechat_tag JSON COMMENT '微信标签信息',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_sort_index (sort_index)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='手机分类表';

-- 2. 内存规格表（存储sub_model数据）
CREATE TABLE memory_specs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID，关联phone_categories表的level=3记录',
    memory_id INT NOT NULL COMMENT '内存规格ID，来自API的sub_model_id',
    memory_size VARCHAR(50) NOT NULL COMMENT '内存大小，如：16G, 64G',
    base_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '基础价格',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES phone_categories(id) ON DELETE CASCADE,
    UNIQUE KEY uk_category_memory (category_id, memory_id),
    INDEX idx_category_id (category_id),
    INDEX idx_memory_id (memory_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内存规格表';

-- 3. 价格标签表（存储tags数据）
CREATE TABLE price_tags (
    id INT PRIMARY KEY COMMENT '价格标签ID，直接使用API返回的id',
    memory_spec_id INT NOT NULL COMMENT '内存规格ID',
    sub_model_memory_id INT NOT NULL COMMENT '对应的sub_model中的memory_id',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称，如：靓机、小花、大花',
    group_name VARCHAR(100) NOT NULL COMMENT '分组名称，如：成色',
    price_rate DECIMAL(10,2) NOT NULL COMMENT '价格（单位：元）',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (memory_spec_id) REFERENCES memory_specs(id) ON DELETE CASCADE,
    INDEX idx_memory_spec_id (memory_spec_id),
    INDEX idx_sub_model_memory_id (sub_model_memory_id),
    INDEX idx_group_name (group_name),
    INDEX idx_status (status),
    INDEX idx_price_rate (price_rate)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格标签表';

-- 4. 价格历史记录表
CREATE TABLE price_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tag_id INT NOT NULL COMMENT '价格标签ID',
    old_price DECIMAL(10,2) COMMENT '原价格',
    new_price DECIMAL(10,2) NOT NULL COMMENT '新价格',
    change_reason VARCHAR(255) COMMENT '修改原因',
    change_type ENUM('CREATE', 'UPDATE', 'DELETE') DEFAULT 'UPDATE' COMMENT '变更类型',
    operator_id VARCHAR(50) COMMENT '操作员ID',
    operator_name VARCHAR(50) COMMENT '操作员姓名',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tag_id) REFERENCES price_tags(id) ON DELETE CASCADE,
    INDEX idx_tag_id (tag_id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_created_at (created_at),
    INDEX idx_change_type (change_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格历史记录表';

-- 5. 操作日志表
CREATE TABLE operation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：SYNC_CATEGORY, SYNC_PRICE, UPDATE_PRICE等',
    table_name VARCHAR(50) COMMENT '操作的表名',
    record_id VARCHAR(50) COMMENT '操作的记录ID',
    operation_data JSON COMMENT '操作数据',
    operator_id VARCHAR(50) COMMENT '操作员ID',
    operator_name VARCHAR(50) COMMENT '操作员姓名',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_operation_type (operation_type),
    INDEX idx_table_name (table_name),
    INDEX idx_record_id (record_id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 6. 数据同步记录表
CREATE TABLE sync_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sync_type VARCHAR(20) NOT NULL COMMENT '同步类型：CATEGORY, PRICE',
    api_url VARCHAR(255) NOT NULL COMMENT 'API地址',
    request_params JSON COMMENT '请求参数',
    response_data JSON COMMENT '响应数据（截取前1000字符）',
    sync_status TINYINT NOT NULL COMMENT '同步状态：1-成功，0-失败',
    error_message TEXT COMMENT '错误信息',
    records_count INT DEFAULT 0 COMMENT '同步记录数',
    duration_ms INT COMMENT '耗时（毫秒）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sync_type (sync_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据同步记录表';

-- 插入初始操作日志
INSERT INTO operation_logs (operation_type, operation_data, operator_id, operator_name) VALUES
('DATABASE_REDESIGN', '{"action": "recreate_tables", "tables": ["phone_categories", "memory_specs", "price_tags", "price_history", "operation_logs", "sync_records"], "timestamp": "2025-06-17"}', 'system', '系统重构'),
('SYSTEM_READY', '{"message": "重新设计的数据库表结构创建完成", "timestamp": "2025-06-17"}', 'system', '系统重构');

-- 5. 涨跌榜表（存储每日涨跌榜数据）
CREATE TABLE price_trend_rankings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ranking_date DATE NOT NULL COMMENT '榜单日期',
    category_id INT NOT NULL COMMENT '分类ID，关联phone_categories表',
    category_level TINYINT NOT NULL COMMENT '分类级别：1-品牌，2-型号，3-子型号',
    category_name VARCHAR(200) NOT NULL COMMENT '分类名称',
    brand_name VARCHAR(200) COMMENT '品牌名称',
    model_name VARCHAR(200) COMMENT '型号名称',
    sub_model_name VARCHAR(200) COMMENT '子型号名称',
    memory_size VARCHAR(50) COMMENT '内存规格',
    tag_name VARCHAR(100) COMMENT '价格标签名称',
    group_name VARCHAR(100) COMMENT '分组名称',
    current_price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
    previous_price DECIMAL(10,2) NOT NULL COMMENT '前一日价格',
    price_change DECIMAL(10,2) NOT NULL COMMENT '价格变化（正数为涨，负数为跌）',
    change_percentage DECIMAL(5,2) NOT NULL COMMENT '变化百分比',
    trend_type ENUM('RISE', 'FALL', 'STABLE') NOT NULL COMMENT '趋势类型：涨、跌、稳定',
    ranking_position INT NOT NULL COMMENT '排名位置',
    ranking_type ENUM('RISE_RANKING', 'FALL_RANKING') NOT NULL COMMENT '榜单类型：涨幅榜、跌幅榜',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES phone_categories(id) ON DELETE CASCADE,
    UNIQUE KEY uk_ranking_date_category_type (ranking_date, category_id, ranking_type),
    INDEX idx_ranking_date (ranking_date),
    INDEX idx_category_id (category_id),
    INDEX idx_trend_type (trend_type),
    INDEX idx_ranking_type (ranking_type),
    INDEX idx_ranking_position (ranking_position),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='涨跌榜表';

-- 6. 涨跌趋势历史表（存储历史趋势数据）
CREATE TABLE price_trend_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL COMMENT '分类ID，关联phone_categories表',
    category_level TINYINT NOT NULL COMMENT '分类级别：1-品牌，2-型号，3-子型号',
    analysis_date DATE NOT NULL COMMENT '分析日期',
    period_days INT NOT NULL DEFAULT 7 COMMENT '分析周期（天数）',
    total_changes INT NOT NULL DEFAULT 0 COMMENT '总变化次数',
    rise_changes INT NOT NULL DEFAULT 0 COMMENT '上涨次数',
    fall_changes INT NOT NULL DEFAULT 0 COMMENT '下跌次数',
    stable_changes INT NOT NULL DEFAULT 0 COMMENT '稳定次数',
    avg_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '平均价格',
    max_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '最高价格',
    min_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '最低价格',
    total_change_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '总变化金额',
    avg_change_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '平均变化金额',
    volatility_score DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '波动性评分（0-100）',
    trend_direction ENUM('UPWARD', 'DOWNWARD', 'STABLE', 'VOLATILE') NOT NULL DEFAULT 'STABLE' COMMENT '整体趋势方向',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES phone_categories(id) ON DELETE CASCADE,
    UNIQUE KEY uk_category_analysis_date_period (category_id, analysis_date, period_days),
    INDEX idx_category_id (category_id),
    INDEX idx_analysis_date (analysis_date),
    INDEX idx_period_days (period_days),
    INDEX idx_trend_direction (trend_direction),
    INDEX idx_volatility_score (volatility_score),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='涨跌趋势历史表';

-- 显示创建结果
SELECT '重新设计的数据库表结构创建完成！' as message;
SHOW TABLES;
