-- MySQL dump 10.13  Distrib 8.0.39, for macos14 (arm64)
--
-- Host: localhost    Database: baofeng_admin
-- ------------------------------------------------------
-- Server version	8.0.39

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admins` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '管理员ID (UUID)',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱 (登录标识)',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `first_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名',
  `last_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓',
  `avatar` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `role` enum('admin','super_admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'admin' COMMENT '管理员角色',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `permissions` json DEFAULT NULL COMMENT '权限配置 (JSON格式)',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `created_by` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`),
  KEY `fk_created_by` (`created_by`),
  CONSTRAINT `fk_admins_created_by` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES ('admin-normal-001','<EMAIL>','manager','$2b$12$jn6Nmke5N5oy1yh1xniT/.Oas1mhQROltxX/zZGNgT9SCX7afYfuy','业务','经理',NULL,'admin',1,'{\"logs\": [\"read\"], \"users\": [\"read\", \"update\"]}',NULL,NULL,'admin-super-001','2025-06-15 15:02:59','2025-06-15 15:02:59'),('admin-super-001','<EMAIL>','admin','$2b$12$UPfCYLIwQJDMfDvQGxTdNOeF9bjYFkobU1NA7PTkc.IekGOaP09cS','系统','管理员',NULL,'super_admin',1,'{\"logs\": [\"read\"], \"users\": [\"create\", \"read\", \"update\", \"delete\"], \"admins\": [\"create\", \"read\", \"update\", \"delete\"], \"system\": [\"read\", \"update\"]}','2025-06-16 06:36:49','unknown',NULL,'2025-06-15 15:02:59','2025-06-16 06:36:49');
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `contact_access_logs`
--

DROP TABLE IF EXISTS `contact_access_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contact_access_logs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `contact_id` int NOT NULL COMMENT '联系人ID',
  `share_config_id` int DEFAULT NULL COMMENT '分享配置ID（如果通过分享访问）',
  `visitor_user_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问者用户ID',
  `visitor_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问者IP',
  `access_type` enum('VIEW','SHARE','DOWNLOAD','SAVE') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问类型',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理信息',
  `referer` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源页面',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_share_config_id` (`share_config_id`),
  KEY `idx_visitor_user_id` (`visitor_user_id`),
  KEY `idx_access_type` (`access_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_contact_access_type` (`contact_id`,`access_type`),
  KEY `idx_visitor_date` (`visitor_user_id`,`created_at`),
  CONSTRAINT `contact_access_logs_ibfk_1` FOREIGN KEY (`contact_id`) REFERENCES `miniprogram_contacts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `contact_access_logs_ibfk_2` FOREIGN KEY (`share_config_id`) REFERENCES `contact_share_configs` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人访问记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contact_access_logs`
--

LOCK TABLES `contact_access_logs` WRITE;
/*!40000 ALTER TABLE `contact_access_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `contact_access_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `contact_share_configs`
--

DROP TABLE IF EXISTS `contact_share_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contact_share_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `contact_id` int NOT NULL COMMENT '联系人ID',
  `share_type` enum('QR_CODE','LINK','CARD','MINI_PROGRAM') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分享类型',
  `share_title` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分享标题',
  `share_description` text COLLATE utf8mb4_unicode_ci COMMENT '分享描述',
  `share_image_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分享图片URL',
  `share_scope` enum('PUBLIC','FRIENDS','PRIVATE') COLLATE utf8mb4_unicode_ci DEFAULT 'PRIVATE' COMMENT '分享范围',
  `allowed_users` json DEFAULT NULL COMMENT '允许查看的用户ID数组（当share_scope为PRIVATE时使用）',
  `include_phone` tinyint(1) DEFAULT '1' COMMENT '是否包含手机号',
  `include_wechat` tinyint(1) DEFAULT '1' COMMENT '是否包含微信号',
  `include_email` tinyint(1) DEFAULT '0' COMMENT '是否包含邮箱',
  `include_company` tinyint(1) DEFAULT '0' COMMENT '是否包含公司信息',
  `include_address` tinyint(1) DEFAULT '0' COMMENT '是否包含地址',
  `include_notes` tinyint(1) DEFAULT '0' COMMENT '是否包含备注',
  `share_count` int DEFAULT '0' COMMENT '分享次数',
  `view_count` int DEFAULT '0' COMMENT '查看次数',
  `last_shared_at` timestamp NULL DEFAULT NULL COMMENT '最后分享时间',
  `last_viewed_at` timestamp NULL DEFAULT NULL COMMENT '最后查看时间',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间（NULL表示永不过期）',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_contact_id` (`contact_id`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_share_scope` (`share_scope`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_contact` (`user_id`,`contact_id`),
  KEY `idx_active_expires` (`is_active`,`expires_at`),
  CONSTRAINT `contact_share_configs_ibfk_1` FOREIGN KEY (`contact_id`) REFERENCES `miniprogram_contacts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人分享配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contact_share_configs`
--

LOCK TABLES `contact_share_configs` WRITE;
/*!40000 ALTER TABLE `contact_share_configs` DISABLE KEYS */;
/*!40000 ALTER TABLE `contact_share_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `miniprogram_config_logs`
--

DROP TABLE IF EXISTS `miniprogram_config_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `miniprogram_config_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_id` int NOT NULL COMMENT '配置ID',
  `operation_type` enum('CREATE','UPDATE','DELETE','ENABLE','DISABLE') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `old_value` json DEFAULT NULL COMMENT '修改前的值',
  `new_value` json DEFAULT NULL COMMENT '修改后的值',
  `operator` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作者',
  `operation_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作原因',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator` (`operator`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `miniprogram_config_logs_ibfk_1` FOREIGN KEY (`config_id`) REFERENCES `miniprogram_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小程序配置操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `miniprogram_config_logs`
--

LOCK TABLES `miniprogram_config_logs` WRITE;
/*!40000 ALTER TABLE `miniprogram_config_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `miniprogram_config_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `miniprogram_configs`
--

DROP TABLE IF EXISTS `miniprogram_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `miniprogram_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_type` enum('CAROUSEL','ANNOUNCEMENT','CONTACT_PHONE','WECHAT_COPY','SHARE_CONFIG','SYSTEM_CONFIG') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置类型',
  `config_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键名，如：carousel_1, announcement_main',
  `config_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称，如：首页轮播图1, 主要公告',
  `config_value` json NOT NULL COMMENT '配置值，JSON格式存储不同类型的配置数据',
  `display_order` int DEFAULT '0' COMMENT '显示顺序，数字越小越靠前',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间，NULL表示立即生效',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间，NULL表示永久有效',
  `group_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分组名称，用于配置分类管理',
  `tags` json DEFAULT NULL COMMENT '标签数组，用于配置筛选和管理',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '配置描述',
  `extra_data` json DEFAULT NULL COMMENT '扩展数据，存储额外的配置信息',
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '创建者',
  `updated_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'system' COMMENT '更新者',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_type_key` (`config_type`,`config_key`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_display_order` (`display_order`),
  KEY `idx_group_name` (`group_name`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `miniprogram_configs`
--

LOCK TABLES `miniprogram_configs` WRITE;
/*!40000 ALTER TABLE `miniprogram_configs` DISABLE KEYS */;
INSERT INTO `miniprogram_configs` VALUES (1,'CAROUSEL','carousel_1','首页轮播图1','{\"title\": \"欢迎使用宝丰回收\", \"subtitle\": \"专业手机回收服务\", \"image_url\": \"https://example.com/carousel1.jpg\", \"link_type\": \"page\", \"link_value\": \"/pages/home/<USER>", \"background_color\": \"#FF6B6B\"}',1,1,NULL,NULL,'home_carousel',NULL,'首页主要轮播图',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(2,'CAROUSEL','carousel_2','首页轮播图2','{\"title\": \"高价回收\", \"subtitle\": \"价格透明，服务专业\", \"image_url\": \"https://example.com/carousel2.jpg\", \"link_type\": \"page\", \"link_value\": \"/pages/recycle/index\", \"background_color\": \"#4ECDC4\"}',2,1,NULL,NULL,'home_carousel',NULL,'首页第二张轮播图',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(3,'ANNOUNCEMENT','main_notice','主要公告','{\"type\": \"info\", \"title\": \"系统维护通知\", \"content\": \"系统将于今晚22:00-24:00进行维护，期间可能影响部分功能使用，敬请谅解。\", \"closable\": true, \"show_icon\": true, \"auto_close_time\": 5000}',1,1,NULL,NULL,'system_notice',NULL,'系统主要公告',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(4,'ANNOUNCEMENT','promotion_notice','促销公告','{\"type\": \"success\", \"title\": \"限时优惠\", \"content\": \"本月回收价格上调10%，机会难得，快来回收吧！\", \"closable\": true, \"highlight\": true, \"show_icon\": true}',2,1,NULL,NULL,'promotion_notice',NULL,'促销活动公告',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(5,'CONTACT_PHONE','customer_service','客服电话','{\"description\": \"专业客服为您服务\", \"confirm_text\": \"确定要拨打客服电话吗？\", \"display_name\": \"客服热线\", \"phone_number\": \"************\", \"service_time\": \"9:00-18:00\", \"show_confirm\": true}',1,1,NULL,NULL,'contact_info',NULL,'客服联系电话',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(6,'CONTACT_PHONE','business_phone','商务合作','{\"description\": \"商务合作洽谈\", \"confirm_text\": \"确定要拨打商务电话吗？\", \"display_name\": \"商务合作\", \"phone_number\": \"138-0000-1234\", \"service_time\": \"工作日 9:00-17:30\", \"show_confirm\": true}',2,1,NULL,NULL,'contact_info',NULL,'商务合作电话',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(7,'WECHAT_COPY','customer_wechat','客服微信','{\"wechat_id\": \"baofeng_service\", \"description\": \"添加客服微信，获得更好服务\", \"qr_code_url\": \"https://example.com/wechat_qr.jpg\", \"display_name\": \"客服微信\", \"show_qr_code\": true, \"copy_success_text\": \"微信号已复制，请到微信添加好友\"}',1,1,NULL,NULL,'wechat_contact',NULL,'客服微信号',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(8,'WECHAT_COPY','business_wechat','商务微信','{\"wechat_id\": \"baofeng_business\", \"description\": \"商务合作请添加此微信\", \"qr_code_url\": \"https://example.com/business_qr.jpg\", \"display_name\": \"商务合作微信\", \"show_qr_code\": true, \"copy_success_text\": \"商务微信号已复制\"}',2,1,NULL,NULL,'wechat_contact',NULL,'商务合作微信号',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(9,'SHARE_CONFIG','app_share','应用分享','{\"title\": \"宝丰回收 - 专业手机回收平台\", \"enable_qq\": false, \"image_url\": \"https://example.com/share_logo.jpg\", \"share_path\": \"/pages/home/<USER>", \"description\": \"高价回收，价格透明，服务专业，值得信赖的手机回收平台\", \"enable_session\": true, \"enable_timeline\": true}',1,1,NULL,NULL,'share_settings',NULL,'应用分享配置',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(10,'SHARE_CONFIG','price_share','价格分享','{\"title\": \"手机回收价格查询\", \"image_url\": \"https://example.com/price_share.jpg\", \"share_path\": \"/pages/price/index\", \"description\": \"实时更新的手机回收价格，快来查看你的手机值多少钱\", \"enable_session\": true, \"enable_timeline\": true}',2,1,NULL,NULL,'share_settings',NULL,'价格查询分享配置',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(11,'SYSTEM_CONFIG','app_info','应用信息','{\"app_name\": \"宝丰回收\", \"copyright\": \"© 2025 宝丰科技有限公司\", \"terms_url\": \"https://example.com/terms\", \"app_version\": \"1.0.0\", \"privacy_url\": \"https://example.com/privacy\", \"company_name\": \"宝丰科技有限公司\"}',1,1,NULL,NULL,'app_settings',NULL,'应用基本信息',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31'),(12,'SYSTEM_CONFIG','feature_switches','功能开关','{\"maintenance_mode\": false, \"enable_price_trend\": true, \"maintenance_message\": \"系统维护中，请稍后再试\", \"enable_user_feedback\": true, \"enable_location_service\": false, \"enable_push_notification\": true}',1,1,NULL,NULL,'app_settings',NULL,'功能开关配置',NULL,'system','system','2025-06-22 10:14:31','2025-06-22 10:14:31');
/*!40000 ALTER TABLE `miniprogram_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `miniprogram_contacts`
--

DROP TABLE IF EXISTS `miniprogram_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `miniprogram_contacts` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
  `user_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID，关联users表',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人姓名',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号码',
  `wechat_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信号',
  `avatar_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `category` enum('FRIEND','FAMILY','COLLEAGUE','BUSINESS','OTHER') COLLATE utf8mb4_unicode_ci DEFAULT 'FRIEND' COMMENT '联系人分类',
  `tags` json DEFAULT NULL COMMENT '标签数组，如：["重要客户", "同事", "朋友"]',
  `company` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司名称',
  `position` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职位',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱地址',
  `address` text COLLATE utf8mb4_unicode_ci COMMENT '地址',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT '备注信息',
  `contact_frequency` enum('DAILY','WEEKLY','MONTHLY','RARELY','NEVER') COLLATE utf8mb4_unicode_ci DEFAULT 'RARELY' COMMENT '联系频率',
  `importance_level` tinyint DEFAULT '3' COMMENT '重要程度：1-5，5最重要',
  `is_favorite` tinyint(1) DEFAULT '0' COMMENT '是否收藏',
  `last_contact_at` timestamp NULL DEFAULT NULL COMMENT '最后联系时间',
  `last_contact_type` enum('CALL','MESSAGE','WECHAT','EMAIL','MEETING','OTHER') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后联系方式',
  `contact_count` int DEFAULT '0' COMMENT '联系次数统计',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开（可被其他用户搜索）',
  `share_phone` tinyint(1) DEFAULT '0' COMMENT '是否允许分享手机号',
  `share_wechat` tinyint(1) DEFAULT '0' COMMENT '是否允许分享微信号',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-正常，0-已删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_wechat_id` (`wechat_id`),
  KEY `idx_category` (`category`),
  KEY `idx_importance_level` (`importance_level`),
  KEY `idx_is_favorite` (`is_favorite`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_last_contact_at` (`last_contact_at`),
  KEY `idx_user_category` (`user_id`,`category`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_public_status` (`is_public`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小程序联系人表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `miniprogram_contacts`
--

LOCK TABLES `miniprogram_contacts` WRITE;
/*!40000 ALTER TABLE `miniprogram_contacts` DISABLE KEYS */;
INSERT INTO `miniprogram_contacts` VALUES (1,'user-test-001','测试联系人','13900139000',NULL,NULL,'FRIEND',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'RARELY',3,0,NULL,NULL,0,1,0,0,1,'2025-06-21 08:02:21','2025-06-21 08:02:21'),(2,'user-test-001','测试联系人','13900139000',NULL,NULL,'FRIEND',NULL,NULL,NULL,NULL,NULL,NULL,NULL,'RARELY',3,0,NULL,NULL,0,1,0,0,1,'2025-06-21 08:03:07','2025-06-21 08:03:07');
/*!40000 ALTER TABLE `miniprogram_contacts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `operation_logs`
--

DROP TABLE IF EXISTS `operation_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `operation_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户ID',
  `admin_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员ID',
  `user_type` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户类型',
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作动作',
  `resource` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作资源',
  `resource_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源ID',
  `details` json DEFAULT NULL COMMENT '操作详情 (JSON格式)',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_action` (`action`),
  KEY `idx_resource` (`resource`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_operation_logs_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_operation_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `operation_logs`
--

LOCK TABLES `operation_logs` WRITE;
/*!40000 ALTER TABLE `operation_logs` DISABLE KEYS */;
INSERT INTO `operation_logs` VALUES (1,NULL,'admin-super-001','admin','CREATE_ADMIN','admins','admin-normal-001','{\"description\": \"创建普通管理员账户\", \"target_email\": \"<EMAIL>\"}','127.0.0.1',NULL,'2025-06-15 15:02:59'),(2,NULL,'admin-super-001','admin','SYSTEM_INIT','system',NULL,'{\"version\": \"1.0.0\", \"description\": \"系统初始化完成\"}','127.0.0.1',NULL,'2025-06-15 15:02:59');
/*!40000 ALTER TABLE `operation_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID',
  `user_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户ID',
  `admin_id` varchar(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员ID',
  `user_type` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户类型',
  `access_token` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问令牌',
  `refresh_token` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '刷新令牌',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_sessions_admin_id` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID (UUID)',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `role` enum('user','admin','super_admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT '用户角色',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `wechat_open_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信OpenID',
  `wechat_union_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信UnionID',
  `wechat_session_key` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信SessionKey',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_wechat_open_id` (`wechat_open_id`),
  UNIQUE KEY `uk_wechat_union_id` (`wechat_union_id`),
  KEY `idx_role` (`role`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('60027a43-db23-48f7-a173-8b79ac4f3ec6','13800138003','https://example.com/avatar3.jpg','user',1,'test_openid_3',NULL,NULL,'2025-06-21 19:38:23','2025-06-21 19:38:23'),('70e13506-10f9-4718-a506-a423e8e61924','13800138002',NULL,'user',1,'test_openid_2','test_unionid_2','test_session_2','2025-06-21 19:38:23','2025-06-21 19:38:23'),('98898e4a-c230-483d-928b-66f06cd29841',NULL,NULL,'user',1,'test_openid_4',NULL,NULL,'2025-06-21 19:38:23','2025-06-21 19:38:23'),('e0738c13-9c5a-4be4-a732-1adb1afdac09','13800138001',NULL,'admin',1,'test_openid_1','test_unionid_1','test_session_1','2025-06-21 19:38:23','2025-06-21 19:38:23');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'baofeng_admin'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-22 21:42:09
