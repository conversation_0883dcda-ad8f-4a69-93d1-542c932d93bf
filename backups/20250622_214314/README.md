# 数据库备份说明

## 备份时间
2025-06-22 21:43:14

## 备份内容

### baofeng_admin_backup.sql (29KB)
- 数据库: baofeng_admin
- 包含表: admins, contact_access_logs, contact_share_configs, miniprogram_config_logs, miniprogram_configs, miniprogram_contacts, operation_logs, sessions, users
- 备份方式: mysqldump --single-transaction --routines --triggers

### baofeng_recycle_backup.sql (6MB)
- 数据库: baofeng_recycle  
- 包含表: admins, customer_contact_logs, memory_specs, operation_logs, phone_categories, price_history, price_tag_applications, price_tag_rules, price_tags, price_trend_history, price_trend_rankings, recycle_employees, recycle_stores, store_contact_configs, sync_records
- 备份方式: mysqldump --single-transaction --routines --triggers

## 恢复方法

如果需要恢复数据库，使用以下命令：

```bash
# 恢复 baofeng_admin 数据库
mysql -u root -pbaofeng123456 baofeng_admin < baofeng_admin_backup.sql

# 恢复 baofeng_recycle 数据库  
mysql -u root -pbaofeng123456 baofeng_recycle < baofeng_recycle_backup.sql
```

## 备份验证

备份文件已创建并验证：
- ✅ baofeng_admin_backup.sql - 29,392 bytes
- ✅ baofeng_recycle_backup.sql - 6,166,888 bytes

## 注意事项

1. 这些备份文件包含完整的数据库结构和数据
2. 备份时使用了 --single-transaction 确保数据一致性
3. 包含了存储过程、触发器等数据库对象
4. 在进行数据库迁移前，请确保这些备份文件安全保存
