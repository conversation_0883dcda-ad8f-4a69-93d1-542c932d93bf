# 宝丰后台管理系统 - 完整API接口文档

## 📋 目录
- [系统信息](#系统信息)
- [认证说明](#认证说明)
- [C端用户认证接口](#c端用户认证接口)
- [C端用户管理接口](#c端用户管理接口)
- [B端管理员认证接口](#b端管理员认证接口)
- [类目管理接口](#类目管理接口)
- [价格涨跌榜接口](#价格涨跌榜接口)
- [响应格式](#响应格式)
- [错误码说明](#错误码说明)

## 系统信息

**基础URL**: `http://localhost:3000/api/v1`  
**API文档**: `http://localhost:3000/api-docs`  
**健康检查**: `http://localhost:3000/health`

## 认证说明

### JWT Token认证
- **Header**: `Authorization: Bearer <token>`
- **Token类型**: JWT
- **过期时间**: 7天
- **刷新Token**: 30天

### 权限级别
- **user**: 普通用户
- **admin**: 管理员
- **super_admin**: 超级管理员

## C端用户认证接口

### 1. 用户注册
```
POST /auth/register
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "张",
  "lastName": "三",
  "role": "user"
}
```

**响应**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "张",
      "lastName": "三",
      "role": "user"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token"
    }
  }
}
```

### 2. 用户登录
```
POST /auth/login
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 3. 刷新Token
```
POST /auth/refresh
```

**请求体**:
```json
{
  "refreshToken": "refresh_token"
}
```

### 4. 用户登出
```
POST /auth/logout
```
**认证**: 需要Bearer Token

### 5. 获取用户资料
```
GET /auth/profile
```
**认证**: 需要Bearer Token

### 6. 更新用户资料
```
PUT /auth/profile
```
**认证**: 需要Bearer Token

**请求体**:
```json
{
  "firstName": "李",
  "lastName": "四",
  "email": "<EMAIL>"
}
```

### 7. 获取当前用户信息（通用）
```
GET /auth/user
```
**认证**: 需要Bearer Token  
**说明**: 支持管理员和普通用户

## C端用户管理接口

### 1. 获取用户列表
```
GET /users
```
**认证**: 需要管理员权限

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `search`: 搜索关键词
- `role`: 角色筛选
- `isActive`: 状态筛选

### 2. 获取指定用户
```
GET /users/{id}
```
**认证**: 需要管理员权限

### 3. 创建用户
```
POST /users
```
**认证**: 需要管理员权限

### 4. 更新用户
```
PUT /users/{id}
```
**认证**: 需要管理员权限

### 5. 删除用户
```
DELETE /users/{id}
```
**认证**: 需要管理员权限

## B端管理员认证接口

### 1. 管理员登录
```
POST /admin/auth/login
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "admin123456"
}
```

### 2. 获取管理员资料
```
GET /admin/auth/profile
```
**认证**: 需要Bearer Token

### 3. 创建管理员
```
POST /admin/auth/create
```
**认证**: 需要超级管理员权限

**请求体**:
```json
{
  "email": "<EMAIL>",
  "username": "newadmin",
  "password": "password123",
  "firstName": "新",
  "lastName": "管理员",
  "role": "admin",
  "phone": "13800138000"
}
```

### 4. 修改密码
```
POST /admin/auth/change-password
```
**认证**: 需要Bearer Token

**请求体**:
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword"
}
```

### 5. 管理员登出
```
POST /admin/auth/logout
```
**认证**: 需要Bearer Token

## 类目管理接口

### 基础类目接口

#### 1. 获取根类目
```
GET /categories
```

#### 2. 获取类目树
```
GET /categories/tree
```

#### 3. 获取所有品牌
```
GET /categories/brands
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `search`: 搜索关键词
- `parent_id`: 父级ID

#### 4. 根据类目获取品牌树
```
GET /categories/{categoryId}/brands
```

#### 5. 获取品牌下的型号
```
GET /categories/brands/{brandId}/models
```

#### 6. 获取型号下的子型号
```
GET /categories/models/{modelId}/sub-models
```

### 类目管理接口

#### 1. 获取类目详情
```
GET /categories/{table}/{id}
```

**路径参数**:
- `table`: 表名 (phone_brands, phone_models, phone_sub_models)
- `id`: 记录ID

#### 2. 软删除品牌
```
PUT /categories/brands/{id}/delete
```

#### 3. 软删除型号
```
PUT /categories/models/{id}/delete
```
**认证**: 需要Bearer Token

#### 4. 软删除子型号
```
PUT /categories/sub-models/{id}/delete
```
**认证**: 需要Bearer Token

#### 5. 恢复类目
```
PUT /categories/{table}/{id}/restore
```
**认证**: 需要Bearer Token

### 价格功能接口

#### 1. 获取价格信息
```
GET /categories/{table}/{id}/price
```

#### 2. 获取价格趋势
```
GET /categories/{table}/{id}/price-trend
```

**查询参数**:
- `days`: 分析天数 (默认: 30)

### 内存规格和价格标签

#### 1. 获取内存规格
```
GET /categories/sub-models/{subModelId}/memory-specs
```

#### 2. 获取价格标签
```
GET /categories/memory-specs/{memorySpecId}/price-tags
```

#### 3. 获取完整型号详情
```
GET /categories/sub-models/{subModelId}/complete
```

### 数据同步

#### 1. 同步类目数据
```
POST /categories/sync
```
**认证**: 需要Bearer Token

#### 2. 调试接口
```
GET /categories/debug/{subModelId}
```

## 价格涨跌榜接口

### 1. 获取涨跌榜
```
GET /categories/price-rankings
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `type`: 榜单类型 (RISE_RANKING | FALL_RANKING)
- `date`: 榜单日期 (YYYY-MM-DD)
- `category_level`: 分类级别 (1, 2, 3)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "ranking_date": "2025-06-19",
        "category_name": "iPhone 15 Pro Max",
        "brand_name": "苹果",
        "current_price": "8999.00",
        "previous_price": "8799.00",
        "price_change": "200.00",
        "change_percentage": "2.27",
        "trend_type": "RISE",
        "ranking_position": 1,
        "ranking_type": "RISE_RANKING"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3
    }
  }
}
```

### 2. 获取涨跌榜统计
```
GET /categories/price-rankings/stats
```

**查询参数**:
- `date`: 统计日期 (YYYY-MM-DD)

### 3. 手动触发计算
```
POST /categories/price-rankings/calculate
```
**认证**: 需要Bearer Token

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2025-06-19T12:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "timestamp": "2025-06-19T12:00:00.000Z"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "list": [],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

## 错误码说明

| HTTP状态码 | 说明 |
|-----------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 使用示例

### 获取涨幅榜前10名
```bash
curl -X GET "http://localhost:3000/api/v1/categories/price-rankings?type=RISE_RANKING&limit=10"
```

### 管理员登录
```bash
curl -X POST "http://localhost:3000/api/v1/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123456"}'
```

### 获取类目树
```bash
curl -X GET "http://localhost:3000/api/v1/categories/tree"
```
