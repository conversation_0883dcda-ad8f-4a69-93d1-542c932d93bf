# 微信小程序配置系统 - 快速开始

## 🚀 5分钟快速上手

### 1. 系统初始化

#### 自动初始化（推荐）
```bash
# 使用默认配置初始化
./scripts/init-miniprogram-config.sh

# 或指定数据库配置
./scripts/init-miniprogram-config.sh -H localhost -u root -p your_password -d baofeng_admin
```

#### 手动初始化
```bash
# 1. 创建数据库
mysql -u root -p -e "CREATE DATABASE baofeng_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 2. 执行SQL脚本
mysql -u root -p baofeng_admin < docs/database/miniprogram-config-schema.sql
```

### 2. 启动服务

```bash
# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev

# 服务将在 http://localhost:3000 启动
```

### 3. 验证安装

#### 健康检查
```bash
curl http://localhost:3000/health
```

#### 获取配置（无需认证）
```bash
curl http://localhost:3000/api/v1/miniprogram/configs/active
```

#### 运行测试脚本
```bash
# 注意：需要先获取有效的JWT token
node scripts/test-miniprogram-config.js
```

## 📱 小程序集成

### 基础配置获取

```javascript
// 小程序 app.js 或页面文件中
const API_BASE = 'https://your-api-domain.com/api/v1';

// 获取所有有效配置
const getAllConfigs = () => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${API_BASE}/miniprogram/configs/active`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data.message));
        }
      },
      fail: reject
    });
  });
};

// 按类型获取配置
const getConfigsByType = (type) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${API_BASE}/miniprogram/configs/active`,
      data: { type },
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data.message));
        }
      },
      fail: reject
    });
  });
};
```

### 轮播图组件

```javascript
// pages/home/<USER>
Page({
  data: {
    carouselList: []
  },

  onLoad() {
    this.loadCarouselConfigs();
  },

  async loadCarouselConfigs() {
    try {
      const configs = await getConfigsByType('CAROUSEL');
      const carouselList = configs
        .sort((a, b) => a.order - b.order)
        .map(config => config.value);
      
      this.setData({ carouselList });
    } catch (error) {
      console.error('加载轮播图配置失败:', error);
    }
  },

  onCarouselTap(e) {
    const item = e.currentTarget.dataset.item;
    if (item.link_type === 'page' && item.link_value) {
      wx.navigateTo({
        url: item.link_value
      });
    } else if (item.link_type === 'url' && item.link_value) {
      // 处理外部链接
      wx.showModal({
        title: '提示',
        content: '即将跳转到外部链接',
        success: (res) => {
          if (res.confirm) {
            // 可以通过webview页面打开
            wx.navigateTo({
              url: `/pages/webview/webview?url=${encodeURIComponent(item.link_value)}`
            });
          }
        }
      });
    }
  }
});
```

```xml
<!-- pages/home/<USER>
<swiper class="carousel" indicator-dots="true" autoplay="true" interval="3000" duration="500">
  <swiper-item wx:for="{{carouselList}}" wx:key="index" bindtap="onCarouselTap" data-item="{{item}}">
    <image src="{{item.image_url}}" class="carousel-image" mode="aspectFill" />
    <view class="carousel-content" style="background-color: {{item.background_color || '#000'}}">
      <text class="carousel-title">{{item.title}}</text>
      <text class="carousel-subtitle" wx:if="{{item.subtitle}}">{{item.subtitle}}</text>
    </view>
  </swiper-item>
</swiper>
```

### 公告组件

```javascript
// components/announcement/announcement.js
Component({
  data: {
    announcements: [],
    currentIndex: 0
  },

  lifetimes: {
    attached() {
      this.loadAnnouncements();
    }
  },

  methods: {
    async loadAnnouncements() {
      try {
        const configs = await getConfigsByType('ANNOUNCEMENT');
        const announcements = configs
          .sort((a, b) => a.order - b.order)
          .map(config => config.value);
        
        this.setData({ announcements });
        
        // 自动轮播公告
        if (announcements.length > 1) {
          this.startAutoPlay();
        }
      } catch (error) {
        console.error('加载公告配置失败:', error);
      }
    },

    startAutoPlay() {
      setInterval(() => {
        const { announcements, currentIndex } = this.data;
        const nextIndex = (currentIndex + 1) % announcements.length;
        this.setData({ currentIndex: nextIndex });
      }, 5000);
    },

    onAnnouncementTap() {
      const announcement = this.data.announcements[this.data.currentIndex];
      if (announcement) {
        wx.showModal({
          title: announcement.title,
          content: announcement.content,
          showCancel: announcement.closable !== false
        });
      }
    }
  }
});
```

### 联系功能组件

```javascript
// components/contact/contact.js
Component({
  methods: {
    async callPhone() {
      try {
        const configs = await getConfigsByType('CONTACT_PHONE');
        if (configs.length === 0) return;
        
        const phoneConfig = configs[0].value;
        
        if (phoneConfig.show_confirm) {
          wx.showModal({
            title: '拨打电话',
            content: phoneConfig.confirm_text || `确定要拨打${phoneConfig.display_name}吗？`,
            success: (res) => {
              if (res.confirm) {
                wx.makePhoneCall({
                  phoneNumber: phoneConfig.phone_number
                });
              }
            }
          });
        } else {
          wx.makePhoneCall({
            phoneNumber: phoneConfig.phone_number
          });
        }
      } catch (error) {
        console.error('获取电话配置失败:', error);
        wx.showToast({
          title: '获取联系方式失败',
          icon: 'none'
        });
      }
    },

    async copyWechat() {
      try {
        const configs = await getConfigsByType('WECHAT_COPY');
        if (configs.length === 0) return;
        
        const wechatConfig = configs[0].value;
        
        wx.setClipboardData({
          data: wechatConfig.wechat_id,
          success: () => {
            wx.showToast({
              title: wechatConfig.copy_success_text || '微信号已复制',
              icon: 'success'
            });
          }
        });
      } catch (error) {
        console.error('获取微信配置失败:', error);
        wx.showToast({
          title: '获取微信号失败',
          icon: 'none'
        });
      }
    },

    async shareApp() {
      try {
        const configs = await getConfigsByType('SHARE_CONFIG');
        if (configs.length === 0) return;
        
        const shareConfig = configs[0].value;
        
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline']
        });
        
        // 设置分享内容
        wx.onShareAppMessage(() => ({
          title: shareConfig.title,
          desc: shareConfig.description,
          path: shareConfig.share_path || '/pages/home/<USER>',
          imageUrl: shareConfig.image_url
        }));
        
        wx.onShareTimeline(() => ({
          title: shareConfig.title,
          imageUrl: shareConfig.image_url
        }));
        
      } catch (error) {
        console.error('获取分享配置失败:', error);
      }
    }
  }
});
```

## 🔧 管理后台集成

### 获取管理员Token

```javascript
// 管理员登录获取token
const adminLogin = async (email, password) => {
  const response = await fetch('/api/v1/admin/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, password })
  });
  
  const data = await response.json();
  if (data.success) {
    localStorage.setItem('admin_token', data.data.accessToken);
    return data.data.accessToken;
  }
  throw new Error(data.message);
};
```

### 配置管理操作

```javascript
// 配置管理API封装
class ConfigManager {
  constructor(token) {
    this.token = token;
    this.baseURL = '/api/v1/miniprogram/configs';
  }

  async request(url, options = {}) {
    const response = await fetch(`${this.baseURL}${url}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      }
    });
    
    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message);
    }
    return data.data;
  }

  // 创建配置
  async createConfig(configData) {
    return this.request('', {
      method: 'POST',
      body: JSON.stringify(configData)
    });
  }

  // 获取配置列表
  async getConfigs(query = {}) {
    const params = new URLSearchParams(query);
    return this.request(`?${params}`);
  }

  // 更新配置
  async updateConfig(id, updateData) {
    return this.request(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  }

  // 删除配置
  async deleteConfig(id) {
    return this.request(`/${id}`, {
      method: 'DELETE'
    });
  }

  // 批量更新
  async batchUpdate(configIds, updates) {
    return this.request('/batch', {
      method: 'POST',
      body: JSON.stringify({
        config_ids: configIds,
        updates
      })
    });
  }

  // 获取统计信息
  async getStatistics() {
    return this.request('/statistics');
  }
}

// 使用示例
const token = localStorage.getItem('admin_token');
const configManager = new ConfigManager(token);

// 创建轮播图配置
await configManager.createConfig({
  config_type: 'CAROUSEL',
  config_key: 'home_banner_1',
  config_name: '首页轮播图1',
  config_value: {
    image_url: 'https://example.com/banner1.jpg',
    title: '欢迎使用我们的服务',
    subtitle: '专业、可靠、值得信赖',
    link_type: 'page',
    link_value: '/pages/service/index'
  },
  group_name: 'home_banners'
});
```

## 🔍 常见问题

### Q: 如何添加新的配置类型？
A: 
1. 在 `src/types/miniprogramConfig.ts` 中添加新的配置类型枚举
2. 定义对应的配置值接口
3. 在验证器中添加验证规则
4. 更新API文档

### Q: 配置不生效怎么办？
A: 检查以下几点：
1. 配置是否启用 (`is_enabled = true`)
2. 配置时间范围是否有效
3. 小程序是否正确调用API
4. 网络连接是否正常

### Q: 如何备份配置数据？
A: 
```bash
# 导出配置数据
mysqldump -u root -p baofeng_admin miniprogram_configs > configs_backup.sql

# 恢复配置数据
mysql -u root -p baofeng_admin < configs_backup.sql
```

### Q: 如何监控配置变更？
A: 系统自动记录所有配置变更到 `miniprogram_config_logs` 表，可以查询该表获取变更历史。

## 📞 技术支持

- 📧 邮箱: <EMAIL>
- 💬 微信: baofeng_tech
- 📖 文档: [完整文档](miniprogram-config-system.md)
- 🐛 问题反馈: [GitHub Issues](https://github.com/baofeng/issues)
