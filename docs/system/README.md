# 🏥 系统健康检查文档

## 📋 **概述**

系统健康检查模块提供了监控服务状态的接口，用于确保系统正常运行。

## 🔗 **接口列表**

### 1. 系统健康检查
**路径**: `GET /api/v1/health`  
**认证**: 不需要  
**描述**: 检查系统整体健康状态

#### 请求示例
```bash
curl -X GET "http://localhost:3000/api/v1/health"
```

#### 响应示例
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "status": "OK",
    "timestamp": "2025-06-16T10:30:00.000Z",
    "uptime": 3600,
    "environment": "development",
    "version": "1.0.0"
  },
  "timestamp": "2025-06-16T10:30:00.000Z"
}
```

#### 响应字段说明
| 字段 | 类型 | 描述 |
|------|------|------|
| `status` | string | 服务状态，通常为 "OK" |
| `timestamp` | string | 当前时间戳 |
| `uptime` | number | 服务运行时间（秒） |
| `environment` | string | 运行环境 (development/production) |
| `version` | string | 服务版本号 |

### 2. 应用级健康检查
**路径**: `GET /health`  
**认证**: 不需要  
**描述**: 应用级别的健康检查，在API路由之外

#### 请求示例
```bash
curl -X GET "http://localhost:3000/health"
```

#### 响应示例
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "status": "OK",
    "timestamp": "2025-06-16T10:30:00.000Z",
    "uptime": 3600,
    "environment": "development",
    "version": "1.0.0"
  }
}
```

## 🔧 **实现细节**

### 路由配置
```typescript
// 在 app.ts 中的应用级健康检查
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Service is healthy',
    data: {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.env,
      version: process.env.npm_package_version || '1.0.0',
    },
  });
});

// 在 routes/index.ts 中的API级健康检查
router.get('/health', (req, res) => {
  sendSuccess(res, {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  }, 'Service is healthy');
});
```

### 监控指标

#### 系统运行时间
- 使用 `process.uptime()` 获取Node.js进程运行时间
- 单位：秒
- 用于监控服务稳定性

#### 环境信息
- 从环境变量 `NODE_ENV` 获取
- 区分开发环境和生产环境
- 用于环境识别和配置验证

#### 版本信息
- 从 `package.json` 的版本号获取
- 用于版本追踪和部署验证

## 📊 **监控建议**

### 1. 自动化监控
```bash
# 使用 curl 进行定期检查
*/5 * * * * curl -f http://localhost:3000/health || echo "Health check failed"
```

### 2. 负载均衡器配置
```nginx
# Nginx 健康检查配置
upstream backend {
    server localhost:3000;
    # 健康检查
    health_check uri=/health;
}
```

### 3. Docker 健康检查
```dockerfile
# Dockerfile 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1
```

### 4. Kubernetes 健康检查
```yaml
# k8s deployment 配置
livenessProbe:
  httpGet:
    path: /health
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health
    port: 3000
  initialDelaySeconds: 5
  periodSeconds: 5
```

## 🚨 **故障排查**

### 常见问题

#### 1. 健康检查失败
**症状**: 返回非200状态码或无响应
**可能原因**:
- 服务未启动
- 端口被占用
- 内存不足
- 数据库连接失败

**排查步骤**:
```bash
# 检查服务状态
ps aux | grep node

# 检查端口占用
lsof -i :3000

# 检查系统资源
free -h
df -h

# 查看应用日志
tail -f logs/app.log
```

#### 2. 响应时间过长
**症状**: 健康检查响应时间超过预期
**可能原因**:
- 系统负载过高
- 数据库查询缓慢
- 内存泄漏

**排查步骤**:
```bash
# 检查系统负载
top
htop

# 检查内存使用
free -h

# 分析Node.js性能
node --inspect app.js
```

## 📈 **扩展功能**

### 1. 数据库健康检查
```typescript
// 扩展健康检查包含数据库状态
app.get('/health/detailed', async (req, res) => {
  try {
    // 检查数据库连接
    await db.raw('SELECT 1');
    
    res.json({
      success: true,
      data: {
        status: 'OK',
        database: 'connected',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      data: {
        status: 'ERROR',
        database: 'disconnected',
        error: error.message
      }
    });
  }
});
```

### 2. 依赖服务检查
```typescript
// 检查外部依赖服务
app.get('/health/dependencies', async (req, res) => {
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkRedis(),
    checkExternalAPI()
  ]);
  
  const results = {
    database: checks[0].status === 'fulfilled',
    redis: checks[1].status === 'fulfilled',
    externalAPI: checks[2].status === 'fulfilled'
  };
  
  const allHealthy = Object.values(results).every(Boolean);
  
  res.status(allHealthy ? 200 : 503).json({
    success: allHealthy,
    data: {
      status: allHealthy ? 'OK' : 'DEGRADED',
      dependencies: results,
      timestamp: new Date().toISOString()
    }
  });
});
```

## 📝 **最佳实践**

1. **快速响应**: 健康检查应该在1秒内响应
2. **轻量级**: 避免在健康检查中执行重量级操作
3. **明确状态**: 返回明确的健康状态信息
4. **日志记录**: 记录健康检查的异常情况
5. **多层检查**: 提供不同级别的健康检查接口

---

📝 **文档最后更新**: 2025-06-16  
🔧 **维护者**: Baofeng R&D Team
