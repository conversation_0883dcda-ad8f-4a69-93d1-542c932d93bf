# WeChat Mini-Program Integration Guide

## Overview

This project provides complete WeChat mini-program authentication integration with phone number authorization. The system supports user authentication through WeChat login and phone number collection with proper security measures.

## Features

- ✅ WeChat Mini-Program Login
- ✅ Phone Number Authorization (New & Legacy APIs)
- ✅ User Data Encryption/Decryption
- ✅ Data Signature Verification
- ✅ Automatic User Creation and Updates
- ✅ JWT Token Generation
- ✅ Comprehensive Error Handling and Logging
- ✅ Database Schema Optimization
- ✅ Type Safety with TypeScript
- ✅ **OpenID-Based Response**: Returns only WeChat OpenID for security and simplicity

## Key Design Decisions

### Using OpenID as Primary Identifier

The API returns WeChat's `openId` as the primary user identifier instead of internal UUIDs or full user objects. This approach provides several benefits:

1. **Security**: Minimizes exposure of sensitive user data
2. **Simplicity**: Single, stable identifier for frontend applications
3. **WeChat Native**: Uses WeChat's own unique identifier system
4. **Logging Friendly**: Easy to track user actions across logs
5. **Stateless**: Frontend only needs to store openId and access token

### Response Structure

- **Login Response**: `{ openId, isNewUser, accessToken }`
- **Phone Update Response**: `{ openId, phoneUpdated: true }`
- **All sensitive data is logged server-side with proper masking**

## Configuration

### Environment Variables

Configure the following environment variables in your `.env` file:

```env
# WeChat Mini-Program Configuration
WECHAT_APP_ID=wx3263a06336cf0b6b
WECHAT_APP_SECRET=your-wechat-app-secret

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=baofeng123456
DB_NAME=baofeng_admin
DB_CHARSET=utf8mb4
```

### Getting WeChat Configuration

1. Login to [WeChat Official Accounts Platform](https://mp.weixin.qq.com/)
2. Navigate to Mini-Program Management
3. Go to "Development" -> "Development Management" -> "Development Settings"
4. Copy the AppID and AppSecret

## API Endpoints

### 1. WeChat Login

**Endpoint:** `POST /api/v1/wechat/login`

**Description:** Authenticate user with WeChat login code. Creates new user if not exists, or logs in existing user.

**Request Headers:**
```
Content-Type: application/json
User-Agent: WeChat Mini-Program User Agent
```

**Request Body:**
```json
{
  "code": "WeChat login code from wx.login()"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "微信登录成功",
  "data": {
    "openId": "wechat-openid-unique-identifier",
    "isNewUser": true,
    "accessToken": "jwt-access-token"
  }
}
```

**Response for Existing User (200):**
```json
{
  "success": true,
  "message": "微信登录成功",
  "data": {
    "openId": "wechat-openid-unique-identifier",
    "isNewUser": false,
    "accessToken": "jwt-access-token"
  }
}
```

**Error Response (400/500):**
```json
{
  "success": false,
  "error": "Error message",
  "details": "Detailed error information"
}
```

### 2. Get Phone Number (New API)

**Endpoint:** `POST /api/v1/wechat/phone`

**Description:** Get user's phone number using WeChat's new phone number API.

**Request Headers:**
```
Content-Type: application/json
Authorization: Bearer <access_token>
```

**Request Body:**
```json
{
  "code": "Phone authorization code from getPhoneNumber"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "手机号获取成功",
  "data": {
    "openId": "wechat-openid-unique-identifier",
    "phoneUpdated": true
  }
}
```

### 3. 通过加密数据获取手机号（兼容旧版本）

**接口地址：** `POST /api/v1/wechat/phone/decrypt`

**请求头：**
```
Authorization: Bearer <access_token>
```

**请求参数：**
```json
{
  "encryptedData": "encrypted_phone_data",
  "iv": "initialization_vector"
}
```

### 4. 检查登录状态

**接口地址：** `GET /api/v1/wechat/status`

**请求头：**
```
Authorization: Bearer <access_token>
```

### 5. 获取配置信息

**接口地址：** `GET /api/v1/wechat/config`

### 6. 登出

**接口地址：** `POST /api/v1/wechat/logout`

**请求头：**
```
Authorization: Bearer <access_token>
```

## 前端集成示例

### 微信小程序端

```javascript
// 1. 微信登录
wx.login({
  success: (res) => {
    if (res.code) {
      // 发送登录请求
      wx.request({
        url: 'https://your-api-domain.com/api/v1/wechat/login',
        method: 'POST',
        data: {
          code: res.code
        },
        success: (response) => {
          const { openId, accessToken, isNewUser } = response.data.data;
          // 保存token和openId
          wx.setStorageSync('access_token', accessToken);
          wx.setStorageSync('open_id', openId);
          wx.setStorageSync('is_new_user', isNewUser);

          console.log('Login successful:', { openId, isNewUser });
        }
      });
    }
  }
});

// 2. 获取手机号
wx.getPhoneNumber({
  success: (res) => {
    if (res.code) {
      wx.request({
        url: 'https://your-api-domain.com/api/v1/wechat/phone',
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('access_token')}`
        },
        data: {
          code: res.code
        },
        success: (response) => {
          const { openId, phoneUpdated } = response.data.data;
          // 确认手机号更新成功
          if (phoneUpdated) {
            console.log('Phone number updated for user:', openId);
            // 可以设置标记表示用户已完成手机号授权
            wx.setStorageSync('phone_authorized', true);
          }
        }
      });
    }
  }
});
```

## 数据库设计

用户表包含以下微信相关字段：

```sql
-- 微信相关字段
wechatOpenId VARCHAR(100) UNIQUE, -- 微信OpenID
wechatUnionId VARCHAR(100), -- 微信UnionID
wechatSessionKey VARCHAR(100), -- 微信会话密钥（加密存储）

-- 用户信息字段
nickname VARCHAR(100), -- 微信昵称
avatar VARCHAR(500), -- 头像URL
gender ENUM('male', 'female', 'unknown'), -- 性别
city VARCHAR(100), -- 城市
province VARCHAR(100), -- 省份
country VARCHAR(100), -- 国家
language VARCHAR(50), -- 语言
```

## 安全注意事项

1. **AppSecret 保护**：绝不要在前端代码中暴露 AppSecret
2. **Session Key 安全**：Session Key 应该加密存储，定期更新
3. **数据验证**：对所有微信返回的数据进行验证
4. **错误处理**：妥善处理微信API的各种错误情况
5. **日志记录**：记录关键操作日志，便于问题排查

## 错误处理

系统会处理以下微信API错误：

- `40013`: invalid appid
- `40014`: invalid access_token
- `40029`: invalid code
- `45011`: API 调用太频繁
- `40226`: 高风险等级用户，小程序登录拦截

## 测试

可以使用微信开发者工具进行测试：

1. 在微信开发者工具中配置服务器域名
2. 使用真实的微信小程序进行登录测试
3. 测试手机号授权功能

## 部署注意事项

1. 确保服务器域名已在微信小程序后台配置
2. 生产环境必须使用 HTTPS
3. 配置正确的 CORS 策略
4. 监控微信API调用频率，避免超限

## Logging and Monitoring

### Server-Side Logging

The API automatically logs all important events with proper data masking:

**Login Events:**
```json
{
  "level": "info",
  "message": "WeChat login successful",
  "openId": "wechat-openid",
  "userId": "internal-uuid",
  "isNewUser": true,
  "ip": "127.0.0.1",
  "userAgent": "WeChat Mini-Program",
  "loginTime": "2025-06-21T09:00:00.000Z"
}
```

**Phone Authorization Events:**
```json
{
  "level": "info",
  "message": "WeChat phone number obtained",
  "openId": "wechat-openid",
  "userId": "internal-uuid",
  "phone": "138****8000",
  "ip": "127.0.0.1",
  "timestamp": "2025-06-21T09:05:00.000Z"
}
```

### Data Privacy in Logs

- **Phone numbers are masked** in logs (e.g., `138****8000`)
- **Session keys are never logged**
- **IP addresses and user agents are recorded** for security monitoring
- **OpenID is used as primary identifier** for tracking user actions

## Security Considerations

1. **Token Security**: Store access tokens securely in WeChat storage
2. **HTTPS Only**: Always use HTTPS in production
3. **Token Expiration**: Handle token expiration gracefully
4. **Rate Limiting**: Implement rate limiting for login attempts
5. **Input Validation**: All inputs are validated server-side
6. **Data Minimization**: API returns only essential data (openId + status)
7. **Audit Trail**: Comprehensive logging for security monitoring

## 常见问题

### Q: 登录时提示 "invalid code"
A: 检查微信小程序的 AppID 和 AppSecret 是否正确配置

### Q: 获取手机号失败
A: 确保用户已授权手机号，且 access_token 有效

### Q: 用户信息解密失败
A: 检查 session_key 是否有效，数据是否完整

## Recent Fixes and Improvements

### Database Schema Fixes (v1.1.0)
- ✅ **Phone Field Optimization**: Changed `phone` field from `NOT NULL` to `NULL` to support WeChat login without immediate phone authorization
- ✅ **Gender Field Type Fix**: Changed `gender` from string enum to `tinyint(1)` to match database schema (0: unknown, 1: male, 2: female)
- ✅ **Unique Constraints**: Maintained unique constraints for phone numbers while allowing NULL values
- ✅ **Index Optimization**: Proper indexing for WeChat-related fields

### Code Improvements (v1.1.0)
- ✅ **Type Safety**: Fixed TypeScript type definitions for optional phone and gender fields
- ✅ **Error Handling**: Improved error handling for WeChat API responses
- ✅ **JWT Token Generation**: Fixed token generation for users without phone numbers
- ✅ **User Creation Flow**: Optimized user creation to only set essential fields during WeChat login
- ✅ **Data Mapping**: Fixed data mapping between database and application models

### API Enhancements (v1.1.0)
- ✅ **Minimal User Creation**: WeChat login now creates users with minimal required fields
- ✅ **Phone Authorization Separation**: Phone number collection is now a separate step after login
- ✅ **Better Error Messages**: More descriptive error messages for debugging
- ✅ **Logging Improvements**: Enhanced logging for better troubleshooting
- ✅ **OpenID-Based Response**: Login now returns only openId, isNewUser, and accessToken
- ✅ **Data Privacy**: Phone numbers are masked in logs, sensitive data minimized in responses
- ✅ **Security Enhancement**: Reduced data exposure in API responses

## Testing Status

### ✅ Verified Components
- Database connection and schema
- WeChat API integration
- User creation and update flows
- JWT token generation and validation
- Phone number authorization endpoints
- Error handling and logging
- Type safety and validation

### 🧪 Test Results
- **Database Schema**: All constraints properly configured
- **WeChat Login**: Successfully handles valid and invalid codes
- **User Creation**: Creates users with NULL phone field correctly
- **JWT Tokens**: Generated correctly for users with/without phone numbers
- **Phone Authorization**: Endpoints properly protected with authentication
- **Error Handling**: Appropriate error responses for all failure scenarios

## Changelog

- **v1.1.1** (2025-06-21): OpenID-based responses, enhanced logging, improved data privacy
- **v1.1.0** (2025-06-21): Major fixes for database schema, type safety, and user creation flow
- **v1.0.0**: Initial version with basic login and phone number functionality
