# 微信小程序配置管理 API 文档

## 概述

微信小程序配置管理系统提供了统一的配置管理功能，支持轮播图、公告、电话联系、复制微信号、一键分享等多种配置类型。

## 配置类型

- `CAROUSEL` - 轮播图配置
- `ANNOUNCEMENT` - 公告配置  
- `CONTACT_PHONE` - 电话联系配置
- `WECHAT_COPY` - 复制微信号配置
- `SHARE_CONFIG` - 一键分享配置
- `SYSTEM_CONFIG` - 系统配置

## API 接口

### 1. 获取有效配置（公开接口）

**GET** `/api/v1/miniprogram/configs/active`

获取当前有效的配置（考虑时间范围和启用状态），供小程序前端使用。

**查询参数：**
- `type` (可选): 配置类型
- `group` (可选): 配置分组

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "type": "CAROUSEL",
      "key": "carousel_1",
      "name": "首页轮播图1",
      "value": {
        "image_url": "https://example.com/carousel1.jpg",
        "title": "欢迎使用宝丰回收",
        "subtitle": "专业手机回收服务",
        "link_type": "page",
        "link_value": "/pages/home/<USER>",
        "background_color": "#FF6B6B"
      },
      "enabled": true,
      "order": 1,
      "group": "home_carousel"
    }
  ]
}
```

### 2. 创建配置（管理接口）

**POST** `/api/v1/miniprogram/configs`

**请求头：**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体示例：**
```json
{
  "config_type": "CAROUSEL",
  "config_key": "carousel_1",
  "config_name": "首页轮播图1",
  "config_value": {
    "image_url": "https://example.com/carousel1.jpg",
    "title": "欢迎使用宝丰回收",
    "subtitle": "专业手机回收服务",
    "link_type": "page",
    "link_value": "/pages/home/<USER>",
    "background_color": "#FF6B6B"
  },
  "display_order": 1,
  "group_name": "home_carousel",
  "description": "首页主要轮播图"
}
```

### 3. 获取配置列表（管理接口）

**GET** `/api/v1/miniprogram/configs`

**查询参数：**
- `config_type` (可选): 配置类型
- `group_name` (可选): 分组名称
- `is_enabled` (可选): 是否启用
- `page` (可选): 页码，默认1
- `limit` (可选): 每页数量，默认20
- `sort_by` (可选): 排序字段，默认display_order
- `sort_order` (可选): 排序方向，默认ASC

### 4. 更新配置（管理接口）

**PUT** `/api/v1/miniprogram/configs/:id`

**请求体示例：**
```json
{
  "config_name": "更新后的配置名称",
  "is_enabled": false,
  "description": "更新后的描述"
}
```

### 5. 批量更新配置（管理接口）

**POST** `/api/v1/miniprogram/configs/batch`

**请求体示例：**
```json
{
  "config_ids": [1, 2, 3],
  "updates": {
    "is_enabled": true,
    "group_name": "new_group"
  },
  "operation_reason": "批量启用配置"
}
```

## 配置值结构

### 轮播图配置 (CAROUSEL)
```json
{
  "image_url": "图片URL（必填）",
  "title": "标题（必填）",
  "subtitle": "副标题（可选）",
  "link_type": "链接类型：page/url/none（可选，默认none）",
  "link_value": "链接值（可选）",
  "background_color": "背景色（可选，格式：#RRGGBB）"
}
```

### 公告配置 (ANNOUNCEMENT)
```json
{
  "title": "公告标题（必填）",
  "content": "公告内容（必填）",
  "type": "公告类型：info/warning/success/error（可选，默认info）",
  "show_icon": "是否显示图标（可选，默认true）",
  "closable": "是否可关闭（可选，默认true）",
  "auto_close_time": "自动关闭时间，毫秒（可选）",
  "highlight": "是否高亮显示（可选，默认false）"
}
```

### 电话联系配置 (CONTACT_PHONE)
```json
{
  "phone_number": "电话号码（必填）",
  "display_name": "显示名称（必填）",
  "service_time": "服务时间（可选）",
  "description": "描述信息（可选）",
  "show_confirm": "是否显示确认对话框（可选，默认true）",
  "confirm_text": "确认对话框文本（可选）"
}
```

### 微信复制配置 (WECHAT_COPY)
```json
{
  "wechat_id": "微信号（必填）",
  "display_name": "显示名称（必填）",
  "qr_code_url": "二维码图片URL（可选）",
  "description": "描述信息（可选）",
  "copy_success_text": "复制成功提示文本（可选）",
  "show_qr_code": "是否显示二维码（可选，默认false）"
}
```

### 分享配置 (SHARE_CONFIG)
```json
{
  "title": "分享标题（必填）",
  "description": "分享描述（必填）",
  "image_url": "分享图片URL（可选）",
  "share_path": "分享页面路径（可选）",
  "enable_timeline": "是否启用朋友圈分享（可选，默认true）",
  "enable_session": "是否启用会话分享（可选，默认true）",
  "enable_qq": "是否启用QQ分享（可选，默认false）"
}
```

## 使用示例

### 小程序前端获取轮播图配置

```javascript
// 获取首页轮播图配置
wx.request({
  url: 'https://api.example.com/api/v1/miniprogram/configs/active',
  data: {
    type: 'CAROUSEL',
    group: 'home_carousel'
  },
  success: function(res) {
    if (res.data.success) {
      const carouselConfigs = res.data.data;
      // 渲染轮播图
      this.setData({
        carouselList: carouselConfigs.map(config => config.value)
      });
    }
  }
});
```

### 小程序前端获取公告配置

```javascript
// 获取系统公告
wx.request({
  url: 'https://api.example.com/api/v1/miniprogram/configs/active',
  data: {
    type: 'ANNOUNCEMENT'
  },
  success: function(res) {
    if (res.data.success && res.data.data.length > 0) {
      const announcement = res.data.data[0].value;
      // 显示公告
      wx.showModal({
        title: announcement.title,
        content: announcement.content,
        showCancel: announcement.closable
      });
    }
  }
});
```

### 小程序前端实现一键拨号

```javascript
// 获取客服电话配置并拨号
wx.request({
  url: 'https://api.example.com/api/v1/miniprogram/configs/active',
  data: {
    type: 'CONTACT_PHONE',
    group: 'contact_info'
  },
  success: function(res) {
    if (res.data.success && res.data.data.length > 0) {
      const phoneConfig = res.data.data[0].value;
      
      if (phoneConfig.show_confirm) {
        wx.showModal({
          title: '拨打电话',
          content: phoneConfig.confirm_text || `确定要拨打${phoneConfig.display_name}吗？`,
          success: function(modalRes) {
            if (modalRes.confirm) {
              wx.makePhoneCall({
                phoneNumber: phoneConfig.phone_number
              });
            }
          }
        });
      } else {
        wx.makePhoneCall({
          phoneNumber: phoneConfig.phone_number
        });
      }
    }
  }
});
```

### 小程序前端实现微信号复制

```javascript
// 获取微信号配置并复制
wx.request({
  url: 'https://api.example.com/api/v1/miniprogram/configs/active',
  data: {
    type: 'WECHAT_COPY'
  },
  success: function(res) {
    if (res.data.success && res.data.data.length > 0) {
      const wechatConfig = res.data.data[0].value;
      
      wx.setClipboardData({
        data: wechatConfig.wechat_id,
        success: function() {
          wx.showToast({
            title: wechatConfig.copy_success_text || '微信号已复制',
            icon: 'success'
          });
        }
      });
    }
  }
});
```

## 错误码说明

- `400` - 请求参数错误
- `401` - 未授权访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 注意事项

1. 公开接口 `/active` 不需要认证，供小程序前端直接调用
2. 管理接口需要Bearer Token认证
3. 配置的时间范围控制：`start_time` 和 `end_time` 可以控制配置的生效时间
4. 配置的启用状态：`is_enabled` 控制配置是否启用
5. 建议使用配置分组 `group_name` 来组织相关配置
6. 配置键名 `config_key` 在同一类型下必须唯一
