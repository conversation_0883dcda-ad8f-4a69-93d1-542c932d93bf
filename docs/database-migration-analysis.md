# 数据库迁移分析报告

## 概述
将所有数据表统一迁移到 `baofeng_recycle` 数据库，停止使用 `baofeng_admin` 数据库。

## 当前数据库状态

### baofeng_admin 数据库表
| 表名 | 记录数 | 数据大小 | 说明 |
|------|--------|----------|------|
| admins | 2 | 16KB | 管理员账户 |
| contact_access_logs | 0 | 16KB | 联系人访问日志 |
| contact_share_configs | 0 | 16KB | 联系人分享配置 |
| miniprogram_config_logs | 0 | 16KB | 小程序配置日志 |
| miniprogram_configs | 12 | 16KB | 小程序配置 |
| miniprogram_contacts | 2 | 16KB | 小程序联系人 |
| operation_logs | 2 | 16KB | 操作日志 |
| sessions | 0 | 16KB | 会话信息 |
| users | 4 | 16KB | 用户信息 |

### baofeng_recycle 数据库表
| 表名 | 记录数 | 数据大小 | 说明 |
|------|--------|----------|------|
| admins | 0 | 16KB | 管理员账户（空表） |
| customer_contact_logs | 0 | 16KB | 客户联系日志 |
| memory_specs | 4032 | 262KB | 内存规格 |
| operation_logs | 30 | 16KB | 操作日志 |
| phone_categories | 1607 | 1.5MB | 手机分类 |
| price_history | 23503 | 2.6MB | 价格历史 |
| price_tag_applications | 0 | 16KB | 价格标签应用 |
| price_tag_rules | 4 | 16KB | 价格标签规则 |
| price_tags | 25476 | 3.5MB | 价格标签 |
| price_trend_history | 0 | 16KB | 价格趋势历史 |
| price_trend_rankings | 4 | 16KB | 价格趋势排名 |
| recycle_employees | 0 | 16KB | 回收员工 |
| recycle_stores | 2 | 16KB | 回收门店 |
| store_contact_configs | 0 | 16KB | 门店联系配置 |
| sync_records | 0 | 16KB | 同步记录 |

## 表结构差异分析

### 1. 重复表分析

#### admins 表
- **结构**: 两个数据库中结构完全相同
- **数据**: baofeng_admin 有2条记录，baofeng_recycle 为空
- **处理**: 将 baofeng_admin 中的数据迁移到 baofeng_recycle

#### operation_logs 表
- **结构差异**: 
  - baofeng_admin: 面向用户和管理员操作日志，字段更详细
  - baofeng_recycle: 面向业务操作日志，字段更简化
- **数据**: 两个库都有数据
- **处理**: 保留两个表的数据，统一使用 baofeng_recycle 的结构

### 2. 需要迁移的表

#### 从 baofeng_admin 迁移到 baofeng_recycle:
1. **admins** - 管理员数据（2条记录）
2. **users** - 用户数据（4条记录）
3. **miniprogram_configs** - 小程序配置（12条记录）
4. **miniprogram_contacts** - 小程序联系人（2条记录）
5. **contact_access_logs** - 联系人访问日志（空表，但需要表结构）
6. **contact_share_configs** - 联系人分享配置（空表，但需要表结构）
7. **miniprogram_config_logs** - 小程序配置日志（空表，但需要表结构）
8. **sessions** - 会话信息（空表，但需要表结构）

## 迁移策略

### 阶段1: 数据备份
- 备份 baofeng_admin 数据库
- 备份 baofeng_recycle 数据库

### 阶段2: 表结构创建
- 在 baofeng_recycle 中创建缺失的表结构
- 处理 operation_logs 表的结构差异

### 阶段3: 数据迁移
- 迁移 admins 表数据
- 迁移 users 表数据
- 迁移微信小程序相关表数据
- 合并 operation_logs 数据

### 阶段4: 应用配置更新
- 修改 .env 文件，将 DB_NAME 改为 baofeng_recycle
- 更新 knex 配置，移除 recycleDb
- 更新所有控制器中的数据库连接

### 阶段5: 测试验证
- 测试所有 API 接口
- 验证数据完整性
- 确认功能正常

## 风险评估

### 高风险项
1. **operation_logs 表结构不同** - 需要数据转换
2. **现有数据丢失** - 需要完整备份

### 中风险项
1. **应用配置错误** - 可能导致服务无法启动
2. **数据库连接问题** - 需要彻底测试

### 低风险项
1. **空表迁移** - 只需要表结构，无数据风险

## 回滚计划

如果迁移失败：
1. 恢复原始数据库备份
2. 恢复原始应用配置
3. 重启服务

## 预计时间

- 备份: 10分钟
- 表结构创建: 15分钟
- 数据迁移: 20分钟
- 配置更新: 15分钟
- 测试验证: 30分钟
- **总计: 约90分钟**
