# 微信小程序配置管理系统

## 📋 系统概述

微信小程序配置管理系统是一个统一的配置管理解决方案，支持轮播图、公告、电话联系、复制微信号、一键分享等多种配置类型。系统采用单表设计，通过JSON字段存储不同类型的配置数据，具有高度的灵活性和扩展性。

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端小程序     │    │   管理后台       │    │   数据库         │
│                │    │                │    │                │
│ - 轮播图展示     │    │ - 配置管理       │    │ - 配置表         │
│ - 公告显示       │◄──►│ - 批量操作       │◄──►│ - 操作日志       │
│ - 电话拨打       │    │ - 统计分析       │    │ - 索引优化       │
│ - 微信复制       │    │ - 权限控制       │    │                │
│ - 分享功能       │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈

- **后端**: Node.js + TypeScript + Express
- **数据库**: MySQL 8.0+
- **验证**: Joi
- **认证**: JWT
- **文档**: Swagger
- **日志**: Winston

## 📊 数据库设计

### 主配置表 (miniprogram_configs)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键ID |
| config_type | ENUM | 配置类型 |
| config_key | VARCHAR(100) | 配置键名 |
| config_name | VARCHAR(200) | 配置名称 |
| config_value | JSON | 配置值 |
| display_order | INT | 显示顺序 |
| is_enabled | TINYINT(1) | 是否启用 |
| start_time | DATETIME | 生效开始时间 |
| end_time | DATETIME | 生效结束时间 |
| group_name | VARCHAR(100) | 分组名称 |
| tags | JSON | 标签数组 |
| description | TEXT | 配置描述 |
| extra_data | JSON | 扩展数据 |
| created_by | VARCHAR(50) | 创建者 |
| updated_by | VARCHAR(50) | 更新者 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 操作日志表 (miniprogram_config_logs)

记录所有配置的操作历史，包括创建、更新、删除、启用、禁用等操作。

## 🎯 配置类型

### 1. 轮播图配置 (CAROUSEL)
```json
{
  "image_url": "图片URL",
  "title": "标题",
  "subtitle": "副标题",
  "link_type": "链接类型(page/url/none)",
  "link_value": "链接值",
  "background_color": "背景色"
}
```

### 2. 公告配置 (ANNOUNCEMENT)
```json
{
  "title": "公告标题",
  "content": "公告内容",
  "type": "公告类型(info/warning/success/error)",
  "show_icon": "是否显示图标",
  "closable": "是否可关闭",
  "auto_close_time": "自动关闭时间(毫秒)",
  "highlight": "是否高亮显示"
}
```

### 3. 电话联系配置 (CONTACT_PHONE)
```json
{
  "phone_number": "电话号码",
  "display_name": "显示名称",
  "service_time": "服务时间",
  "description": "描述信息",
  "show_confirm": "是否显示确认对话框",
  "confirm_text": "确认对话框文本"
}
```

### 4. 微信复制配置 (WECHAT_COPY)
```json
{
  "wechat_id": "微信号",
  "display_name": "显示名称",
  "qr_code_url": "二维码图片URL",
  "description": "描述信息",
  "copy_success_text": "复制成功提示文本",
  "show_qr_code": "是否显示二维码"
}
```

### 5. 分享配置 (SHARE_CONFIG)
```json
{
  "title": "分享标题",
  "description": "分享描述",
  "image_url": "分享图片URL",
  "share_path": "分享页面路径",
  "enable_timeline": "是否启用朋友圈分享",
  "enable_session": "是否启用会话分享",
  "enable_qq": "是否启用QQ分享"
}
```

### 6. 系统配置 (SYSTEM_CONFIG)
灵活的键值对配置，支持任意JSON结构。

## 🚀 API 接口

### 公开接口（小程序使用）

- `GET /api/v1/miniprogram/configs/active` - 获取有效配置

### 管理接口（需要认证）

- `POST /api/v1/miniprogram/configs` - 创建配置
- `GET /api/v1/miniprogram/configs` - 获取配置列表
- `GET /api/v1/miniprogram/configs/:id` - 获取配置详情
- `PUT /api/v1/miniprogram/configs/:id` - 更新配置
- `DELETE /api/v1/miniprogram/configs/:id` - 删除配置
- `GET /api/v1/miniprogram/configs/type/:type` - 按类型获取配置
- `GET /api/v1/miniprogram/configs/group/:group` - 按分组获取配置
- `POST /api/v1/miniprogram/configs/batch` - 批量更新配置
- `GET /api/v1/miniprogram/configs/statistics` - 获取统计信息
- `GET /api/v1/miniprogram/configs/groups` - 获取分组信息

## 📱 小程序集成示例

### 获取轮播图配置
```javascript
// 获取首页轮播图
wx.request({
  url: 'https://api.example.com/api/v1/miniprogram/configs/active',
  data: { type: 'CAROUSEL', group: 'home_carousel' },
  success: (res) => {
    if (res.data.success) {
      this.setData({
        carouselList: res.data.data.map(config => config.value)
      });
    }
  }
});
```

### 显示系统公告
```javascript
// 获取并显示公告
wx.request({
  url: 'https://api.example.com/api/v1/miniprogram/configs/active',
  data: { type: 'ANNOUNCEMENT' },
  success: (res) => {
    if (res.data.success && res.data.data.length > 0) {
      const announcement = res.data.data[0].value;
      wx.showModal({
        title: announcement.title,
        content: announcement.content,
        showCancel: announcement.closable
      });
    }
  }
});
```

### 一键拨号功能
```javascript
// 获取客服电话并拨号
const callCustomerService = () => {
  wx.request({
    url: 'https://api.example.com/api/v1/miniprogram/configs/active',
    data: { type: 'CONTACT_PHONE', group: 'customer_service' },
    success: (res) => {
      if (res.data.success && res.data.data.length > 0) {
        const phoneConfig = res.data.data[0].value;
        
        if (phoneConfig.show_confirm) {
          wx.showModal({
            title: '拨打电话',
            content: phoneConfig.confirm_text,
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.makePhoneCall({
                  phoneNumber: phoneConfig.phone_number
                });
              }
            }
          });
        } else {
          wx.makePhoneCall({
            phoneNumber: phoneConfig.phone_number
          });
        }
      }
    }
  });
};
```

### 微信号复制功能
```javascript
// 复制微信号
const copyWechatId = () => {
  wx.request({
    url: 'https://api.example.com/api/v1/miniprogram/configs/active',
    data: { type: 'WECHAT_COPY' },
    success: (res) => {
      if (res.data.success && res.data.data.length > 0) {
        const wechatConfig = res.data.data[0].value;
        
        wx.setClipboardData({
          data: wechatConfig.wechat_id,
          success: () => {
            wx.showToast({
              title: wechatConfig.copy_success_text || '微信号已复制',
              icon: 'success'
            });
          }
        });
      }
    }
  });
};
```

## 🔧 部署和使用

### 1. 数据库初始化
```bash
# 执行SQL脚本创建表结构
mysql -u root -p baofeng_admin < docs/database/miniprogram-config-schema.sql
```

### 2. 启动服务
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 或启动生产服务器
npm run build
npm start
```

### 3. 测试系统
```bash
# 运行测试脚本
node scripts/test-miniprogram-config.js
```

## 🛡️ 安全特性

- **认证授权**: JWT Token认证，支持角色权限控制
- **输入验证**: 使用Joi进行严格的参数验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输入输出过滤
- **CORS配置**: 跨域请求控制
- **限流保护**: API请求频率限制

## 📈 性能优化

- **数据库索引**: 针对查询字段建立合适索引
- **缓存策略**: 可集成Redis缓存热点配置
- **分页查询**: 支持分页避免大量数据查询
- **连接池**: 数据库连接池管理
- **压缩传输**: Gzip压缩响应数据

## 🔍 监控和日志

- **操作日志**: 记录所有配置变更操作
- **错误日志**: 详细的错误信息和堆栈跟踪
- **性能监控**: API响应时间和成功率统计
- **健康检查**: 系统健康状态监控

## 🚀 扩展功能

### 计划中的功能
- [ ] 配置版本管理
- [ ] 配置模板功能
- [ ] 配置导入导出
- [ ] 配置审批流程
- [ ] 多环境配置管理
- [ ] 配置变更通知
- [ ] 配置回滚功能

### 自定义配置类型
系统支持轻松添加新的配置类型：

1. 在`ConfigType`枚举中添加新类型
2. 定义对应的配置值接口
3. 添加验证规则
4. 更新API文档

## 📞 技术支持

如有问题或建议，请联系开发团队：
- 邮箱: <EMAIL>
- 微信: baofeng_tech
- 文档: [API文档](docs/miniprogram-config-api.md)
