-- 微信小程序配置表结构设计
-- 创建时间: 2025-06-22
-- 功能: 统一管理轮播图、公告、电话联系、复制微信号、一键分享等配置

USE baofeng_admin;

-- 微信小程序配置表
CREATE TABLE miniprogram_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    
    -- 配置基本信息
    config_type ENUM(
        'CAROUSEL',      -- 轮播图
        'ANNOUNCEMENT',  -- 公告
        'CONTACT_PHONE', -- 电话联系
        'WECHAT_COPY',   -- 复制微信号
        'SHARE_CONFIG',  -- 一键分享配置
        'SYSTEM_CONFIG'  -- 系统配置
    ) NOT NULL COMMENT '配置类型',
    
    config_key VARCHAR(100) NOT NULL COMMENT '配置键名，如：carousel_1, announcement_main',
    config_name VARCHAR(200) NOT NULL COMMENT '配置名称，如：首页轮播图1, 主要公告',
    
    -- 配置内容 (JSON格式，支持不同类型的配置)
    config_value JSON NOT NULL COMMENT '配置值，JSON格式存储不同类型的配置数据',
    
    -- 显示和排序
    display_order INT DEFAULT 0 COMMENT '显示顺序，数字越小越靠前',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用：1-启用，0-禁用',
    
    -- 生效时间控制
    start_time DATETIME DEFAULT NULL COMMENT '生效开始时间，NULL表示立即生效',
    end_time DATETIME DEFAULT NULL COMMENT '生效结束时间，NULL表示永久有效',
    
    -- 分组和标签
    group_name VARCHAR(100) DEFAULT NULL COMMENT '分组名称，用于配置分类管理',
    tags JSON DEFAULT NULL COMMENT '标签数组，用于配置筛选和管理',
    
    -- 扩展字段
    description TEXT COMMENT '配置描述',
    extra_data JSON DEFAULT NULL COMMENT '扩展数据，存储额外的配置信息',
    
    -- 操作记录
    created_by VARCHAR(50) DEFAULT 'system' COMMENT '创建者',
    updated_by VARCHAR(50) DEFAULT 'system' COMMENT '更新者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_config_type_key (config_type, config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_display_order (display_order),
    INDEX idx_group_name (group_name),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信小程序配置表';

-- 插入示例配置数据

-- 1. 轮播图配置
INSERT INTO miniprogram_configs (config_type, config_key, config_name, config_value, display_order, group_name, description) VALUES
('CAROUSEL', 'carousel_1', '首页轮播图1', JSON_OBJECT(
    'image_url', 'https://example.com/carousel1.jpg',
    'title', '欢迎使用宝丰回收',
    'subtitle', '专业手机回收服务',
    'link_type', 'page', -- page, url, none
    'link_value', '/pages/home/<USER>',
    'background_color', '#FF6B6B'
), 1, 'home_carousel', '首页主要轮播图'),

('CAROUSEL', 'carousel_2', '首页轮播图2', JSON_OBJECT(
    'image_url', 'https://example.com/carousel2.jpg',
    'title', '高价回收',
    'subtitle', '价格透明，服务专业',
    'link_type', 'page',
    'link_value', '/pages/recycle/index',
    'background_color', '#4ECDC4'
), 2, 'home_carousel', '首页第二张轮播图'),

-- 2. 公告配置
('ANNOUNCEMENT', 'main_notice', '主要公告', JSON_OBJECT(
    'title', '系统维护通知',
    'content', '系统将于今晚22:00-24:00进行维护，期间可能影响部分功能使用，敬请谅解。',
    'type', 'info', -- info, warning, success, error
    'show_icon', true,
    'closable', true,
    'auto_close_time', 5000
), 1, 'system_notice', '系统主要公告'),

('ANNOUNCEMENT', 'promotion_notice', '促销公告', JSON_OBJECT(
    'title', '限时优惠',
    'content', '本月回收价格上调10%，机会难得，快来回收吧！',
    'type', 'success',
    'show_icon', true,
    'closable', true,
    'highlight', true
), 2, 'promotion_notice', '促销活动公告'),

-- 3. 电话联系配置
('CONTACT_PHONE', 'customer_service', '客服电话', JSON_OBJECT(
    'phone_number', '************',
    'display_name', '客服热线',
    'service_time', '9:00-18:00',
    'description', '专业客服为您服务',
    'show_confirm', true,
    'confirm_text', '确定要拨打客服电话吗？'
), 1, 'contact_info', '客服联系电话'),

('CONTACT_PHONE', 'business_phone', '商务合作', JSON_OBJECT(
    'phone_number', '138-0000-1234',
    'display_name', '商务合作',
    'service_time', '工作日 9:00-17:30',
    'description', '商务合作洽谈',
    'show_confirm', true,
    'confirm_text', '确定要拨打商务电话吗？'
), 2, 'contact_info', '商务合作电话'),

-- 4. 微信号复制配置
('WECHAT_COPY', 'customer_wechat', '客服微信', JSON_OBJECT(
    'wechat_id', 'baofeng_service',
    'display_name', '客服微信',
    'qr_code_url', 'https://example.com/wechat_qr.jpg',
    'description', '添加客服微信，获得更好服务',
    'copy_success_text', '微信号已复制，请到微信添加好友',
    'show_qr_code', true
), 1, 'wechat_contact', '客服微信号'),

('WECHAT_COPY', 'business_wechat', '商务微信', JSON_OBJECT(
    'wechat_id', 'baofeng_business',
    'display_name', '商务合作微信',
    'qr_code_url', 'https://example.com/business_qr.jpg',
    'description', '商务合作请添加此微信',
    'copy_success_text', '商务微信号已复制',
    'show_qr_code', true
), 2, 'wechat_contact', '商务合作微信号'),

-- 5. 一键分享配置
('SHARE_CONFIG', 'app_share', '应用分享', JSON_OBJECT(
    'title', '宝丰回收 - 专业手机回收平台',
    'description', '高价回收，价格透明，服务专业，值得信赖的手机回收平台',
    'image_url', 'https://example.com/share_logo.jpg',
    'share_path', '/pages/home/<USER>',
    'enable_timeline', true,
    'enable_session', true,
    'enable_qq', false
), 1, 'share_settings', '应用分享配置'),

('SHARE_CONFIG', 'price_share', '价格分享', JSON_OBJECT(
    'title', '手机回收价格查询',
    'description', '实时更新的手机回收价格，快来查看你的手机值多少钱',
    'image_url', 'https://example.com/price_share.jpg',
    'share_path', '/pages/price/index',
    'enable_timeline', true,
    'enable_session', true
), 2, 'share_settings', '价格查询分享配置'),

-- 6. 系统配置
('SYSTEM_CONFIG', 'app_info', '应用信息', JSON_OBJECT(
    'app_name', '宝丰回收',
    'app_version', '1.0.0',
    'company_name', '宝丰科技有限公司',
    'copyright', '© 2025 宝丰科技有限公司',
    'privacy_url', 'https://example.com/privacy',
    'terms_url', 'https://example.com/terms'
), 1, 'app_settings', '应用基本信息'),

('SYSTEM_CONFIG', 'feature_switches', '功能开关', JSON_OBJECT(
    'enable_price_trend', true,
    'enable_user_feedback', true,
    'enable_location_service', false,
    'enable_push_notification', true,
    'maintenance_mode', false,
    'maintenance_message', '系统维护中，请稍后再试'
), 1, 'app_settings', '功能开关配置');

-- 创建配置操作日志表（可选）
CREATE TABLE miniprogram_config_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_id INT NOT NULL COMMENT '配置ID',
    operation_type ENUM('CREATE', 'UPDATE', 'DELETE', 'ENABLE', 'DISABLE') NOT NULL COMMENT '操作类型',
    old_value JSON COMMENT '修改前的值',
    new_value JSON COMMENT '修改后的值',
    operator VARCHAR(50) NOT NULL COMMENT '操作者',
    operation_reason VARCHAR(255) COMMENT '操作原因',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (config_id) REFERENCES miniprogram_configs(id) ON DELETE CASCADE,
    INDEX idx_config_id (config_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operator (operator),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小程序配置操作日志表';

-- 插入操作日志（适配现有表结构）
INSERT INTO operation_logs (user_type, action, resource, details, ip_address, created_at) VALUES
('admin', 'TABLE_CREATE', 'miniprogram_configs', '{"action": "create_miniprogram_config_tables", "tables": ["miniprogram_configs", "miniprogram_config_logs"], "timestamp": "2025-06-22"}', '127.0.0.1', NOW()),
('admin', 'SYSTEM_READY', 'miniprogram_system', '{"message": "微信小程序配置表结构创建完成", "timestamp": "2025-06-22"}', '127.0.0.1', NOW());

-- 显示创建结果
SELECT '微信小程序配置表结构创建完成！' as message;
SHOW TABLES LIKE '%miniprogram%';
