-- 回收业务联系信息相关表结构设计
-- 创建时间: 2025-06-22
-- 基于图片中的回收业务联系信息需求设计

USE baofeng_recycle;

-- 1. 回收站点/门店表
CREATE TABLE recycle_stores (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '门店ID',
    store_code VARCHAR(20) UNIQUE NOT NULL COMMENT '门店编码',
    store_name VARCHAR(200) NOT NULL COMMENT '门店名称',
    
    -- 地址信息
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    detailed_address TEXT NOT NULL COMMENT '详细地址',
    full_address TEXT NOT NULL COMMENT '完整地址',
    longitude DECIMAL(10,7) COMMENT '经度',
    latitude DECIMAL(10,7) COMMENT '纬度',
    
    -- 联系信息
    sales_phone VARCHAR(20) COMMENT '销售电话',
    sales_wechat VARCHAR(100) COMMENT '销售微信',
    recycle_phone VARCHAR(20) COMMENT '回收电话',
    recycle_wechat VARCHAR(100) COMMENT '回收微信',
    main_phone VARCHAR(20) NOT NULL COMMENT '主要联系电话',
    email VARCHAR(100) COMMENT '邮箱地址',
    
    -- 营业信息
    business_hours JSON COMMENT '营业时间，格式：{"monday": "09:00-18:00", "tuesday": "09:00-18:00", ...}',
    business_status ENUM('OPEN', 'CLOSED', 'MAINTENANCE') DEFAULT 'OPEN' COMMENT '营业状态',
    
    -- 门店信息
    store_type ENUM('FLAGSHIP', 'STANDARD', 'MINI', 'ONLINE') DEFAULT 'STANDARD' COMMENT '门店类型',
    store_area DECIMAL(8,2) COMMENT '门店面积（平方米）',
    employee_count INT DEFAULT 0 COMMENT '员工数量',
    
    -- 服务范围
    service_radius INT DEFAULT 5 COMMENT '服务半径（公里）',
    supported_categories JSON COMMENT '支持的回收类别ID数组',
    
    -- 系统字段
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_store_code (store_code),
    INDEX idx_province_city (province, city),
    INDEX idx_district (district),
    INDEX idx_business_status (business_status),
    INDEX idx_store_type (store_type),
    INDEX idx_status (status),
    INDEX idx_location (longitude, latitude),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回收站点/门店表';

-- 2. 回收员工表
CREATE TABLE recycle_employees (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '员工ID',
    employee_code VARCHAR(20) UNIQUE NOT NULL COMMENT '员工编号',
    store_id INT NOT NULL COMMENT '所属门店ID',
    
    -- 基本信息
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    wechat_id VARCHAR(100) COMMENT '微信号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    
    -- 职位信息
    position ENUM('MANAGER', 'SALES', 'RECYCLER', 'APPRAISER', 'CUSTOMER_SERVICE', 'OTHER') NOT NULL COMMENT '职位',
    department VARCHAR(50) COMMENT '部门',
    level TINYINT DEFAULT 1 COMMENT '级别：1-初级，2-中级，3-高级，4-专家',
    
    -- 工作信息
    hire_date DATE NOT NULL COMMENT '入职日期',
    work_status ENUM('ACTIVE', 'INACTIVE', 'LEAVE', 'RESIGNED') DEFAULT 'ACTIVE' COMMENT '工作状态',
    specialties JSON COMMENT '专业技能，如：["iPhone评估", "Android回收", "价格谈判"]',
    
    -- 联系偏好
    preferred_contact_method ENUM('PHONE', 'WECHAT', 'EMAIL') DEFAULT 'PHONE' COMMENT '首选联系方式',
    work_hours JSON COMMENT '工作时间安排',
    
    -- 系统字段
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (store_id) REFERENCES recycle_stores(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_employee_code (employee_code),
    INDEX idx_store_id (store_id),
    INDEX idx_phone (phone),
    INDEX idx_wechat_id (wechat_id),
    INDEX idx_position (position),
    INDEX idx_work_status (work_status),
    INDEX idx_status (status),
    INDEX idx_hire_date (hire_date),
    
    -- 复合索引
    INDEX idx_store_position (store_id, position),
    INDEX idx_store_status (store_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回收员工表';

-- 3. 客户联系记录表
CREATE TABLE customer_contact_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    store_id INT NOT NULL COMMENT '门店ID',
    employee_id INT COMMENT '员工ID',
    
    -- 客户信息
    customer_name VARCHAR(50) COMMENT '客户姓名',
    customer_phone VARCHAR(20) NOT NULL COMMENT '客户电话',
    customer_wechat VARCHAR(100) COMMENT '客户微信',
    
    -- 联系信息
    contact_type ENUM('SALES_INQUIRY', 'RECYCLE_INQUIRY', 'COMPLAINT', 'FOLLOW_UP', 'OTHER') NOT NULL COMMENT '联系类型',
    contact_method ENUM('PHONE', 'WECHAT', 'IN_PERSON', 'EMAIL', 'ONLINE') NOT NULL COMMENT '联系方式',
    contact_direction ENUM('INCOMING', 'OUTGOING') NOT NULL COMMENT '联系方向：来电/去电',
    
    -- 联系内容
    inquiry_category_ids JSON COMMENT '咨询的回收类别ID数组',
    contact_content TEXT COMMENT '联系内容',
    customer_needs TEXT COMMENT '客户需求',
    quoted_price DECIMAL(10,2) COMMENT '报价',
    
    -- 处理结果
    result_status ENUM('PENDING', 'COMPLETED', 'FOLLOW_UP_NEEDED', 'REJECTED') DEFAULT 'PENDING' COMMENT '处理状态',
    next_follow_up_at TIMESTAMP NULL COMMENT '下次跟进时间',
    notes TEXT COMMENT '备注信息',
    
    -- 系统字段
    contact_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '联系时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (store_id) REFERENCES recycle_stores(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES recycle_employees(id) ON DELETE SET NULL,
    
    -- 索引
    INDEX idx_store_id (store_id),
    INDEX idx_employee_id (employee_id),
    INDEX idx_customer_phone (customer_phone),
    INDEX idx_contact_type (contact_type),
    INDEX idx_contact_method (contact_method),
    INDEX idx_result_status (result_status),
    INDEX idx_contact_at (contact_at),
    INDEX idx_next_follow_up_at (next_follow_up_at),
    
    -- 复合索引
    INDEX idx_store_contact_type (store_id, contact_type),
    INDEX idx_employee_contact_date (employee_id, contact_at),
    INDEX idx_customer_phone_date (customer_phone, contact_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户联系记录表';

-- 4. 门店联系信息配置表
CREATE TABLE store_contact_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    store_id INT NOT NULL COMMENT '门店ID',
    
    -- 显示配置
    display_name VARCHAR(200) NOT NULL COMMENT '显示名称',
    display_logo_url VARCHAR(500) COMMENT '显示Logo URL',
    display_description TEXT COMMENT '门店描述',
    
    -- 联系信息显示设置
    show_sales_contact BOOLEAN DEFAULT TRUE COMMENT '是否显示销售联系方式',
    show_recycle_contact BOOLEAN DEFAULT TRUE COMMENT '是否显示回收联系方式',
    show_address BOOLEAN DEFAULT TRUE COMMENT '是否显示地址',
    show_business_hours BOOLEAN DEFAULT TRUE COMMENT '是否显示营业时间',
    
    -- 二维码配置
    sales_qr_code_url VARCHAR(500) COMMENT '销售微信二维码URL',
    recycle_qr_code_url VARCHAR(500) COMMENT '回收微信二维码URL',
    store_qr_code_url VARCHAR(500) COMMENT '门店二维码URL',
    
    -- 自定义信息
    custom_message TEXT COMMENT '自定义消息',
    welcome_message TEXT COMMENT '欢迎消息',
    service_promise TEXT COMMENT '服务承诺',
    
    -- 系统字段
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (store_id) REFERENCES recycle_stores(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_store_id (store_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='门店联系信息配置表';

-- 插入示例数据
INSERT INTO recycle_stores (
    store_code, store_name, province, city, district, detailed_address, full_address,
    sales_phone, sales_wechat, recycle_phone, recycle_wechat, main_phone,
    business_status, store_type
) VALUES (
    'CD001', '宝丰回收成都青羊店', '四川省', '成都市', '青羊区', 
    '赛格广场一楼5032-5033号', '四川省成都市青羊区赛格广场一楼5032-5033号',
    '***********', '***********', '***********', '***********', '***********',
    'OPEN', 'STANDARD'
);

-- 插入操作日志
INSERT INTO operation_logs (operation_type, operation_data, operator_id, operator_name) VALUES
('TABLE_CREATE', '{"action": "create_recycle_business_tables", "tables": ["recycle_stores", "recycle_employees", "customer_contact_logs", "store_contact_configs"], "timestamp": "2025-06-22"}', 'system', '系统创建'),
('SYSTEM_READY', '{"message": "回收业务联系信息表结构创建完成", "timestamp": "2025-06-22"}', 'system', '系统创建');

-- 显示创建结果
SELECT '回收业务联系信息相关表结构创建完成！' as message;
SHOW TABLES LIKE '%recycle%';
