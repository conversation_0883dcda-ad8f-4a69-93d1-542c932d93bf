-- 小程序联系人相关表结构设计
-- 创建时间: 2025-06-21
-- 更新时间: 2025-06-21

USE baofeng_admin;

-- 1. 小程序联系人表
CREATE TABLE miniprogram_contacts (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '联系人ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID，关联users表',

    -- 基本信息
    name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号码',
    wechat_id VARCHAR(100) COMMENT '微信号',
    avatar_url VARCHAR(500) COMMENT '头像URL',

    -- 分类和标签
    category ENUM('FRIEND', 'FAMILY', 'COLLEAGUE', 'BUSINESS', 'OTHER') DEFAULT 'FRIEND' COMMENT '联系人分类',
    tags JSON COMMENT '标签数组，如：["重要客户", "同事", "朋友"]',

    -- 扩展信息
    company VARCHAR(200) COMMENT '公司名称',
    position VARCHAR(100) COMMENT '职位',
    email VARCHAR(100) COMMENT '邮箱地址',
    address TEXT COMMENT '地址',
    birthday DATE COMMENT '生日',
    notes TEXT COMMENT '备注信息',

    -- 联系频率和重要性
    contact_frequency ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'RARELY', 'NEVER') DEFAULT 'RARELY' COMMENT '联系频率',
    importance_level TINYINT DEFAULT 3 COMMENT '重要程度：1-5，5最重要',
    is_favorite BOOLEAN DEFAULT FALSE COMMENT '是否收藏',

    -- 最后联系信息
    last_contact_at TIMESTAMP NULL COMMENT '最后联系时间',
    last_contact_type ENUM('CALL', 'MESSAGE', 'WECHAT', 'EMAIL', 'MEETING', 'OTHER') COMMENT '最后联系方式',
    contact_count INT DEFAULT 0 COMMENT '联系次数统计',

    -- 隐私和权限
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开（可被其他用户搜索）',
    share_phone BOOLEAN DEFAULT FALSE COMMENT '是否允许分享手机号',
    share_wechat BOOLEAN DEFAULT FALSE COMMENT '是否允许分享微信号',
    
    -- 系统字段
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-已删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_phone (phone),
    INDEX idx_wechat_id (wechat_id),
    INDEX idx_category (category),
    INDEX idx_importance_level (importance_level),
    INDEX idx_is_favorite (is_favorite),
    INDEX idx_is_public (is_public),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_last_contact_at (last_contact_at),
    
    -- 复合索引
    INDEX idx_user_category (user_id, category),
    INDEX idx_user_status (user_id, status),
    INDEX idx_public_status (is_public, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小程序联系人表';

-- 2. 联系人分享配置表
CREATE TABLE contact_share_configs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    contact_id INT NOT NULL COMMENT '联系人ID',
    
    -- 分享设置
    share_type ENUM('QR_CODE', 'LINK', 'CARD', 'MINI_PROGRAM') NOT NULL COMMENT '分享类型',
    share_title VARCHAR(200) COMMENT '分享标题',
    share_description TEXT COMMENT '分享描述',
    share_image_url VARCHAR(500) COMMENT '分享图片URL',
    
    -- 分享权限
    share_scope ENUM('PUBLIC', 'FRIENDS', 'PRIVATE') DEFAULT 'PRIVATE' COMMENT '分享范围',
    allowed_users JSON COMMENT '允许查看的用户ID数组（当share_scope为PRIVATE时使用）',
    
    -- 分享内容控制
    include_phone BOOLEAN DEFAULT TRUE COMMENT '是否包含手机号',
    include_wechat BOOLEAN DEFAULT TRUE COMMENT '是否包含微信号',
    include_email BOOLEAN DEFAULT FALSE COMMENT '是否包含邮箱',
    include_company BOOLEAN DEFAULT FALSE COMMENT '是否包含公司信息',
    include_address BOOLEAN DEFAULT FALSE COMMENT '是否包含地址',
    include_notes BOOLEAN DEFAULT FALSE COMMENT '是否包含备注',
    
    -- 分享统计
    share_count INT DEFAULT 0 COMMENT '分享次数',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    last_shared_at TIMESTAMP NULL COMMENT '最后分享时间',
    last_viewed_at TIMESTAMP NULL COMMENT '最后查看时间',
    
    -- 有效期设置
    expires_at TIMESTAMP NULL COMMENT '过期时间（NULL表示永不过期）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    
    -- 系统字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (contact_id) REFERENCES miniprogram_contacts(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_contact_id (contact_id),
    INDEX idx_share_type (share_type),
    INDEX idx_share_scope (share_scope),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_user_contact (user_id, contact_id),
    INDEX idx_active_expires (is_active, expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人分享配置表';

-- 3. 联系人访问记录表（可选，用于统计分析）
CREATE TABLE contact_access_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    contact_id INT NOT NULL COMMENT '联系人ID',
    share_config_id INT COMMENT '分享配置ID（如果通过分享访问）',
    visitor_user_id VARCHAR(50) COMMENT '访问者用户ID',
    visitor_ip VARCHAR(45) COMMENT '访问者IP',
    access_type ENUM('VIEW', 'SHARE', 'DOWNLOAD', 'SAVE') NOT NULL COMMENT '访问类型',
    user_agent TEXT COMMENT '用户代理信息',
    referer VARCHAR(500) COMMENT '来源页面',
    
    -- 系统字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
    
    -- 外键约束
    FOREIGN KEY (contact_id) REFERENCES miniprogram_contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (share_config_id) REFERENCES contact_share_configs(id) ON DELETE SET NULL,
    
    -- 索引
    INDEX idx_contact_id (contact_id),
    INDEX idx_share_config_id (share_config_id),
    INDEX idx_visitor_user_id (visitor_user_id),
    INDEX idx_access_type (access_type),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_contact_access_type (contact_id, access_type),
    INDEX idx_visitor_date (visitor_user_id, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系人访问记录表';

-- 插入操作日志（可选）
INSERT INTO operation_logs (user_type, action, resource, details, ip_address, created_at) VALUES
('admin', 'TABLE_CREATE', 'miniprogram_contacts', '{"action": "create_contact_tables", "tables": ["miniprogram_contacts", "contact_share_configs", "contact_access_logs"], "timestamp": "2025-06-21"}', '127.0.0.1', NOW()),
('admin', 'SYSTEM_READY', 'contacts_system', '{"message": "联系人相关表结构创建完成", "timestamp": "2025-06-21"}', '127.0.0.1', NOW());

-- 显示创建结果
SELECT '联系人相关表结构创建完成！' as message;
SHOW TABLES LIKE '%contact%';
