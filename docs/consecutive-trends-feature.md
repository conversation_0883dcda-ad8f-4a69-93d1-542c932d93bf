# 连续涨跌功能说明文档

## 功能概述

为价格涨跌榜系统添加了连续涨跌判断功能，可以识别连续两天或更多天数的价格上涨或下跌趋势，并提供相应的标识和查询接口。

## 数据库变更

### 新增字段

在 `price_trend_rankings` 表中添加了以下字段：

```sql
-- 连续上涨天数（0表示今日未涨或中断）
consecutive_rise_days INT DEFAULT 0

-- 连续下跌天数（0表示今日未跌或中断）  
consecutive_fall_days INT DEFAULT 0

-- 趋势信号：CONSECUTIVE_RISE_2+, CONSECUTIVE_FALL_2+, NORMAL
trend_signal VARCHAR(20) DEFAULT NULL
```

### 索引优化

```sql
CREATE INDEX idx_consecutive_rise ON price_trend_rankings(consecutive_rise_days);
CREATE INDEX idx_consecutive_fall ON price_trend_rankings(consecutive_fall_days);
CREATE INDEX idx_trend_signal ON price_trend_rankings(trend_signal);
```

## API 接口

### 1. 增强的价格涨跌榜接口

**接口地址**: `GET /api/v1/categories/price-rankings`

现有接口返回数据中新增了连续涨跌相关字段：

```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "category_name": "iPhone 15 Pro",
        "consecutive_rise_days": 3,
        "consecutive_fall_days": 0,
        "trend_signal": "CONSECUTIVE_RISE_3",
        "change_percentage": 2.56,
        // ... 其他字段
      }
    ]
  }
}
```

### 2. 连续涨跌商品查询接口

**接口地址**: `GET /api/v1/categories/price-rankings/consecutive`

**查询参数**:
- `trend_type` (可选): 趋势类型，`RISE` | `FALL`，不传则返回所有
- `min_days` (可选): 最少连续天数，默认为 2
- `date` (可选): 目标日期，格式 YYYY-MM-DD，默认今天
- `page` (可选): 页码，默认 1
- `limit` (可选): 每页数量，默认 20

**示例请求**:
```bash
# 查询连续上涨2天以上的商品
GET /api/v1/categories/price-rankings/consecutive?trend_type=RISE&min_days=2

# 查询连续下跌3天以上的商品
GET /api/v1/categories/price-rankings/consecutive?trend_type=FALL&min_days=3

# 查询所有连续涨跌2天以上的商品
GET /api/v1/categories/price-rankings/consecutive?min_days=2
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "category_name": "iPhone 15 Pro",
        "brand_name": "苹果",
        "consecutive_rise_days": 3,
        "consecutive_fall_days": 0,
        "trend_signal": "CONSECUTIVE_RISE_3",
        "change_percentage": 2.56,
        "current_price": 7999.00,
        "previous_price": 7799.00
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "totalPages": 1
    },
    "meta": {
      "trend_type": "RISE",
      "min_days": 2,
      "ranking_date": "2025-06-22"
    }
  },
  "message": "Consecutive trends retrieved successfully"
}
```

## 计算逻辑

### 连续天数计算

1. **连续上涨**: 从今天开始向前查找，统计连续 `trend_type = 'RISE'` 的天数
2. **连续下跌**: 从今天开始向前查找，统计连续 `trend_type = 'FALL'` 的天数
3. **最大查找范围**: 30天（可配置）

### 趋势信号生成

- `NORMAL`: 连续天数 < 2
- `CONSECUTIVE_RISE_N`: 连续上涨 N 天（N >= 2）
- `CONSECUTIVE_FALL_N`: 连续下跌 N 天（N >= 2）

## 定时任务

### 1. 脚本执行

每天定时运行价格涨跌榜计算脚本：

```bash
# 手动执行
npm run calculate-rankings

# 或者直接运行脚本
node dist/scripts/calculatePriceRankings.js
```

### 2. Cron 定时任务

使用提供的 shell 脚本设置定时任务：

```bash
# 给脚本添加执行权限
chmod +x src/scripts/cron-price-rankings.sh

# 添加到 crontab（每天早上8点执行）
crontab -e
0 8 * * * /path/to/your/project/src/scripts/cron-price-rankings.sh
```

### 3. PM2 管理

使用 PM2 管理定时任务：

```bash
# 启动 PM2 生态系统
pm2 start ecosystem.config.js

# 查看定时任务状态
pm2 list

# 查看日志
pm2 logs price-rankings-cron
```

## 测试

运行测试用例验证功能：

```bash
# 运行连续涨跌功能测试
npm test -- consecutiveTrends.test.ts

# 运行所有价格涨跌榜相关测试
npm test -- priceRankings.test.ts
```

## 使用场景

1. **风险预警**: 识别连续下跌的商品，及时调整采购策略
2. **机会发现**: 发现连续上涨的商品，把握市场机会
3. **趋势分析**: 分析市场整体趋势，制定长期策略
4. **自动化决策**: 基于连续涨跌信号触发自动化业务逻辑

## 注意事项

1. **数据依赖**: 需要至少2天的历史数据才能计算连续趋势
2. **性能考虑**: 连续天数计算会查询历史数据，建议合理设置查找范围
3. **数据一致性**: 确保每天定时任务正常运行，保持数据的连续性
4. **索引优化**: 已添加相关索引，但大数据量时仍需关注查询性能

## 日志监控

- **计算日志**: `logs/price-rankings-cron.log`
- **应用日志**: 通过 `operation_logs` 表记录 API 调用
- **PM2 日志**: `pm2 logs price-rankings-cron`
