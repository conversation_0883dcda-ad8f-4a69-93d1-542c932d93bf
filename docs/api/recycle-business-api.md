# 回收业务联系信息管理系统 API 文档

## 概述

基于您提供的回收业务联系信息图片，我们设计了一套完整的回收业务管理系统，包含门店管理、员工管理、客户联系记录等功能。

## 核心功能

### 1. 回收门店管理
- 门店基本信息管理（名称、编码、地址、联系方式）
- 支持销售电话/微信和回收电话/微信分别管理
- 地理位置信息（经纬度）和附近门店查询
- 营业时间和状态管理
- 门店类型分类（旗舰店、标准店、迷你店、线上店）

### 2. 员工管理
- 员工基本信息和联系方式
- 职位分类（经理、销售、回收员、评估师、客服等）
- 专业技能标签
- 工作状态和时间安排

### 3. 客户联系记录
- 客户咨询记录管理
- 联系方式和内容记录
- 报价和跟进状态
- 处理结果跟踪

### 4. 门店展示配置
- 自定义门店展示信息
- 二维码管理
- 联系信息显示控制
- 欢迎消息和服务承诺

## API 接口列表

### 回收门店接口

#### 1. 创建门店
```
POST /api/recycle-stores
```

**请求体示例：**
```json
{
  "storeCode": "CD001",
  "storeName": "宝丰回收成都青羊店",
  "province": "四川省",
  "city": "成都市",
  "district": "青羊区",
  "detailedAddress": "赛格广场一楼5032-5033号",
  "longitude": 104.0668,
  "latitude": 30.6598,
  "salesPhone": "***********",
  "salesWechat": "***********",
  "recyclePhone": "***********",
  "recycleWechat": "***********",
  "mainPhone": "***********",
  "email": "<EMAIL>",
  "businessHours": {
    "monday": "09:00-18:00",
    "tuesday": "09:00-18:00",
    "wednesday": "09:00-18:00",
    "thursday": "09:00-18:00",
    "friday": "09:00-18:00",
    "saturday": "09:00-17:00",
    "sunday": "10:00-17:00"
  },
  "businessStatus": "OPEN",
  "storeType": "STANDARD",
  "storeArea": 120.5,
  "serviceRadius": 10,
  "supportedCategories": [1, 2, 3, 4, 5]
}
```

#### 2. 获取门店列表
```
GET /api/recycle-stores?page=1&limit=20&province=四川省&city=成都市&search=宝丰
```

#### 3. 获取门店详情
```
GET /api/recycle-stores/{id}
```

#### 4. 根据门店编码获取门店
```
GET /api/recycle-stores/code/{storeCode}
```

#### 5. 查找附近门店
```
GET /api/recycle-stores/nearby?longitude=104.0668&latitude=30.6598&radius=10
```

#### 6. 更新门店信息
```
PUT /api/recycle-stores/{id}
```

#### 7. 删除门店
```
DELETE /api/recycle-stores/{id}
```

## 数据库表结构

### 1. recycle_stores (回收门店表)
- 存储门店基本信息、地址、联系方式
- 支持地理位置查询
- 营业时间和状态管理

### 2. recycle_employees (回收员工表)
- 员工基本信息和职位
- 关联门店信息
- 专业技能和工作安排

### 3. customer_contact_logs (客户联系记录表)
- 客户咨询和联系记录
- 处理状态和跟进安排
- 关联门店和员工

### 4. store_contact_configs (门店联系信息配置表)
- 门店展示配置
- 二维码和自定义信息
- 联系信息显示控制

## 特色功能

### 1. 地理位置服务
- 支持根据经纬度查找附近门店
- 计算距离并按距离排序
- 服务半径配置

### 2. 多联系方式管理
- 区分销售和回收联系方式
- 支持电话和微信分别配置
- 主要联系电话设置

### 3. 灵活的展示配置
- 可控制各项信息的显示/隐藏
- 支持自定义欢迎消息
- 二维码统一管理

### 4. 完整的权限控制
- 管理员权限验证
- 操作日志记录
- 数据安全保护

## 使用场景

### 1. 门店管理
- 总部管理多个回收门店
- 门店信息统一维护
- 营业状态实时更新

### 2. 客户服务
- 客户查找附近门店
- 获取准确联系方式
- 在线咨询和预约

### 3. 员工管理
- 门店员工信息管理
- 专业技能匹配
- 工作安排优化

### 4. 数据分析
- 客户咨询统计
- 门店服务效率
- 地区业务分布

## 技术特点

### 1. RESTful API 设计
- 标准化接口规范
- 统一响应格式
- 完整的错误处理

### 2. 类型安全
- TypeScript 类型定义
- 数据验证和转换
- 编译时错误检查

### 3. 数据库优化
- 合理的索引设计
- 外键约束保证数据一致性
- JSON 字段存储复杂数据

### 4. 安全性
- JWT 令牌认证
- 角色权限控制
- SQL 注入防护

## 部署说明

### 1. 数据库初始化
```bash
# 执行数据库脚本
mysql -u root -p baofeng_recycle < docs/database/recycle-business-schema.sql
```

### 2. 环境配置
确保 `.env` 文件包含正确的数据库连接信息。

### 3. 启动服务
```bash
npm run dev
```

### 4. API 文档访问
访问 `http://localhost:3000/api-docs` 查看完整的 Swagger API 文档。

## 示例数据

系统会自动插入一条示例门店数据：
- 门店编码：CD001
- 门店名称：宝丰回收成都青羊店
- 地址：四川省成都市青羊区赛格广场一楼5032-5033号
- 联系电话：***********

这与您提供的图片信息完全一致，可以直接用于测试和演示。
