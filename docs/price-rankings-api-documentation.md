# Price Rankings API Documentation

## Overview

The Price Rankings API provides endpoints for retrieving and managing price trend data for recycling categories. This API tracks price changes over time and provides ranking information for items that have experienced significant price increases or decreases.

## Base URL
```
http://localhost:3000/api/v1/categories
```

## Authentication

Some endpoints require authentication using Bearer tokens:
```
Authorization: Bearer <your-token>
```

## Endpoints

### 1. Get Price Rankings

**GET** `/price-rankings`

Retrieve price rankings showing items with significant price changes.

#### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page` | integer | No | 1 | Page number for pagination (min: 1) |
| `limit` | integer | No | 20 | Items per page (min: 1, max: 100) |
| `type` | string | No | RISE_RANKING | Ranking type: `RISE_RANKING` or `FALL_RANKING` |
| `date` | string | No | today | Date in YYYY-MM-DD format |
| `category_level` | integer | No | all | Filter by level: 1 (Brand), 2 (Model), 3 (Sub-model) |

#### Example Request
```bash
curl "http://localhost:3000/api/v1/categories/price-rankings?type=RISE_RANKING&date=2025-06-19&limit=5"
```

#### Example Response
```json
{
  "success": true,
  "message": "Price rankings retrieved successfully",
  "data": {
    "list": [
      {
        "id": 1,
        "ranking_date": "2025-06-18T16:00:00.000Z",
        "category_id": 1,
        "category_level": 3,
        "category_name": "iPhone 15 Pro Max",
        "brand_name": "苹果",
        "model_name": "iPhone 15",
        "sub_model_name": "iPhone 15 Pro Max",
        "memory_size": "256GB",
        "tag_name": "靓机",
        "group_name": "成色",
        "current_price": "8999.00",
        "previous_price": "8799.00",
        "price_change": "200.00",
        "change_percentage": "2.27",
        "trend_type": "RISE",
        "ranking_position": 1,
        "ranking_type": "RISE_RANKING",
        "status": 1
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 5,
      "total": 2,
      "totalPages": 1
    },
    "meta": {
      "ranking_type": "RISE_RANKING",
      "ranking_date": "2025-06-19",
      "category_level": "all"
    }
  },
  "timestamp": "2025-06-22T11:30:00.000Z"
}
```

### 2. Get Price Rankings Statistics

**GET** `/price-rankings/stats`

Retrieve statistical analysis of price rankings including distribution and top movers.

#### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `date` | string | No | today | Statistics date in YYYY-MM-DD format |

#### Example Request
```bash
curl "http://localhost:3000/api/v1/categories/price-rankings/stats?date=2025-06-19"
```

#### Example Response
```json
{
  "success": true,
  "message": "Price ranking statistics retrieved successfully",
  "data": {
    "statistics": [
      {
        "ranking_type": "RISE_RANKING",
        "total_count": 2,
        "avg_change_percentage": "2.27",
        "max_change_percentage": "2.27",
        "min_change_percentage": "2.27"
      },
      {
        "ranking_type": "FALL_RANKING",
        "total_count": 2,
        "avg_change_percentage": "-1.50",
        "max_change_percentage": "-1.00",
        "min_change_percentage": "-2.00"
      }
    ],
    "levelDistribution": [
      {
        "category_level": 3,
        "ranking_type": "RISE_RANKING",
        "count": 2
      },
      {
        "category_level": 3,
        "ranking_type": "FALL_RANKING",
        "count": 2
      }
    ],
    "topRise": [
      {
        "category_name": "iPhone 15 Pro Max",
        "brand_name": "苹果",
        "model_name": "iPhone 15",
        "sub_model_name": "iPhone 15 Pro Max",
        "change_percentage": "2.27",
        "current_price": "8999.00",
        "previous_price": "8799.00"
      }
    ],
    "topFall": [
      {
        "category_name": "Samsung Galaxy S24",
        "brand_name": "三星",
        "model_name": "Galaxy S24",
        "sub_model_name": "Galaxy S24 Ultra",
        "change_percentage": "-2.00",
        "current_price": "7800.00",
        "previous_price": "8000.00"
      }
    ],
    "meta": {
      "date": "2025-06-19",
      "total_categories": 4
    }
  },
  "timestamp": "2025-06-22T11:30:00.000Z"
}
```

### 3. Calculate Price Rankings

**POST** `/price-rankings/calculate`

Manually trigger the calculation of price rankings. Requires authentication.

#### Authentication Required
```
Authorization: Bearer <your-token>
```

#### Example Request
```bash
curl -X POST "http://localhost:3000/api/v1/categories/price-rankings/calculate" \
  -H "Authorization: Bearer your-token-here"
```

#### Example Response
```json
{
  "success": true,
  "message": "Price rankings calculation completed successfully",
  "data": {
    "processed": 150,
    "created": 25,
    "updated": 10
  },
  "timestamp": "2025-06-22T11:30:00.000Z"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid ranking type. Must be RISE_RANKING or FALL_RANKING",
  "timestamp": "2025-06-22T11:30:00.000Z"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Access token is required",
  "timestamp": "2025-06-22T11:30:00.000Z"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to retrieve price rankings",
  "timestamp": "2025-06-22T11:30:00.000Z"
}
```

## Data Models

### Price Ranking Object
```typescript
interface PriceRanking {
  id: number;
  ranking_date: string;
  category_id: number;
  category_level: number;
  category_name: string;
  brand_name: string;
  model_name: string;
  sub_model_name: string;
  memory_size: string;
  tag_name: string;
  group_name: string;
  current_price: string;
  previous_price: string;
  price_change: string;
  change_percentage: string;
  trend_type: 'RISE' | 'FALL' | 'STABLE';
  ranking_position: number;
  ranking_type: 'RISE_RANKING' | 'FALL_RANKING';
  status: number;
}
```

### Pagination Object
```typescript
interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}
```

## Rate Limiting

The API implements rate limiting to ensure fair usage. Current limits:
- 100 requests per minute per IP address
- 1000 requests per hour per authenticated user

## Database Information

- **Database**: `baofeng_recycle`
- **Main Table**: `price_trend_rankings`
- **Logs Table**: `operation_logs`

## Testing

Use the provided test script to verify functionality:
```bash
./scripts/test-price-rankings.sh
```

## Support

For technical support or questions about the Price Rankings API, please refer to:
- Test Report: `docs/price-rankings-test-report.md`
- Fix Summary: `docs/price-rankings-fix-summary.md`
- API Documentation: `docs/price-rankings-api-documentation.md`
