/**
 * 微信小程序配置相关类型定义
 */

// 配置类型枚举
export enum ConfigType {
  CAROUSEL = 'CAROUSEL',           // 轮播图
  ANNOUNCEMENT = 'ANNOUNCEMENT',   // 公告
  CONTACT_PHONE = 'CONTACT_PHONE', // 电话联系
  WECHAT_COPY = 'WECHAT_COPY',     // 复制微信号
  SHARE_CONFIG = 'SHARE_CONFIG',   // 一键分享配置
  SYSTEM_CONFIG = 'SYSTEM_CONFIG'  // 系统配置
}

// 轮播图配置数据结构
export interface CarouselConfigValue {
  image_url: string;
  title: string;
  subtitle?: string;
  link_type: 'page' | 'url' | 'none';
  link_value?: string;
  background_color?: string;
}

// 公告配置数据结构
export interface AnnouncementConfigValue {
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error';
  show_icon?: boolean;
  closable?: boolean;
  auto_close_time?: number;
  highlight?: boolean;
}

// 电话联系配置数据结构
export interface ContactPhoneConfigValue {
  phone_number: string;
  display_name: string;
  service_time?: string;
  description?: string;
  show_confirm?: boolean;
  confirm_text?: string;
}

// 微信号复制配置数据结构
export interface WechatCopyConfigValue {
  wechat_id: string;
  display_name: string;
  qr_code_url?: string;
  description?: string;
  copy_success_text?: string;
  show_qr_code?: boolean;
}

// 分享配置数据结构
export interface ShareConfigValue {
  title: string;
  description: string;
  image_url?: string;
  share_path?: string;
  enable_timeline?: boolean;
  enable_session?: boolean;
  enable_qq?: boolean;
}

// 系统配置数据结构
export interface SystemConfigValue {
  [key: string]: any;
}

// 配置值联合类型
export type ConfigValue = 
  | CarouselConfigValue 
  | AnnouncementConfigValue 
  | ContactPhoneConfigValue 
  | WechatCopyConfigValue 
  | ShareConfigValue 
  | SystemConfigValue;

// 小程序配置主接口
export interface MiniprogramConfig {
  id: number;
  config_type: ConfigType;
  config_key: string;
  config_name: string;
  config_value: ConfigValue;
  display_order: number;
  is_enabled: boolean;
  start_time?: Date;
  end_time?: Date;
  group_name?: string;
  tags?: string[];
  description?: string;
  extra_data?: any;
  created_by: string;
  updated_by: string;
  created_at: Date;
  updated_at: Date;
}

// 创建配置DTO
export interface CreateMiniprogramConfigDto {
  config_type: ConfigType;
  config_key: string;
  config_name: string;
  config_value: ConfigValue;
  display_order?: number;
  is_enabled?: boolean;
  start_time?: Date;
  end_time?: Date;
  group_name?: string;
  tags?: string[];
  description?: string;
  extra_data?: any;
  created_by?: string;
}

// 更新配置DTO
export interface UpdateMiniprogramConfigDto {
  config_name?: string;
  config_value?: ConfigValue;
  display_order?: number;
  is_enabled?: boolean;
  start_time?: Date;
  end_time?: Date;
  group_name?: string;
  tags?: string[];
  description?: string;
  extra_data?: any;
  updated_by?: string;
}

// 配置查询参数
export interface MiniprogramConfigQuery {
  config_type?: ConfigType;
  config_key?: string;
  group_name?: string;
  is_enabled?: boolean;
  tags?: string[];
  start_date?: Date;
  end_date?: Date;
  page?: number;
  limit?: number;
  sort_by?: 'display_order' | 'created_at' | 'updated_at';
  sort_order?: 'ASC' | 'DESC';
}

// 配置操作日志
export interface MiniprogramConfigLog {
  id: number;
  config_id: number;
  operation_type: 'CREATE' | 'UPDATE' | 'DELETE' | 'ENABLE' | 'DISABLE';
  old_value?: ConfigValue;
  new_value?: ConfigValue;
  operator: string;
  operation_reason?: string;
  ip_address?: string;
  created_at: Date;
}

// 批量操作DTO
export interface BatchUpdateConfigDto {
  config_ids: number[];
  updates: Partial<UpdateMiniprogramConfigDto>;
  operator?: string;
  operation_reason?: string;
}

// 配置分组信息
export interface ConfigGroup {
  group_name: string;
  config_count: number;
  enabled_count: number;
  disabled_count: number;
  last_updated: Date;
}

// API响应类型
export interface MiniprogramConfigResponse {
  success: boolean;
  data?: MiniprogramConfig | MiniprogramConfig[];
  message?: string;
  total?: number;
  page?: number;
  limit?: number;
}

// 配置统计信息
export interface ConfigStatistics {
  total_configs: number;
  enabled_configs: number;
  disabled_configs: number;
  configs_by_type: Record<ConfigType, number>;
  configs_by_group: Record<string, number>;
  recent_updates: number;
}

// 前端展示用的配置数据
export interface DisplayConfig {
  id: number;
  type: ConfigType;
  key: string;
  name: string;
  value: ConfigValue;
  enabled: boolean;
  order: number;
  group?: string;
  description?: string;
  valid_period?: {
    start?: Date;
    end?: Date;
  };
}

// 配置验证规则
export interface ConfigValidationRule {
  config_type: ConfigType;
  required_fields: string[];
  optional_fields: string[];
  field_types: Record<string, string>;
  validation_rules: Record<string, any>;
}

// 导出所有类型，不使用默认导出以避免TypeScript错误
