import * as XLSX from 'xlsx';
import * as nodemailer from 'nodemailer';
import * as fs from 'fs';
import * as path from 'path';
import { UserModel } from '../models/User';
import { User } from '../types';
import logger from '../config/logger';

/**
 * 数据导出服务
 */
export class ExportService {
  /**
   * 导出用户数据到Excel并发送邮件
   * @param recipientEmail 收件人邮箱
   */
  static async exportUsersToExcelAndEmail(recipientEmail: string): Promise<void> {
    try {
      logger.info('Starting user data export', { recipientEmail });

      // 1. 获取所有用户数据
      const users = await UserModel.findAll();
      logger.info(`Found ${users.length} users to export`);

      // 2. 生成Excel文件
      const filePath = await this.generateUsersExcel(users);
      logger.info(`Excel file generated: ${filePath}`);

      // 3. 发送邮件
      await this.sendEmailWithAttachment(recipientEmail, filePath);
      logger.info(`Email sent successfully to ${recipientEmail}`);

      // 4. 清理临时文件
      fs.unlinkSync(filePath);
      logger.info('Temporary file cleaned up');

    } catch (error) {
      logger.error('Export users to Excel and email failed:', error);
      throw error;
    }
  }

  /**
   * 生成用户数据Excel文件
   * @param users 用户数据
   * @returns Excel文件路径
   */
  private static async generateUsersExcel(users: User[]): Promise<string> {
    // 准备Excel数据
    const excelData = users.map((user, index) => ({
      '序号': index + 1,
      '用户ID': user.id,
      '手机号': user.phone || '未设置',
      '头像': user.avatar || '未设置',
      '角色': this.translateRole(user.role),
      '状态': user.isActive ? '激活' : '未激活',
      '微信OpenID': user.wechatOpenId || '未绑定',
      '微信UnionID': user.wechatUnionId || '未绑定',
      '创建时间': this.formatDate(user.createdAt),
      '更新时间': this.formatDate(user.updatedAt),
    }));

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // 设置列宽
    const columnWidths = [
      { wch: 8 },  // 序号
      { wch: 40 }, // 用户ID
      { wch: 15 }, // 手机号
      { wch: 50 }, // 头像
      { wch: 10 }, // 角色
      { wch: 10 }, // 状态
      { wch: 35 }, // 微信OpenID
      { wch: 35 }, // 微信UnionID
      { wch: 20 }, // 创建时间
      { wch: 20 }, // 更新时间
    ];
    worksheet['!cols'] = columnWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '用户数据');

    // 生成文件路径
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `users_export_${timestamp}.xlsx`;
    const filePath = path.join(process.cwd(), 'temp', fileName);

    // 确保temp目录存在
    const tempDir = path.dirname(filePath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 写入文件
    XLSX.writeFile(workbook, filePath);

    return filePath;
  }

  /**
   * 发送带附件的邮件
   * @param recipientEmail 收件人邮箱
   * @param attachmentPath 附件路径
   */
  private static async sendEmailWithAttachment(
    recipientEmail: string, 
    attachmentPath: string
  ): Promise<void> {
    // 创建邮件传输器
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.qq.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER || '<EMAIL>',
        pass: process.env.SMTP_PASS || 'your-app-password',
      },
    });

    // 邮件选项
    const mailOptions = {
      from: process.env.SMTP_USER || '<EMAIL>',
      to: recipientEmail,
      subject: '宝丰系统用户数据导出',
      html: `
        <h2>宝丰系统用户数据导出</h2>
        <p>您好！</p>
        <p>附件中包含了系统中所有用户的数据导出，请查收。</p>
        <p><strong>导出时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
        <p><strong>数据说明：</strong></p>
        <ul>
          <li>包含所有用户的基本信息</li>
          <li>手机号、微信信息、角色状态等</li>
          <li>创建和更新时间</li>
        </ul>
        <p>如有任何问题，请联系系统管理员。</p>
        <br>
        <p>宝丰系统</p>
      `,
      attachments: [
        {
          filename: path.basename(attachmentPath),
          path: attachmentPath,
        },
      ],
    };

    // 发送邮件
    await transporter.sendMail(mailOptions);
  }

  /**
   * 翻译用户角色
   * @param role 角色
   * @returns 中文角色名
   */
  private static translateRole(role: string): string {
    const roleMap: { [key: string]: string } = {
      'user': '普通用户',
      'admin': '管理员',
      'super_admin': '超级管理员',
      'moderator': '版主',
    };
    return roleMap[role] || role;
  }

  /**
   * 格式化日期
   * @param date 日期
   * @returns 格式化后的日期字符串
   */
  private static formatDate(date: Date): string {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  /**
   * 获取用户统计信息
   * @returns 用户统计数据
   */
  static async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    withPhone: number;
    withWechat: number;
    byRole: { [key: string]: number };
  }> {
    try {
      const users = await UserModel.findAll();
      
      const stats = {
        total: users.length,
        active: users.filter(u => u.isActive).length,
        inactive: users.filter(u => !u.isActive).length,
        withPhone: users.filter(u => u.phone).length,
        withWechat: users.filter(u => u.wechatOpenId).length,
        byRole: {} as { [key: string]: number },
      };

      // 统计各角色数量
      users.forEach(user => {
        stats.byRole[user.role] = (stats.byRole[user.role] || 0) + 1;
      });

      return stats;
    } catch (error) {
      logger.error('Get user stats failed:', error);
      throw error;
    }
  }
}
