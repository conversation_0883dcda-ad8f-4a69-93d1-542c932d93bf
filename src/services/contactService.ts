import { 
  Contact, 
  CreateContactDto, 
  UpdateContactDto, 
  ContactQuery, 
  ContactStats,
  ContactType,
  AppError 
} from '../types';
import { ContactModel } from '../models/Contact';
import logger from '../config/logger';

export class ContactService {
  /**
   * Create a new contact
   */
  static async createContact(userId: string, contactData: CreateContactDto): Promise<Contact> {
    try {
      // Validate required fields
      if (!contactData.name || !contactData.phone) {
        throw new AppError('Name and phone are required', 400);
      }

      // Validate phone format (basic validation)
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(contactData.phone)) {
        throw new AppError('Invalid phone number format', 400);
      }

      // Validate importance level
      if (contactData.importanceLevel && (contactData.importanceLevel < 1 || contactData.importanceLevel > 5)) {
        throw new AppError('Importance level must be between 1 and 5', 400);
      }

      const contact = await ContactModel.create(userId, contactData);
      
      logger.info(`Contact created successfully: ${contact.id} for user: ${userId}`);
      return contact;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in createContact service:', error);
      throw new AppError('Failed to create contact', 500);
    }
  }

  /**
   * Get contact by ID
   */
  static async getContactById(contactId: number, userId: string): Promise<Contact> {
    try {
      const contact = await ContactModel.findById(contactId, userId);
      
      if (!contact) {
        throw new AppError('Contact not found', 404);
      }

      return contact;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in getContactById service:', error);
      throw new AppError('Failed to get contact', 500);
    }
  }

  /**
   * Get contacts by user ID with filtering and pagination
   */
  static async getContactsByUserId(userId: string, query: ContactQuery): Promise<{ contacts: Contact[]; total: number; page: number; limit: number }> {
    try {
      // Set default pagination
      const page = Number(query.page) || 1;
      const limit = Math.min(Number(query.limit) || 20, 100); // Max 100 per page

      const queryWithPagination = {
        ...query,
        page,
        limit
      };

      const result = await ContactModel.findByUserId(userId, queryWithPagination);
      
      return {
        contacts: result.contacts,
        total: result.total,
        page,
        limit
      };
    } catch (error) {
      logger.error('Error in getContactsByUserId service:', error);
      throw new AppError('Failed to get contacts', 500);
    }
  }

  /**
   * Update contact
   */
  static async updateContact(contactId: number, userId: string, updateData: UpdateContactDto): Promise<Contact> {
    try {
      // Validate phone format if provided
      if (updateData.phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!updateData.phone.match(phoneRegex)) {
          throw new AppError('Invalid phone number format', 400);
        }
      }

      // Validate importance level if provided
      if (updateData.importanceLevel && (updateData.importanceLevel < 1 || updateData.importanceLevel > 5)) {
        throw new AppError('Importance level must be between 1 and 5', 400);
      }

      const contact = await ContactModel.update(contactId, userId, updateData);
      
      if (!contact) {
        throw new AppError('Contact not found', 404);
      }

      logger.info(`Contact updated successfully: ${contactId} for user: ${userId}`);
      return contact;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in updateContact service:', error);
      throw new AppError('Failed to update contact', 500);
    }
  }

  /**
   * Delete contact
   */
  static async deleteContact(contactId: number, userId: string): Promise<void> {
    try {
      const success = await ContactModel.delete(contactId, userId);
      
      if (!success) {
        throw new AppError('Contact not found', 404);
      }

      logger.info(`Contact deleted successfully: ${contactId} for user: ${userId}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in deleteContact service:', error);
      throw new AppError('Failed to delete contact', 500);
    }
  }

  /**
   * Record contact interaction
   */
  static async recordContactInteraction(contactId: number, userId: string, contactType: ContactType): Promise<Contact> {
    try {
      const contact = await ContactModel.updateContactInteraction(contactId, userId, contactType);
      
      if (!contact) {
        throw new AppError('Contact not found', 404);
      }

      logger.info(`Contact interaction recorded: ${contactId} for user: ${userId}, type: ${contactType}`);
      return contact;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in recordContactInteraction service:', error);
      throw new AppError('Failed to record contact interaction', 500);
    }
  }

  /**
   * Search public contacts
   */
  static async searchPublicContacts(query: ContactQuery): Promise<{ contacts: Contact[]; total: number; page: number; limit: number }> {
    try {
      // Set default pagination
      const page = Number(query.page) || 1;
      const limit = Math.min(Number(query.limit) || 20, 100); // Max 100 per page

      const queryWithPagination = {
        ...query,
        page,
        limit
      };

      const result = await ContactModel.findPublicContacts(queryWithPagination);
      
      return {
        contacts: result.contacts,
        total: result.total,
        page,
        limit
      };
    } catch (error) {
      logger.error('Error in searchPublicContacts service:', error);
      throw new AppError('Failed to search public contacts', 500);
    }
  }

  /**
   * Get contact statistics
   */
  static async getContactStats(userId: string): Promise<ContactStats> {
    try {
      const stats = await ContactModel.getContactStats(userId);
      return stats;
    } catch (error) {
      logger.error('Error in getContactStats service:', error);
      throw new AppError('Failed to get contact statistics', 500);
    }
  }

  /**
   * Toggle favorite status
   */
  static async toggleFavorite(contactId: number, userId: string): Promise<Contact> {
    try {
      const contact = await ContactModel.findById(contactId, userId);
      
      if (!contact) {
        throw new AppError('Contact not found', 404);
      }

      const updatedContact = await ContactModel.update(contactId, userId, {
        isFavorite: !contact.isFavorite
      });

      if (!updatedContact) {
        throw new AppError('Failed to update contact', 500);
      }

      logger.info(`Contact favorite toggled: ${contactId} for user: ${userId}, favorite: ${updatedContact.isFavorite}`);
      return updatedContact;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in toggleFavorite service:', error);
      throw new AppError('Failed to toggle favorite status', 500);
    }
  }

  /**
   * Batch update contacts
   */
  static async batchUpdateContacts(contactIds: number[], userId: string, updateData: Partial<UpdateContactDto>): Promise<Contact[]> {
    try {
      const updatedContacts: Contact[] = [];

      for (const contactId of contactIds) {
        const contact = await ContactModel.update(contactId, userId, updateData);
        if (contact) {
          updatedContacts.push(contact);
        }
      }

      logger.info(`Batch updated ${updatedContacts.length} contacts for user: ${userId}`);
      return updatedContacts;
    } catch (error) {
      logger.error('Error in batchUpdateContacts service:', error);
      throw new AppError('Failed to batch update contacts', 500);
    }
  }

  /**
   * Get contacts by category
   */
  static async getContactsByCategory(userId: string, category: string): Promise<Contact[]> {
    try {
      const query: ContactQuery = {
        category: category as any,
        limit: 1000 // Get all contacts in category
      };

      const result = await ContactModel.findByUserId(userId, query);
      return result.contacts;
    } catch (error) {
      logger.error('Error in getContactsByCategory service:', error);
      throw new AppError('Failed to get contacts by category', 500);
    }
  }
}
