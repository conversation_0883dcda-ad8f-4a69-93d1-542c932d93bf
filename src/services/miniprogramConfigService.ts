import { 
  MiniprogramConfig, 
  CreateMiniprogramConfigDto, 
  UpdateMiniprogramConfigDto,
  MiniprogramConfigQuery,
  ConfigType,
  ConfigStatistics,
  ConfigGroup,
  BatchUpdateConfigDto,
  DisplayConfig
} from '../types/miniprogramConfig';
import { MiniprogramConfigModel } from '../models/MiniprogramConfig';
import { AppError, createError } from '../utils/errors';
import logger from '../config/logger';

export class MiniprogramConfigService {
  /**
   * 创建新配置
   */
  static async createConfig(configData: CreateMiniprogramConfigDto): Promise<MiniprogramConfig> {
    try {
      // 验证配置数据
      this.validateConfigData(configData.config_type, configData.config_value);
      
      const config = await MiniprogramConfigModel.create(configData);
      
      logger.info(`Miniprogram config created successfully: ${config.id}`);
      return config;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in createConfig service:', error);
      throw new AppError('Failed to create config', 500);
    }
  }

  /**
   * 获取配置详情
   */
  static async getConfigById(id: number): Promise<MiniprogramConfig> {
    try {
      const config = await MiniprogramConfigModel.findById(id);
      
      if (!config) {
        throw new AppError('Config not found', 404);
      }

      return config;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in getConfigById service:', error);
      throw new AppError('Failed to get config', 500);
    }
  }

  /**
   * 获取配置列表
   */
  static async getConfigs(query: MiniprogramConfigQuery = {}): Promise<{ configs: MiniprogramConfig[]; total: number }> {
    try {
      const result = await MiniprogramConfigModel.findMany(query);
      return result;
    } catch (error) {
      logger.error('Error in getConfigs service:', error);
      throw new AppError('Failed to get configs', 500);
    }
  }

  /**
   * 获取有效配置（前端使用）
   */
  static async getActiveConfigs(configType?: ConfigType, groupName?: string): Promise<DisplayConfig[]> {
    try {
      const configs = await MiniprogramConfigModel.findActiveConfigs(configType, groupName);
      
      // 转换为前端展示格式
      return configs.map(config => this.convertToDisplayConfig(config));
    } catch (error) {
      logger.error('Error in getActiveConfigs service:', error);
      throw new AppError('Failed to get active configs', 500);
    }
  }

  /**
   * 根据类型获取配置
   */
  static async getConfigsByType(configType: ConfigType): Promise<MiniprogramConfig[]> {
    try {
      const result = await MiniprogramConfigModel.findMany({ config_type: configType });
      return result.configs;
    } catch (error) {
      logger.error('Error in getConfigsByType service:', error);
      throw new AppError('Failed to get configs by type', 500);
    }
  }

  /**
   * 根据分组获取配置
   */
  static async getConfigsByGroup(groupName: string): Promise<MiniprogramConfig[]> {
    try {
      const result = await MiniprogramConfigModel.findMany({ group_name: groupName });
      return result.configs;
    } catch (error) {
      logger.error('Error in getConfigsByGroup service:', error);
      throw new AppError('Failed to get configs by group', 500);
    }
  }

  /**
   * 更新配置
   */
  static async updateConfig(id: number, updateData: UpdateMiniprogramConfigDto): Promise<MiniprogramConfig> {
    try {
      // 如果更新配置值，需要验证
      if (updateData.config_value) {
        const existingConfig = await MiniprogramConfigModel.findById(id);
        if (existingConfig) {
          this.validateConfigData(existingConfig.config_type, updateData.config_value);
        }
      }

      const config = await MiniprogramConfigModel.update(id, updateData);
      
      if (!config) {
        throw new AppError('Config not found', 404);
      }

      logger.info(`Miniprogram config updated successfully: ${id}`);
      return config;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in updateConfig service:', error);
      throw new AppError('Failed to update config', 500);
    }
  }

  /**
   * 删除配置
   */
  static async deleteConfig(id: number, operator: string = 'system'): Promise<void> {
    try {
      const success = await MiniprogramConfigModel.delete(id, operator);
      
      if (!success) {
        throw new AppError('Config not found', 404);
      }

      logger.info(`Miniprogram config deleted successfully: ${id}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in deleteConfig service:', error);
      throw new AppError('Failed to delete config', 500);
    }
  }

  /**
   * 批量更新配置状态
   */
  static async batchUpdateStatus(batchData: BatchUpdateConfigDto): Promise<number> {
    try {
      const { config_ids, updates, operator = 'system' } = batchData;
      
      if (updates.is_enabled !== undefined) {
        return await MiniprogramConfigModel.batchUpdateStatus(config_ids, updates.is_enabled, operator);
      }

      // 如果不是状态更新，逐个更新
      let updatedCount = 0;
      for (const configId of config_ids) {
        const result = await MiniprogramConfigModel.update(configId, { ...updates, updated_by: operator });
        if (result) {
          updatedCount++;
        }
      }

      logger.info(`Batch updated ${updatedCount} configs`);
      return updatedCount;
    } catch (error) {
      logger.error('Error in batchUpdateStatus service:', error);
      throw new AppError('Failed to batch update configs', 500);
    }
  }

  /**
   * 获取配置统计信息
   */
  static async getStatistics(): Promise<ConfigStatistics> {
    try {
      return await MiniprogramConfigModel.getStatistics();
    } catch (error) {
      logger.error('Error in getStatistics service:', error);
      throw new AppError('Failed to get statistics', 500);
    }
  }

  /**
   * 获取配置分组信息
   */
  static async getGroups(): Promise<ConfigGroup[]> {
    try {
      return await MiniprogramConfigModel.getGroups();
    } catch (error) {
      logger.error('Error in getGroups service:', error);
      throw new AppError('Failed to get groups', 500);
    }
  }

  /**
   * 获取所有配置并按类型分组（小程序统一接口）
   */
  static async getAllConfigsGrouped(): Promise<Record<string, DisplayConfig[]>> {
    try {
      const configs = await MiniprogramConfigModel.findActiveConfigs();

      // 按配置类型分组
      const groupedConfigs: Record<string, DisplayConfig[]> = {};

      configs.forEach(config => {
        const displayConfig = this.convertToDisplayConfig(config);
        const type = config.config_type;

        if (!groupedConfigs[type]) {
          groupedConfigs[type] = [];
        }

        groupedConfigs[type].push(displayConfig);
      });

      // 对每个类型的配置按order排序
      Object.keys(groupedConfigs).forEach(type => {
        if (groupedConfigs[type]) {
          groupedConfigs[type].sort((a, b) => a.order - b.order);
        }
      });

      logger.info('Successfully retrieved all configs grouped by type');
      return groupedConfigs;
    } catch (error) {
      logger.error('Error in getAllConfigsGrouped service:', error);
      throw new AppError('Failed to get grouped configs', 500);
    }
  }

  /**
   * 验证配置数据
   */
  private static validateConfigData(configType: ConfigType, configValue: any): void {
    switch (configType) {
      case ConfigType.CAROUSEL:
        this.validateCarouselConfig(configValue);
        break;
      case ConfigType.ANNOUNCEMENT:
        this.validateAnnouncementConfig(configValue);
        break;
      case ConfigType.CONTACT_PHONE:
        this.validateContactPhoneConfig(configValue);
        break;
      case ConfigType.WECHAT_COPY:
        this.validateWechatCopyConfig(configValue);
        break;
      case ConfigType.SHARE_CONFIG:
        this.validateShareConfig(configValue);
        break;
      case ConfigType.SYSTEM_CONFIG:
        // 系统配置较为灵活，只做基本验证
        if (!configValue || typeof configValue !== 'object') {
          throw new AppError('System config value must be an object', 400);
        }
        break;
      default:
        throw new AppError('Invalid config type', 400);
    }
  }

  /**
   * 验证轮播图配置
   */
  private static validateCarouselConfig(value: any): void {
    if (!value.image_url || !value.title) {
      throw new AppError('Carousel config must have image_url and title', 400);
    }
    if (value.link_type && !['page', 'url', 'none'].includes(value.link_type)) {
      throw new AppError('Invalid link_type for carousel config', 400);
    }
  }

  /**
   * 验证公告配置
   */
  private static validateAnnouncementConfig(value: any): void {
    if (!value.title || !value.content) {
      throw new AppError('Announcement config must have title and content', 400);
    }
    if (value.type && !['info', 'warning', 'success', 'error'].includes(value.type)) {
      throw new AppError('Invalid type for announcement config', 400);
    }
  }

  /**
   * 验证电话联系配置
   */
  private static validateContactPhoneConfig(value: any): void {
    if (!value.phone_number || !value.display_name) {
      throw new AppError('Contact phone config must have phone_number and display_name', 400);
    }
  }

  /**
   * 验证微信复制配置
   */
  private static validateWechatCopyConfig(value: any): void {
    if (!value.wechat_id || !value.display_name) {
      throw new AppError('WeChat copy config must have wechat_id and display_name', 400);
    }
  }

  /**
   * 验证分享配置
   */
  private static validateShareConfig(value: any): void {
    if (!value.title || !value.description) {
      throw new AppError('Share config must have title and description', 400);
    }
  }

  /**
   * 转换为前端展示格式
   */
  private static convertToDisplayConfig(config: MiniprogramConfig): DisplayConfig {
    return {
      id: config.id,
      type: config.config_type,
      key: config.config_key,
      name: config.config_name,
      value: config.config_value,
      enabled: config.is_enabled,
      order: config.display_order,
      group: config.group_name,
      description: config.description,
      valid_period: {
        start: config.start_time,
        end: config.end_time
      }
    };
  }
}
