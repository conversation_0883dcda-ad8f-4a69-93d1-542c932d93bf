import { 
  ShareConfig, 
  CreateShareConfigDto, 
  UpdateShareConfigDto, 
  ShareType,
  ShareScope,
  AppError 
} from '../types';
import { ShareConfigModel } from '../models/ShareConfig';
import { ContactModel } from '../models/Contact';
import logger from '../config/logger';

export class ShareConfigService {
  /**
   * Create a new share config
   */
  static async createShareConfig(userId: string, configData: CreateShareConfigDto): Promise<ShareConfig> {
    try {
      // Validate that the contact exists and belongs to the user
      const contact = await ContactModel.findById(configData.contactId, userId);
      if (!contact) {
        throw new AppError('Contact not found', 404);
      }

      // Validate share scope and allowed users
      if (configData.shareScope === ShareScope.PRIVATE && (!configData.allowedUsers || configData.allowedUsers.length === 0)) {
        throw new AppError('Private share configs must specify allowed users', 400);
      }

      // Validate expiration date
      if (configData.expiresAt && configData.expiresAt <= new Date()) {
        throw new AppError('Expiration date must be in the future', 400);
      }

      const shareConfig = await ShareConfigModel.create(userId, configData);
      
      logger.info(`Share config created successfully: ${shareConfig.id} for user: ${userId}`);
      return shareConfig;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in createShareConfig service:', error);
      throw new AppError('Failed to create share config', 500);
    }
  }

  /**
   * Get share config by ID
   */
  static async getShareConfigById(configId: number, userId: string): Promise<ShareConfig> {
    try {
      const shareConfig = await ShareConfigModel.findById(configId, userId);
      
      if (!shareConfig) {
        throw new AppError('Share config not found', 404);
      }

      return shareConfig;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in getShareConfigById service:', error);
      throw new AppError('Failed to get share config', 500);
    }
  }

  /**
   * Get share configs by contact ID
   */
  static async getShareConfigsByContactId(contactId: number, userId: string): Promise<ShareConfig[]> {
    try {
      // Validate that the contact exists and belongs to the user
      const contact = await ContactModel.findById(contactId, userId);
      if (!contact) {
        throw new AppError('Contact not found', 404);
      }

      const shareConfigs = await ShareConfigModel.findByContactId(contactId, userId);
      return shareConfigs;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in getShareConfigsByContactId service:', error);
      throw new AppError('Failed to get share configs', 500);
    }
  }

  /**
   * Update share config
   */
  static async updateShareConfig(configId: number, userId: string, updateData: UpdateShareConfigDto): Promise<ShareConfig> {
    try {
      // Validate share scope and allowed users
      if (updateData.shareScope === ShareScope.PRIVATE && (!updateData.allowedUsers || updateData.allowedUsers.length === 0)) {
        throw new AppError('Private share configs must specify allowed users', 400);
      }

      // Validate expiration date
      if (updateData.expiresAt && updateData.expiresAt <= new Date()) {
        throw new AppError('Expiration date must be in the future', 400);
      }

      const shareConfig = await ShareConfigModel.update(configId, userId, updateData);
      
      if (!shareConfig) {
        throw new AppError('Share config not found', 404);
      }

      logger.info(`Share config updated successfully: ${configId} for user: ${userId}`);
      return shareConfig;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in updateShareConfig service:', error);
      throw new AppError('Failed to update share config', 500);
    }
  }

  /**
   * Delete share config
   */
  static async deleteShareConfig(configId: number, userId: string): Promise<void> {
    try {
      const success = await ShareConfigModel.delete(configId, userId);
      
      if (!success) {
        throw new AppError('Share config not found', 404);
      }

      logger.info(`Share config deleted successfully: ${configId} for user: ${userId}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in deleteShareConfig service:', error);
      throw new AppError('Failed to delete share config', 500);
    }
  }

  /**
   * Generate share link/QR code
   */
  static async generateShareLink(configId: number, userId: string): Promise<{ shareUrl: string; qrCodeUrl?: string }> {
    try {
      const shareConfig = await ShareConfigModel.findById(configId, userId);
      
      if (!shareConfig) {
        throw new AppError('Share config not found', 404);
      }

      // Check if config is expired
      if (shareConfig.expiresAt && shareConfig.expiresAt <= new Date()) {
        throw new AppError('Share config has expired', 400);
      }

      // Generate share URL (in a real app, this would be your domain)
      const baseUrl = process.env.FRONTEND_URL || 'https://your-app.com';
      const shareUrl = `${baseUrl}/share/contact/${configId}`;

      // Update share count
      await ShareConfigModel.updateShareCount(configId);

      logger.info(`Share link generated for config: ${configId}`);
      
      return {
        shareUrl,
        qrCodeUrl: `${baseUrl}/api/qr?url=${encodeURIComponent(shareUrl)}` // QR code generation endpoint
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in generateShareLink service:', error);
      throw new AppError('Failed to generate share link', 500);
    }
  }

  /**
   * Access shared contact (for public viewing)
   */
  static async accessSharedContact(configId: number, visitorUserId?: string): Promise<{ contact: any; shareConfig: ShareConfig }> {
    try {
      // Get share config (without user restriction for public access)
      const sql = 'SELECT * FROM contact_share_configs WHERE id = ? AND is_active = 1';
      const { db } = await import('../config/database');
      const rows = await db.query(sql, [configId]);
      
      if (!rows || rows.length === 0) {
        throw new AppError('Share config not found', 404);
      }

      const shareConfig = ShareConfigModel.mapRowToShareConfig(rows[0]);

      // Check if config is expired
      if (shareConfig.expiresAt && shareConfig.expiresAt <= new Date()) {
        throw new AppError('Share link has expired', 400);
      }

      // Check access permissions
      if (!this.canAccessShareConfig(shareConfig, visitorUserId)) {
        throw new AppError('Access denied', 403);
      }

      // Get the contact
      const contact = await ContactModel.findById(shareConfig.contactId, shareConfig.userId);
      if (!contact) {
        throw new AppError('Contact not found', 404);
      }

      // Filter contact data based on share config settings
      const filteredContact = this.filterContactData(contact, shareConfig);

      // Update view count
      await ShareConfigModel.updateViewCount(configId);

      logger.info(`Shared contact accessed: ${configId} by visitor: ${visitorUserId || 'anonymous'}`);

      return {
        contact: filteredContact,
        shareConfig
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in accessSharedContact service:', error);
      throw new AppError('Failed to access shared contact', 500);
    }
  }

  /**
   * Check if user can access share config
   */
  private static canAccessShareConfig(shareConfig: ShareConfig, visitorUserId?: string): boolean {
    switch (shareConfig.shareScope) {
      case ShareScope.PUBLIC:
        return true;
      case ShareScope.FRIENDS:
        // In a real implementation, you would check if the visitor is a friend
        return visitorUserId !== undefined;
      case ShareScope.PRIVATE:
        return shareConfig.allowedUsers?.includes(visitorUserId || '') || shareConfig.userId === visitorUserId;
      default:
        return false;
    }
  }

  /**
   * Filter contact data based on share config settings
   */
  private static filterContactData(contact: any, shareConfig: ShareConfig): any {
    const filteredContact: any = {
      id: contact.id,
      name: contact.name,
      avatarUrl: contact.avatarUrl,
      category: contact.category,
      tags: contact.tags,
    };

    if (shareConfig.includePhone) {
      filteredContact.phone = contact.phone;
    }

    if (shareConfig.includeWechat) {
      filteredContact.wechatId = contact.wechatId;
    }

    if (shareConfig.includeEmail) {
      filteredContact.email = contact.email;
    }

    if (shareConfig.includeCompany) {
      filteredContact.company = contact.company;
      filteredContact.position = contact.position;
    }

    if (shareConfig.includeAddress) {
      filteredContact.address = contact.address;
    }

    if (shareConfig.includeNotes) {
      filteredContact.notes = contact.notes;
    }

    return filteredContact;
  }

  /**
   * Get share statistics
   */
  static async getShareStats(configId: number, userId: string): Promise<any> {
    try {
      const shareConfig = await ShareConfigModel.findById(configId, userId);
      
      if (!shareConfig) {
        throw new AppError('Share config not found', 404);
      }

      return {
        shareCount: shareConfig.shareCount,
        viewCount: shareConfig.viewCount,
        lastSharedAt: shareConfig.lastSharedAt,
        lastViewedAt: shareConfig.lastViewedAt,
        isActive: shareConfig.isActive,
        expiresAt: shareConfig.expiresAt,
        createdAt: shareConfig.createdAt
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Error in getShareStats service:', error);
      throw new AppError('Failed to get share statistics', 500);
    }
  }
}
