import { AdminLoginDto, AuthTokens, Admin, CreateAdminDto, UpdateAdminDto, AppError, JwtPayload } from '../types';
import { AdminModel } from '../models/Admin';
import { generateToken, verifyToken } from '../utils/jwt';
import { comparePassword, hashPassword } from '../utils/password';
import logger from '../config/logger';

export class AdminAuthService {
  /**
   * Admin login
   */
  static async login(loginData: AdminLoginDto): Promise<{ admin: Admin; tokens: AuthTokens }> {
    logger.info('Admin login attempt:', { email: loginData.email, username: loginData.username });

    let admin: Admin | null = null;

    // Find admin by email or username
    if (loginData.email) {
      logger.info('Finding admin by email:', loginData.email);
      admin = await AdminModel.findByEmail(loginData.email);
    } else if (loginData.username) {
      logger.info('Finding admin by username:', loginData.username);
      admin = await AdminModel.findByUsername(loginData.username);
    }

    logger.info('Admin found:', admin ? { id: admin.id, email: admin.email } : 'null');

    if (!admin) {
      throw new AppError('Invalid credentials', 401);
    }

    // Check if admin is active
    if (!admin.isActive) {
      throw new AppError('Admin account is disabled', 401);
    }

    // Get password hash and verify
    const passwordHash = await AdminModel.getPasswordHash(admin.id);
    if (!passwordHash) {
      throw new AppError('Invalid credentials', 401);
    }

    const isPasswordValid = await comparePassword(loginData.password, passwordHash);
    if (!isPasswordValid) {
      throw new AppError('Invalid credentials', 401);
    }

    // Update last login
    await AdminModel.updateLastLogin(admin.id, 'unknown');

    const tokens = this.generateTokens(admin);
    return { admin, tokens };
  }

  /**
   * Get admin profile
   */
  static async getProfile(adminId: string): Promise<Admin> {
    const admin = await AdminModel.findById(adminId);
    if (!admin) {
      throw new AppError('Admin not found', 404);
    }
    return admin;
  }

  /**
   * Update admin profile
   */
  static async updateProfile(adminId: string, updateData: UpdateAdminDto): Promise<Admin> {
    const admin = await AdminModel.update(adminId, updateData);
    if (!admin) {
      throw new AppError('Admin not found', 404);
    }
    return admin;
  }

  /**
   * Change admin password
   */
  static async changePassword(adminId: string, currentPassword: string, newPassword: string): Promise<void> {
    const admin = await AdminModel.findById(adminId);
    if (!admin) {
      throw new AppError('Admin not found', 404);
    }

    // Verify current password
    const currentPasswordHash = await AdminModel.getPasswordHash(adminId);
    if (!currentPasswordHash) {
      throw new AppError('Invalid current password', 400);
    }

    const isCurrentPasswordValid = await comparePassword(currentPassword, currentPasswordHash);
    if (!isCurrentPasswordValid) {
      throw new AppError('Invalid current password', 400);
    }

    // Hash new password and update
    const newPasswordHash = await hashPassword(newPassword);
    await AdminModel.updatePassword(adminId, newPasswordHash);

    logger.info(`Admin ${adminId} changed password successfully`);
  }

  /**
   * Admin logout
   */
  static async logout(adminId: string): Promise<void> {
    try {
      // In a real implementation, you would:
      // 1. Add the refresh token to a blacklist
      // 2. Delete user session from database
      // 3. Update user's token version number

      logger.info(`Admin ${adminId} logged out successfully`);

      // TODO: Implement token blacklist or session management
      return;
    } catch (error) {
      logger.error('Admin logout error:', error);
      throw new AppError('Logout failed', 500);
    }
  }

  /**
   * Create new admin
   */
  static async createAdmin(adminData: CreateAdminDto): Promise<Admin> {
    try {
      return await AdminModel.create(adminData);
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        throw new AppError(error.message, 409);
      }
      throw new AppError('Failed to create admin', 500);
    }
  }

  /**
   * Generate JWT tokens for admin
   */
  private static generateTokens(admin: Admin): AuthTokens {
    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      userId: admin.id,
      phone: '', // Admins don't have phone numbers in this system

      role: admin.role as any, // Convert AdminRole to UserRole for JWT
    };

    const accessToken = generateToken(payload);

    return {
      accessToken,
      refreshToken: accessToken, // For simplicity, using same token
    };
  }
}
