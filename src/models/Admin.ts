import { Admin, CreateAdminDto, UpdateAdminDto, AdminRole, AdminQuery } from '../types';
import { hashPassword } from '../utils/password';
import { adminDb } from '../config/adminDatabase';
import { v4 as uuidv4 } from 'uuid';

export class AdminModel {
  /**
   * Create a new admin
   */
  static async create(adminData: CreateAdminDto): Promise<Admin> {
    // Check if admin already exists by email or username
    const existingAdmin = await this.findByEmailOrUsername(adminData.email, adminData.username);
    if (existingAdmin) {
      throw new Error('Admin with this email or username already exists');
    }

    // Hash password
    const hashedPassword = await hashPassword(adminData.password);

    // Generate ID
    const id = uuidv4();

    // Insert admin into database
    const sql = `
      INSERT INTO admins (
        id, email, username, password_hash, first_name, last_name, 
        role, is_active, permissions, created_by, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const params = [
      id,
      adminData.email,
      adminData.username,
      hashedPassword,
      adminData.firstName || null,
      adminData.lastName || null,
      adminData.role,
      1, // is_active = true
      adminData.permissions ? JSON.stringify(adminData.permissions) : null,
      adminData.createdBy || null,
    ];

    await adminDb.query(sql, params);

    // Return the created admin
    const admin = await this.findById(id);
    if (!admin) {
      throw new Error('Failed to create admin');
    }

    return admin;
  }

  /**
   * Find admin by ID
   */
  static async findById(id: string): Promise<Admin | null> {
    const sql = 'SELECT * FROM admins WHERE id = ?';
    const rows = await adminDb.query(sql, [id]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToAdmin(rows[0]);
  }

  /**
   * Find admin by email
   */
  static async findByEmail(email: string): Promise<Admin | null> {
    const sql = 'SELECT * FROM admins WHERE email = ?';
    const rows = await adminDb.query(sql, [email]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToAdmin(rows[0]);
  }

  /**
   * Find admin by username
   */
  static async findByUsername(username: string): Promise<Admin | null> {
    const sql = 'SELECT * FROM admins WHERE username = ?';
    const rows = await adminDb.query(sql, [username]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToAdmin(rows[0]);
  }

  /**
   * Find admin by email or username
   */
  static async findByEmailOrUsername(email: string, username: string): Promise<Admin | null> {
    const sql = 'SELECT * FROM admins WHERE email = ? OR username = ?';
    const rows = await adminDb.query(sql, [email, username]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToAdmin(rows[0]);
  }

  /**
   * Get password hash for authentication
   */
  static async getPasswordHash(id: string): Promise<string | null> {
    const sql = 'SELECT password_hash FROM admins WHERE id = ?';
    const rows = await adminDb.query(sql, [id]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return rows[0].password_hash;
  }

  /**
   * Update admin
   */
  static async update(id: string, updateData: UpdateAdminDto): Promise<Admin | null> {
    // Check if admin exists
    const existingAdmin = await this.findById(id);
    if (!existingAdmin) {
      return null;
    }

    // Check for duplicate email/username if being updated
    if (updateData.email || updateData.username) {
      const duplicateAdmin = await adminDb.query(
        'SELECT id FROM admins WHERE (email = ? OR username = ?) AND id != ?',
        [updateData.email || '', updateData.username || '', id]
      );

      if (duplicateAdmin && duplicateAdmin.length > 0) {
        throw new Error('Admin with this email or username already exists');
      }
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const params: any[] = [];

    if (updateData.email !== undefined) {
      updateFields.push('email = ?');
      params.push(updateData.email);
    }
    if (updateData.username !== undefined) {
      updateFields.push('username = ?');
      params.push(updateData.username);
    }
    if (updateData.firstName !== undefined) {
      updateFields.push('first_name = ?');
      params.push(updateData.firstName);
    }
    if (updateData.lastName !== undefined) {
      updateFields.push('last_name = ?');
      params.push(updateData.lastName);
    }
    if (updateData.avatar !== undefined) {
      updateFields.push('avatar = ?');
      params.push(updateData.avatar);
    }
    if (updateData.role !== undefined) {
      updateFields.push('role = ?');
      params.push(updateData.role);
    }
    if (updateData.isActive !== undefined) {
      updateFields.push('is_active = ?');
      params.push(updateData.isActive ? 1 : 0);
    }
    if (updateData.permissions !== undefined) {
      updateFields.push('permissions = ?');
      params.push(updateData.permissions ? JSON.stringify(updateData.permissions) : null);
    }

    if (updateFields.length === 0) {
      return existingAdmin; // No changes
    }

    updateFields.push('updated_at = NOW()');
    params.push(id);

    const sql = `UPDATE admins SET ${updateFields.join(', ')} WHERE id = ?`;
    await adminDb.query(sql, params);

    // Return updated admin
    return await this.findById(id);
  }

  /**
   * Update admin's last login information
   */
  static async updateLastLogin(id: string, ip?: string): Promise<Admin | null> {
    const sql = 'UPDATE admins SET last_login_at = NOW(), last_login_ip = ?, updated_at = NOW() WHERE id = ?';
    await adminDb.query(sql, [ip || null, id]);

    return await this.findById(id);
  }

  /**
   * Update admin password
   */
  static async updatePassword(id: string, passwordHash: string): Promise<void> {
    const sql = 'UPDATE admins SET password_hash = ?, updated_at = NOW() WHERE id = ?';
    await adminDb.query(sql, [passwordHash, id]);
  }

  /**
   * Find admins with pagination and filtering
   */
  static async findMany(query: AdminQuery): Promise<{ admins: Admin[]; total: number }> {
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // Apply filters
    if (query.search) {
      whereClause += ' AND (email LIKE ? OR username LIKE ? OR first_name LIKE ? OR last_name LIKE ?)';
      const searchTerm = `%${query.search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    if (query.role) {
      whereClause += ' AND role = ?';
      params.push(query.role);
    }

    if (query.isActive !== undefined) {
      whereClause += ' AND is_active = ?';
      params.push(query.isActive ? 1 : 0);
    }

    // Get total count
    const countSql = `SELECT COUNT(*) as total FROM admins ${whereClause}`;
    const countResult = await adminDb.query(countSql, params);
    const total = countResult[0].total;

    // Apply pagination and sorting
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;
    const offset = (page - 1) * limit;

    const sortBy = query.sortBy || 'created_at';
    const sortOrder = query.sortOrder || 'desc';

    const dataSql = `
      SELECT * FROM admins ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    const dataParams = [...params, limit, offset];
    const rows = await adminDb.query(dataSql, dataParams);

    const admins = rows.map((row: any) => this.mapRowToAdmin(row));

    return { admins, total };
  }

  /**
   * Delete admin (soft delete by setting is_active = false)
   */
  static async delete(id: string): Promise<boolean> {
    const sql = 'UPDATE admins SET is_active = 0, updated_at = NOW() WHERE id = ?';
    const result = await adminDb.query(sql, [id]);
    
    return result.affectedRows > 0;
  }

  /**
   * Map database row to Admin object
   */
  private static mapRowToAdmin(row: any): Admin {
    return {
      id: row.id,
      email: row.email,
      username: row.username,
      firstName: row.first_name || undefined,
      lastName: row.last_name || undefined,
      avatar: row.avatar || undefined,
      role: row.role as AdminRole,
      isActive: Boolean(row.is_active),
      permissions: row.permissions ? (typeof row.permissions === 'string' ? JSON.parse(row.permissions) : row.permissions) : undefined,
      lastLoginAt: row.last_login_at ? new Date(row.last_login_at) : undefined,
      lastLoginIp: row.last_login_ip || undefined,
      createdBy: row.created_by || undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}
