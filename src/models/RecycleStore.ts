import db from '../config/knex';
import logger from '../config/logger';
import {
  RecycleStore,
  CreateRecycleStoreDto,
  UpdateRecycleStoreDto,
  BusinessStatus,
  StoreType
} from '../types';

export class RecycleStoreModel {
  /**
   * 创建回收门店
   */
  static async create(storeData: CreateRecycleStoreDto): Promise<RecycleStore> {
    // 检查门店编码是否已存在
    const existingStore = await this.findByStoreCode(storeData.storeCode);
    if (existingStore) {
      throw new Error('Store code already exists');
    }

    // 构建完整地址
    const fullAddress = `${storeData.province}${storeData.city}${storeData.district}${storeData.detailedAddress}`;

    const insertData = {
      store_code: storeData.storeCode,
      store_name: storeData.storeName,
      province: storeData.province,
      city: storeData.city,
      district: storeData.district,
      detailed_address: storeData.detailedAddress,
      full_address: fullAddress,
      longitude: storeData.longitude || null,
      latitude: storeData.latitude || null,
      sales_phone: storeData.salesPhone || null,
      sales_wechat: storeData.salesWechat || null,
      recycle_phone: storeData.recyclePhone || null,
      recycle_wechat: storeData.recycleWechat || null,
      main_phone: storeData.mainPhone,
      email: storeData.email || null,
      business_hours: storeData.businessHours ? JSON.stringify(storeData.businessHours) : null,
      business_status: storeData.businessStatus || 'OPEN',
      store_type: storeData.storeType || 'STANDARD',
      store_area: storeData.storeArea || null,
      service_radius: storeData.serviceRadius || 5,
      supported_categories: storeData.supportedCategories ? JSON.stringify(storeData.supportedCategories) : null,
      status: 1,
      created_at: db.fn.now(),
      updated_at: db.fn.now()
    };

    const result = await db('recycle_stores').insert(insertData);
    const insertId = result[0] as number;

    const newStore = await this.findById(insertId);
    if (!newStore) {
      throw new Error('Failed to create store');
    }

    return newStore;
  }

  /**
   * 根据ID查找门店
   */
  static async findById(id: number): Promise<RecycleStore | null> {
    const result = await db('recycle_stores')
      .where({ id, status: 1 })
      .first();

    if (!result) {
      return null;
    }

    return this.formatStoreData(result);
  }

  /**
   * 根据门店编码查找门店
   */
  static async findByStoreCode(storeCode: string): Promise<RecycleStore | null> {
    const result = await db('recycle_stores')
      .where({ store_code: storeCode, status: 1 })
      .first();

    if (!result) {
      return null;
    }

    return this.formatStoreData(result);
  }

  /**
   * 获取门店列表
   */
  static async findAll(options: {
    page?: number;
    limit?: number;
    province?: string;
    city?: string;
    district?: string;
    businessStatus?: BusinessStatus;
    storeType?: StoreType;
    search?: string;
  } = {}): Promise<{ stores: RecycleStore[]; total: number }> {
    const { page = 1, limit = 20, province, city, district, businessStatus, storeType, search } = options;
    const offset = (page - 1) * limit;

    let query = db('recycle_stores').where('status', 1);

    if (province) {
      query = query.where('province', province);
    }

    if (city) {
      query = query.where('city', city);
    }

    if (district) {
      query = query.where('district', district);
    }

    if (businessStatus) {
      query = query.where('business_status', businessStatus);
    }

    if (storeType) {
      query = query.where('store_type', storeType);
    }

    if (search) {
      const searchPattern = `%${search}%`;
      query = query.where(function() {
        this.where('store_name', 'like', searchPattern)
          .orWhere('store_code', 'like', searchPattern)
          .orWhere('full_address', 'like', searchPattern);
      });
    }

    // 获取总数
    const countResult = await query.clone().count('* as total');
    const total = (countResult[0] && countResult[0]['total']) ? countResult[0]['total'] as number : 0;

    // 获取数据
    const dataResult = await query
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const stores = dataResult.map((row: any) => this.formatStoreData(row));

    return { stores, total };
  }

  /**
   * 更新门店信息
   */
  static async update(id: number, updateData: UpdateRecycleStoreDto): Promise<RecycleStore> {
    const existingStore = await this.findById(id);
    if (!existingStore) {
      throw new Error('Store not found');
    }

    const updateFields: string[] = [];
    const values: any[] = [];

    // 动态构建更新字段
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        if (key === 'businessHours' || key === 'supportedCategories') {
          updateFields.push(`${dbField} = ?`);
          values.push(JSON.stringify(value));
        } else {
          updateFields.push(`${dbField} = ?`);
          values.push(value);
        }
      }
    });

    // 如果地址相关字段有更新，重新构建完整地址
    if (updateData.province || updateData.city || updateData.district || updateData.detailedAddress) {
      const province = updateData.province || existingStore.province;
      const city = updateData.city || existingStore.city;
      const district = updateData.district || existingStore.district;
      const detailedAddress = updateData.detailedAddress || existingStore.detailedAddress;
      const fullAddress = `${province}${city}${district}${detailedAddress}`;
      
      updateFields.push('full_address = ?');
      values.push(fullAddress);
    }

    updateFields.push('updated_at = NOW()');
    values.push(id);

    // 构建更新对象
    const updateObject: any = {};
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined) {
        const dbField = this.camelToSnake(key);
        if (key === 'businessHours' || key === 'supportedCategories') {
          updateObject[dbField] = JSON.stringify(value);
        } else {
          updateObject[dbField] = value;
        }
      }
    });

    // 如果地址相关字段有更新，重新构建完整地址
    if (updateData.province || updateData.city || updateData.district || updateData.detailedAddress) {
      const province = updateData.province || existingStore.province;
      const city = updateData.city || existingStore.city;
      const district = updateData.district || existingStore.district;
      const detailedAddress = updateData.detailedAddress || existingStore.detailedAddress;
      const fullAddress = `${province}${city}${district}${detailedAddress}`;
      updateObject.full_address = fullAddress;
    }

    updateObject.updated_at = db.fn.now();

    await db('recycle_stores').where('id', id).update(updateObject);

    const updatedStore = await this.findById(id);
    if (!updatedStore) {
      throw new Error('Failed to update store');
    }

    return updatedStore;
  }

  /**
   * 软删除门店
   */
  static async delete(id: number): Promise<boolean> {
    const result = await db('recycle_stores')
      .where('id', id)
      .update({
        status: 0,
        updated_at: db.fn.now()
      });
    return result > 0;
  }

  /**
   * 根据位置查找附近的门店
   */
  static async findNearby(longitude: number, latitude: number, radius: number = 10): Promise<RecycleStore[]> {
    const result = await db.raw(`
      SELECT *,
        (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
      FROM recycle_stores
      WHERE status = 1 AND longitude IS NOT NULL AND latitude IS NOT NULL
      HAVING distance < ?
      ORDER BY distance
      LIMIT 20
    `, [latitude, longitude, latitude, radius]);

    return result[0].map((row: any) => this.formatStoreData(row));
  }

  /**
   * 格式化门店数据
   */
  private static formatStoreData(row: any): RecycleStore {
    return {
      id: row.id,
      storeCode: row.store_code,
      storeName: row.store_name,
      province: row.province,
      city: row.city,
      district: row.district,
      detailedAddress: row.detailed_address,
      fullAddress: row.full_address,
      longitude: row.longitude,
      latitude: row.latitude,
      salesPhone: row.sales_phone,
      salesWechat: row.sales_wechat,
      recyclePhone: row.recycle_phone,
      recycleWechat: row.recycle_wechat,
      mainPhone: row.main_phone,
      email: row.email,
      businessHours: row.business_hours ? JSON.parse(row.business_hours) : undefined,
      businessStatus: row.business_status,
      storeType: row.store_type,
      storeArea: row.store_area,
      employeeCount: row.employee_count,
      serviceRadius: row.service_radius,
      supportedCategories: row.supported_categories ? JSON.parse(row.supported_categories) : undefined,
      status: row.status,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * 驼峰转下划线
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
