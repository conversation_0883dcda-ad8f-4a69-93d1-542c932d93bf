import { 
  ShareConfig, 
  CreateShareConfigDto, 
  UpdateShareConfigDto, 
  ShareType, 
  ShareScope,
  AccessLog,
  AccessType 
} from '../types';
import { db } from '../config/database';
import logger from '../config/logger';

export class ShareConfigModel {
  /**
   * Create a new share config
   */
  static async create(userId: string, configData: CreateShareConfigDto): Promise<ShareConfig> {
    // Check if share config already exists for this contact and type
    const existingConfig = await this.findByContactAndType(userId, configData.contactId, configData.shareType);
    if (existingConfig) {
      throw new Error('Share config for this contact and type already exists');
    }

    const sql = `
      INSERT INTO contact_share_configs (
        user_id, contact_id, share_type, share_title, share_description, share_image_url,
        share_scope, allowed_users, include_phone, include_wechat, include_email,
        include_company, include_address, include_notes, expires_at, is_active,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;

    const params = [
      userId,
      configData.contactId,
      configData.shareType,
      configData.shareTitle || null,
      configData.shareDescription || null,
      configData.shareImageUrl || null,
      configData.shareScope || ShareScope.PRIVATE,
      configData.allowedUsers ? JSON.stringify(configData.allowedUsers) : null,
      configData.includePhone ?? true,
      configData.includeWechat ?? true,
      configData.includeEmail ?? false,
      configData.includeCompany ?? false,
      configData.includeAddress ?? false,
      configData.includeNotes ?? false,
      configData.expiresAt || null,
    ];

    try {
      const result = await db.query(sql, params);
      const insertId = (result as any).insertId;
      
      // Return the created share config
      const shareConfig = await this.findById(insertId, userId);
      if (!shareConfig) {
        throw new Error('Failed to create share config');
      }

      logger.info(`Share config created: ${insertId} for user: ${userId}`);
      return shareConfig;
    } catch (error) {
      logger.error('Error creating share config:', error);
      throw error;
    }
  }

  /**
   * Find share config by ID
   */
  static async findById(id: number, userId: string): Promise<ShareConfig | null> {
    const sql = 'SELECT * FROM contact_share_configs WHERE id = ? AND user_id = ? AND is_active = 1';
    
    try {
      const rows = await db.query(sql, [id, userId]);
      if (!rows || rows.length === 0) {
        return null;
      }
      
      return this.mapRowToShareConfig(rows[0]);
    } catch (error) {
      logger.error('Error finding share config by ID:', error);
      throw error;
    }
  }

  /**
   * Find share config by contact and type
   */
  static async findByContactAndType(userId: string, contactId: number, shareType: ShareType): Promise<ShareConfig | null> {
    const sql = 'SELECT * FROM contact_share_configs WHERE user_id = ? AND contact_id = ? AND share_type = ? AND is_active = 1';
    
    try {
      const rows = await db.query(sql, [userId, contactId, shareType]);
      if (!rows || rows.length === 0) {
        return null;
      }
      
      return this.mapRowToShareConfig(rows[0]);
    } catch (error) {
      logger.error('Error finding share config by contact and type:', error);
      throw error;
    }
  }

  /**
   * Find share configs by contact ID
   */
  static async findByContactId(contactId: number, userId: string): Promise<ShareConfig[]> {
    const sql = 'SELECT * FROM contact_share_configs WHERE contact_id = ? AND user_id = ? AND is_active = 1';
    
    try {
      const rows = await db.query(sql, [contactId, userId]);
      return rows.map((row: any) => this.mapRowToShareConfig(row));
    } catch (error) {
      logger.error('Error finding share configs by contact ID:', error);
      throw error;
    }
  }

  /**
   * Update share config
   */
  static async update(id: number, userId: string, updateData: UpdateShareConfigDto): Promise<ShareConfig | null> {
    try {
      // Check if config exists
      const existingConfig = await this.findById(id, userId);
      if (!existingConfig) {
        return null;
      }

      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];

      if (updateData.shareTitle !== undefined) {
        updateFields.push('share_title = ?');
        params.push(updateData.shareTitle);
      }
      if (updateData.shareDescription !== undefined) {
        updateFields.push('share_description = ?');
        params.push(updateData.shareDescription);
      }
      if (updateData.shareImageUrl !== undefined) {
        updateFields.push('share_image_url = ?');
        params.push(updateData.shareImageUrl);
      }
      if (updateData.shareScope !== undefined) {
        updateFields.push('share_scope = ?');
        params.push(updateData.shareScope);
      }
      if (updateData.allowedUsers !== undefined) {
        updateFields.push('allowed_users = ?');
        params.push(updateData.allowedUsers ? JSON.stringify(updateData.allowedUsers) : null);
      }
      if (updateData.includePhone !== undefined) {
        updateFields.push('include_phone = ?');
        params.push(updateData.includePhone);
      }
      if (updateData.includeWechat !== undefined) {
        updateFields.push('include_wechat = ?');
        params.push(updateData.includeWechat);
      }
      if (updateData.includeEmail !== undefined) {
        updateFields.push('include_email = ?');
        params.push(updateData.includeEmail);
      }
      if (updateData.includeCompany !== undefined) {
        updateFields.push('include_company = ?');
        params.push(updateData.includeCompany);
      }
      if (updateData.includeAddress !== undefined) {
        updateFields.push('include_address = ?');
        params.push(updateData.includeAddress);
      }
      if (updateData.includeNotes !== undefined) {
        updateFields.push('include_notes = ?');
        params.push(updateData.includeNotes);
      }
      if (updateData.expiresAt !== undefined) {
        updateFields.push('expires_at = ?');
        params.push(updateData.expiresAt);
      }
      if (updateData.isActive !== undefined) {
        updateFields.push('is_active = ?');
        params.push(updateData.isActive);
      }

      if (updateFields.length === 0) {
        return existingConfig; // No changes
      }

      updateFields.push('updated_at = NOW()');
      params.push(id, userId);

      const sql = `UPDATE contact_share_configs SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ? AND is_active = 1`;
      await db.query(sql, params);

      // Return updated config
      return await this.findById(id, userId);
    } catch (error) {
      logger.error('Error updating share config:', error);
      throw error;
    }
  }

  /**
   * Delete share config (soft delete)
   */
  static async delete(id: number, userId: string): Promise<boolean> {
    try {
      const sql = 'UPDATE contact_share_configs SET is_active = 0, updated_at = NOW() WHERE id = ? AND user_id = ? AND is_active = 1';
      const result = await db.query(sql, [id, userId]);
      
      return (result as any).affectedRows > 0;
    } catch (error) {
      logger.error('Error deleting share config:', error);
      throw error;
    }
  }

  /**
   * Update share count
   */
  static async updateShareCount(id: number): Promise<ShareConfig | null> {
    try {
      const sql = `
        UPDATE contact_share_configs 
        SET share_count = share_count + 1, last_shared_at = NOW(), updated_at = NOW()
        WHERE id = ? AND is_active = 1
      `;
      
      const result = await db.query(sql, [id]);
      
      if ((result as any).affectedRows === 0) {
        return null;
      }

      // Get the updated config (we need userId for findById, so get it from the config)
      const configSql = 'SELECT user_id FROM contact_share_configs WHERE id = ?';
      const configRows = await db.query(configSql, [id]);
      if (configRows && configRows.length > 0) {
        return await this.findById(id, configRows[0].user_id);
      }
      
      return null;
    } catch (error) {
      logger.error('Error updating share count:', error);
      throw error;
    }
  }

  /**
   * Update view count
   */
  static async updateViewCount(id: number): Promise<ShareConfig | null> {
    try {
      const sql = `
        UPDATE contact_share_configs 
        SET view_count = view_count + 1, last_viewed_at = NOW(), updated_at = NOW()
        WHERE id = ? AND is_active = 1
      `;
      
      const result = await db.query(sql, [id]);
      
      if ((result as any).affectedRows === 0) {
        return null;
      }

      // Get the updated config
      const configSql = 'SELECT user_id FROM contact_share_configs WHERE id = ?';
      const configRows = await db.query(configSql, [id]);
      if (configRows && configRows.length > 0) {
        return await this.findById(id, configRows[0].user_id);
      }
      
      return null;
    } catch (error) {
      logger.error('Error updating view count:', error);
      throw error;
    }
  }

  /**
   * Map database row to ShareConfig object
   */
  public static mapRowToShareConfig(row: any): ShareConfig {
    return {
      id: row.id,
      userId: row.user_id,
      contactId: row.contact_id,
      shareType: row.share_type as ShareType,
      shareTitle: row.share_title || undefined,
      shareDescription: row.share_description || undefined,
      shareImageUrl: row.share_image_url || undefined,
      shareScope: row.share_scope as ShareScope,
      allowedUsers: row.allowed_users ? (typeof row.allowed_users === 'string' ? JSON.parse(row.allowed_users) : row.allowed_users) : [],
      includePhone: Boolean(row.include_phone),
      includeWechat: Boolean(row.include_wechat),
      includeEmail: Boolean(row.include_email),
      includeCompany: Boolean(row.include_company),
      includeAddress: Boolean(row.include_address),
      includeNotes: Boolean(row.include_notes),
      shareCount: row.share_count || 0,
      viewCount: row.view_count || 0,
      lastSharedAt: row.last_shared_at ? new Date(row.last_shared_at) : undefined,
      lastViewedAt: row.last_viewed_at ? new Date(row.last_viewed_at) : undefined,
      expiresAt: row.expires_at ? new Date(row.expires_at) : undefined,
      isActive: Boolean(row.is_active),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}
