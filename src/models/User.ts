import { v4 as uuidv4 } from 'uuid';
import { User, CreateUserDto, UpdateUserDto, UserRole, UserQuery } from '../types';
import { hashPassword } from '../utils/password';
import { db } from '../config/database';

export class UserModel {
  /**
   * Create a new user
   */
  static async create(userData: CreateUserDto): Promise<User> {
    // Check if user already exists by phone or WeChat OpenID
    const existingUser = await this.findExistingUser(userData);
    if (existingUser) {
      throw new Error('User with this phone or WeChat OpenID already exists');
    }

    // Generate ID
    const id = uuidv4();

    // Insert user into database
    const sql = `
      INSERT INTO users (
        id, phone, avatar, role, is_active, wechat_open_id, wechat_union_id,
        wechat_session_key, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const params = [
      id,
      userData.phone || null,
      userData.avatar || null,
      userData.role || UserRole.USER,
      1, // is_active = true
      userData.wechatOpenId || null,
      userData.wechatUnionId || null,
      userData.wechatSessionKey || null,
    ];

    await db.query(sql, params);

    // Return the created user
    const user = await this.findById(id);
    if (!user) {
      throw new Error('Failed to create user');
    }

    return user;
  }

  /**
   * Check if user already exists
   */
  private static async findExistingUser(userData: CreateUserDto): Promise<User | null> {
    const conditions = [];
    const params = [];

    if (userData.phone) {
      conditions.push('phone = ?');
      params.push(userData.phone);
    }
    if (userData.wechatOpenId) {
      conditions.push('wechat_open_id = ?');
      params.push(userData.wechatOpenId);
    }

    if (conditions.length === 0) {
      return null;
    }

    const sql = `SELECT * FROM users WHERE ${conditions.join(' OR ')} LIMIT 1`;
    const rows = await db.query(sql, params);

    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  /**
   * Find user by ID
   */
  static async findById(id: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE id = ?';
    const rows = await db.query(sql, [id]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  /**
   * Find user by phone
   */
  static async findByPhone(phone: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE phone = ?';
    const rows = await db.query(sql, [phone]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }



  /**
   * Find user by WeChat OpenID
   */
  static async findByWechatOpenId(openId: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE wechat_open_id = ?';
    const rows = await db.query(sql, [openId]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  /**
   * Find user by WeChat UnionID
   */
  static async findByWechatUnionId(unionId: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE wechat_union_id = ?';
    const rows = await db.query(sql, [unionId]);
    
    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  /**
   * Map database row to User object
   */
  private static mapRowToUser(row: any): User {
    return {
      id: row.id,
      phone: row.phone || null,
      avatar: row.avatar || undefined,
      role: row.role as UserRole,
      isActive: Boolean(row.is_active),

      // 微信相关字段
      wechatOpenId: row.wechat_open_id || undefined,
      wechatUnionId: row.wechat_union_id || undefined,
      wechatSessionKey: row.wechat_session_key || undefined,

      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  /**
   * Update user
   */
  static async update(id: string, updateData: UpdateUserDto): Promise<User | null> {
    // Check if user exists
    const existingUser = await this.findById(id);
    if (!existingUser) {
      return null;
    }

    // Check for duplicate phone/wechatOpenId if being updated
    if (updateData.phone || updateData.wechatOpenId) {
      const duplicateUser = await this.findDuplicateUser(id, updateData);
      if (duplicateUser) {
        throw new Error('User with this phone or WeChat OpenID already exists');
      }
    }

    // Build update query
    const updateFields = [];
    const params = [];

    if (updateData.phone !== undefined) {
      updateFields.push('phone = ?');
      params.push(updateData.phone);
    }
    if (updateData.avatar !== undefined) {
      updateFields.push('avatar = ?');
      params.push(updateData.avatar);
    }
    if (updateData.role !== undefined) {
      updateFields.push('role = ?');
      params.push(updateData.role);
    }
    if (updateData.isActive !== undefined) {
      updateFields.push('is_active = ?');
      params.push(updateData.isActive ? 1 : 0);
    }
    if (updateData.wechatOpenId !== undefined) {
      updateFields.push('wechat_open_id = ?');
      params.push(updateData.wechatOpenId);
    }
    if (updateData.wechatUnionId !== undefined) {
      updateFields.push('wechat_union_id = ?');
      params.push(updateData.wechatUnionId);
    }
    if (updateData.wechatSessionKey !== undefined) {
      updateFields.push('wechat_session_key = ?');
      params.push(updateData.wechatSessionKey);
    }

    if (updateFields.length === 0) {
      return existingUser; // No fields to update
    }

    // Add updated_at
    updateFields.push('updated_at = NOW()');
    params.push(id);

    const sql = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
    await db.query(sql, params);

    // Return updated user
    return await this.findById(id);
  }

  /**
   * Find duplicate user for update validation
   */
  private static async findDuplicateUser(excludeId: string, updateData: UpdateUserDto): Promise<User | null> {
    const conditions = [];
    const params = [];

    if (updateData.phone) {
      conditions.push('phone = ?');
      params.push(updateData.phone);
    }
    if (updateData.wechatOpenId) {
      conditions.push('wechat_open_id = ?');
      params.push(updateData.wechatOpenId);
    }

    if (conditions.length === 0) {
      return null;
    }

    const sql = `SELECT * FROM users WHERE (${conditions.join(' OR ')}) AND id != ? LIMIT 1`;
    params.push(excludeId);

    const rows = await db.query(sql, params);

    if (!rows || rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  /**
   * Delete user
   */
  static async delete(id: string): Promise<boolean> {
    const sql = 'DELETE FROM users WHERE id = ?';
    const result = await db.query(sql, [id]);

    return result.affectedRows > 0;
  }

  /**
   * Find users with pagination and filtering
   */
  static async findMany(query: UserQuery): Promise<{ users: User[]; total: number }> {
    // Build WHERE clause
    const conditions = [];
    const params = [];

    if (query.search) {
      const searchTerm = `%${query.search}%`;
      conditions.push('(phone LIKE ? OR wechat_open_id LIKE ?)');
      params.push(searchTerm, searchTerm);
    }

    if (query.role) {
      conditions.push('role = ?');
      params.push(query.role);
    }

    if (query.isActive !== undefined) {
      conditions.push('is_active = ?');
      params.push(query.isActive ? 1 : 0);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Get total count
    const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;

    // Build ORDER BY clause
    let orderClause = '';
    if (query.sortBy) {
      const sortOrder = query.sortOrder === 'desc' ? 'DESC' : 'ASC';
      const sortField = this.mapSortField(query.sortBy);
      orderClause = `ORDER BY ${sortField} ${sortOrder}`;
    } else {
      orderClause = 'ORDER BY created_at DESC';
    }

    // Apply pagination
    const page = Number(query.page) || 1;
    const limit = Number(query.limit) || 10;
    const offset = (page - 1) * limit;

    const sql = `SELECT * FROM users ${whereClause} ${orderClause} LIMIT ? OFFSET ?`;
    const rows = await db.query(sql, [...params, limit, offset]);

    const users = rows.map((row: any) => this.mapRowToUser(row));

    return { users, total };
  }

  /**
   * Map sort field to database column
   */
  private static mapSortField(sortBy: string): string {
    const fieldMap: { [key: string]: string } = {
      isActive: 'is_active',
      wechatOpenId: 'wechat_open_id',
      wechatUnionId: 'wechat_union_id',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    };

    return fieldMap[sortBy] || sortBy;
  }

  /**
   * Get all users (for testing/admin purposes)
   */
  static async findAll(): Promise<User[]> {
    const sql = 'SELECT * FROM users ORDER BY created_at DESC';
    const rows = await db.query(sql);
    return rows.map((row: any) => this.mapRowToUser(row));
  }


}
