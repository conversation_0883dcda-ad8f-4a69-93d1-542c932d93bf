import {
  Contact,
  CreateContactDto,
  UpdateContactDto,
  ContactQuery,
  ContactCategory,
  ContactFrequency,
  ContactType,
  ContactStats
} from '../types';
import { db } from '../config/database';
import logger from '../config/logger';

export class ContactModel {
  /**
   * Create a new contact
   */
  static async create(userId: string, contactData: CreateContactDto): Promise<Contact> {
    // Check if contact already exists for this user
    const existingContact = await this.findByPhoneOrWechat(userId, contactData.phone, contactData.wechatId);
    if (existingContact) {
      throw new Error('Contact with this phone or WeChat ID already exists');
    }

    const sql = `
      INSERT INTO miniprogram_contacts (
        user_id, name, phone, wechat_id, avatar_url, category, tags,
        company, position, email, address, birthday, notes,
        contact_frequency, importance_level, is_favorite,
        is_public, share_phone, share_wechat, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;

    const params = [
      userId,
      contactData.name,
      contactData.phone,
      contactData.wechatId || null,
      contactData.avatarUrl || null,
      contactData.category || ContactCategory.FRIEND,
      contactData.tags ? JSON.stringify(contactData.tags) : null,
      contactData.company || null,
      contactData.position || null,
      contactData.email || null,
      contactData.address || null,
      contactData.birthday || null,
      contactData.notes || null,
      contactData.contactFrequency || ContactFrequency.RARELY,
      contactData.importanceLevel || 3,
      contactData.isFavorite || false,
      contactData.isPublic || false,
      contactData.sharePhone || false,
      contactData.shareWechat || false,
    ];

    try {
      const result = await db.query(sql, params);
      const insertId = (result as any).insertId;

      // Return the created contact
      const contact = await this.findById(insertId, userId);
      if (!contact) {
        throw new Error('Failed to create contact');
      }

      logger.info(`Contact created: ${insertId} for user: ${userId}`);
      return contact;
    } catch (error) {
      logger.error('Error creating contact:', error);
      throw error;
    }
  }

  /**
   * Find contact by ID
   */
  static async findById(id: number, userId: string): Promise<Contact | null> {
    const sql = 'SELECT * FROM miniprogram_contacts WHERE id = ? AND user_id = ? AND status = 1';

    try {
      const rows = await db.query(sql, [id, userId]);
      if (!rows || rows.length === 0) {
        return null;
      }

      return this.mapRowToContact(rows[0]);
    } catch (error) {
      logger.error('Error finding contact by ID:', error);
      throw error;
    }
  }

  /**
   * Find contact by phone or WeChat ID
   */
  static async findByPhoneOrWechat(userId: string, phone: string, wechatId?: string): Promise<Contact | null> {
    let sql = 'SELECT * FROM miniprogram_contacts WHERE user_id = ? AND status = 1 AND (phone = ?';
    const params = [userId, phone];

    if (wechatId) {
      sql += ' OR wechat_id = ?';
      params.push(wechatId);
    }
    sql += ')';

    try {
      const rows = await db.query(sql, params);
      if (!rows || rows.length === 0) {
        return null;
      }

      return this.mapRowToContact(rows[0]);
    } catch (error) {
      logger.error('Error finding contact by phone or WeChat:', error);
      throw error;
    }
  }

  /**
   * Find contacts by user ID with pagination and filtering
   */
  static async findByUserId(userId: string, query: ContactQuery): Promise<{ contacts: Contact[]; total: number }> {
    try {
      // Build WHERE clause
      let whereClause = 'WHERE user_id = ? AND status = 1';
      const params: any[] = [userId];

      // Apply filters
      if (query.search) {
        whereClause += ' AND (name LIKE ? OR phone LIKE ? OR wechat_id LIKE ? OR company LIKE ?)';
        const searchTerm = `%${query.search}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }

      if (query.category) {
        whereClause += ' AND category = ?';
        params.push(query.category);
      }

      if (query.isFavorite !== undefined) {
        whereClause += ' AND is_favorite = ?';
        params.push(query.isFavorite);
      }

      if (query.importanceLevel) {
        whereClause += ' AND importance_level = ?';
        params.push(query.importanceLevel);
      }

      if (query.isPublic !== undefined) {
        whereClause += ' AND is_public = ?';
        params.push(query.isPublic);
      }

      if (query.tags && query.tags.length > 0) {
        const tagConditions = query.tags.map(() => 'JSON_CONTAINS(tags, ?)').join(' OR ');
        whereClause += ` AND (${tagConditions})`;
        query.tags.forEach(tag => params.push(`"${tag}"`));
      }

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM miniprogram_contacts ${whereClause}`;
      const countResult = await db.query(countSql, params);
      const total = countResult[0].total;

      // Apply pagination and sorting
      const page = Number(query.page) || 1;
      const limit = Number(query.limit) || 20;
      const offset = (page - 1) * limit;

      const sortBy = query.sortBy || 'created_at';
      const sortOrder = query.sortOrder || 'desc';

      const dataSql = `
        SELECT * FROM miniprogram_contacts ${whereClause}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
      `;

      const dataParams = [...params, limit, offset];
      const rows = await db.query(dataSql, dataParams);

      const contacts = rows.map((row: any) => this.mapRowToContact(row));

      return { contacts, total };
    } catch (error) {
      logger.error('Error finding contacts by user ID:', error);
      throw error;
    }
  }

  /**
   * Update contact
   */
  static async update(id: number, userId: string, updateData: UpdateContactDto): Promise<Contact | null> {
    try {
      // Check if contact exists
      const existingContact = await this.findById(id, userId);
      if (!existingContact) {
        return null;
      }

      // Check for duplicate phone/wechat if being updated
      if (updateData.phone || updateData.wechatId) {
        const duplicateContact = await this.findByPhoneOrWechat(userId, updateData.phone || '', updateData.wechatId);
        if (duplicateContact && duplicateContact.id !== id) {
          throw new Error('Contact with this phone or WeChat ID already exists');
        }
      }

      // Build update query
      const updateFields: string[] = [];
      const params: any[] = [];

      if (updateData.name !== undefined) {
        updateFields.push('name = ?');
        params.push(updateData.name);
      }
      if (updateData.phone !== undefined) {
        updateFields.push('phone = ?');
        params.push(updateData.phone);
      }
      if (updateData.wechatId !== undefined) {
        updateFields.push('wechat_id = ?');
        params.push(updateData.wechatId);
      }
      if (updateData.avatarUrl !== undefined) {
        updateFields.push('avatar_url = ?');
        params.push(updateData.avatarUrl);
      }
      if (updateData.category !== undefined) {
        updateFields.push('category = ?');
        params.push(updateData.category);
      }
      if (updateData.tags !== undefined) {
        updateFields.push('tags = ?');
        params.push(updateData.tags ? JSON.stringify(updateData.tags) : null);
      }
      if (updateData.company !== undefined) {
        updateFields.push('company = ?');
        params.push(updateData.company);
      }
      if (updateData.position !== undefined) {
        updateFields.push('position = ?');
        params.push(updateData.position);
      }
      if (updateData.email !== undefined) {
        updateFields.push('email = ?');
        params.push(updateData.email);
      }
      if (updateData.address !== undefined) {
        updateFields.push('address = ?');
        params.push(updateData.address);
      }
      if (updateData.birthday !== undefined) {
        updateFields.push('birthday = ?');
        params.push(updateData.birthday);
      }
      if (updateData.notes !== undefined) {
        updateFields.push('notes = ?');
        params.push(updateData.notes);
      }
      if (updateData.contactFrequency !== undefined) {
        updateFields.push('contact_frequency = ?');
        params.push(updateData.contactFrequency);
      }
      if (updateData.importanceLevel !== undefined) {
        updateFields.push('importance_level = ?');
        params.push(updateData.importanceLevel);
      }
      if (updateData.isFavorite !== undefined) {
        updateFields.push('is_favorite = ?');
        params.push(updateData.isFavorite);
      }
      if (updateData.lastContactAt !== undefined) {
        updateFields.push('last_contact_at = ?');
        params.push(updateData.lastContactAt);
      }
      if (updateData.lastContactType !== undefined) {
        updateFields.push('last_contact_type = ?');
        params.push(updateData.lastContactType);
      }
      if (updateData.isPublic !== undefined) {
        updateFields.push('is_public = ?');
        params.push(updateData.isPublic);
      }
      if (updateData.sharePhone !== undefined) {
        updateFields.push('share_phone = ?');
        params.push(updateData.sharePhone);
      }
      if (updateData.shareWechat !== undefined) {
        updateFields.push('share_wechat = ?');
        params.push(updateData.shareWechat);
      }

      if (updateFields.length === 0) {
        return existingContact; // No changes
      }

      updateFields.push('updated_at = NOW()');
      params.push(id, userId);

      const sql = `UPDATE miniprogram_contacts SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ? AND status = 1`;
      await db.query(sql, params);

      // Return updated contact
      return await this.findById(id, userId);
    } catch (error) {
      logger.error('Error updating contact:', error);
      throw error;
    }
  }

  /**
   * Delete contact (soft delete)
   */
  static async delete(id: number, userId: string): Promise<boolean> {
    try {
      const sql = 'UPDATE miniprogram_contacts SET status = 0, updated_at = NOW() WHERE id = ? AND user_id = ? AND status = 1';
      const result = await db.query(sql, [id, userId]);

      return (result as any).affectedRows > 0;
    } catch (error) {
      logger.error('Error deleting contact:', error);
      throw error;
    }
  }

  /**
   * Update contact interaction (when user contacts someone)
   */
  static async updateContactInteraction(
    id: number,
    userId: string,
    contactType: ContactType
  ): Promise<Contact | null> {
    try {
      const sql = `
        UPDATE miniprogram_contacts
        SET last_contact_at = NOW(), last_contact_type = ?, contact_count = contact_count + 1, updated_at = NOW()
        WHERE id = ? AND user_id = ? AND status = 1
      `;

      const result = await db.query(sql, [contactType, id, userId]);

      if ((result as any).affectedRows === 0) {
        return null;
      }

      return await this.findById(id, userId);
    } catch (error) {
      logger.error('Error updating contact interaction:', error);
      throw error;
    }
  }

  /**
   * Find public contacts (for search across users)
   */
  static async findPublicContacts(query: ContactQuery): Promise<{ contacts: Contact[]; total: number }> {
    try {
      logger.info('Finding public contacts with query:', query);
      // Build WHERE clause for public contacts
      let whereClause = 'WHERE is_public = 1 AND status = 1';
      const params: any[] = [];

      // Apply search filter
      if (query.search) {
        whereClause += ' AND (name LIKE ? OR (share_phone = 1 AND phone LIKE ?) OR (share_wechat = 1 AND wechat_id LIKE ?))';
        const searchTerm = `%${query.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // Apply other filters
      if (query.category) {
        whereClause += ' AND category = ?';
        params.push(query.category);
      }

      if (query.tags && query.tags.length > 0) {
        const tagConditions = query.tags.map(() => 'JSON_CONTAINS(tags, ?)').join(' OR ');
        whereClause += ` AND (${tagConditions})`;
        query.tags.forEach(tag => params.push(`"${tag}"`));
      }

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM miniprogram_contacts ${whereClause}`;
      const countResult = await db.query(countSql, params);
      const total = countResult[0].total;

      // Apply pagination
      const page = Number(query.page) || 1;
      const limit = Number(query.limit) || 20;
      const offset = (page - 1) * limit;

      const dataSql = `
        SELECT * FROM miniprogram_contacts ${whereClause}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;

      const dataParams = [...params, limit, offset];
      const rows = await db.query(dataSql, dataParams);

      const contacts = rows.map((row: any) => this.mapRowToContact(row));

      return { contacts, total };
    } catch (error) {
      logger.error('Error finding public contacts:', error);
      throw error;
    }
  }

  /**
   * Get contact statistics for a user
   */
  static async getContactStats(userId: string): Promise<ContactStats> {
    try {
      const sql = `
        SELECT
          COUNT(*) as totalContacts,
          SUM(CASE WHEN is_favorite = 1 THEN 1 ELSE 0 END) as favoriteContacts,
          SUM(CASE WHEN is_public = 1 THEN 1 ELSE 0 END) as publicContacts,
          SUM(CASE WHEN last_contact_at > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recentContacts,
          category,
          importance_level,
          tags
        FROM miniprogram_contacts
        WHERE user_id = ? AND status = 1
        GROUP BY category, importance_level
      `;

      const rows = await db.query(sql, [userId]);

      const stats: ContactStats = {
        totalContacts: 0,
        favoriteContacts: 0,
        publicContacts: 0,
        categoryCounts: {} as Record<ContactCategory, number>,
        importanceLevelCounts: {} as Record<number, number>,
        recentContacts: 0,
        topTags: []
      };

      // Initialize category counts
      Object.values(ContactCategory).forEach(category => {
        stats.categoryCounts[category] = 0;
      });

      // Initialize importance level counts
      for (let i = 1; i <= 5; i++) {
        stats.importanceLevelCounts[i] = 0;
      }

      if (rows && rows.length > 0) {
        // Get basic stats from first row
        stats.totalContacts = rows[0].totalContacts || 0;
        stats.favoriteContacts = rows[0].favoriteContacts || 0;
        stats.publicContacts = rows[0].publicContacts || 0;
        stats.recentContacts = rows[0].recentContacts || 0;

        // Process category and importance level counts
        rows.forEach((row: any) => {
          if (row.category) {
            stats.categoryCounts[row.category as ContactCategory] = (stats.categoryCounts[row.category as ContactCategory] || 0) + 1;
          }
          if (row.importance_level) {
            stats.importanceLevelCounts[row.importance_level] = (stats.importanceLevelCounts[row.importance_level] || 0) + 1;
          }
        });
      }

      // Get top tags separately
      const tagSql = `
        SELECT tags FROM miniprogram_contacts
        WHERE user_id = ? AND status = 1 AND tags IS NOT NULL
      `;
      const tagRows = await db.query(tagSql, [userId]);

      const tagCounts: Record<string, number> = {};
      tagRows.forEach((row: any) => {
        if (row.tags) {
          const tags = typeof row.tags === 'string' ? JSON.parse(row.tags) : row.tags;
          if (Array.isArray(tags)) {
            tags.forEach((tag: string) => {
              tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            });
          }
        }
      });

      stats.topTags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return stats;
    } catch (error) {
      logger.error('Error getting contact stats:', error);
      throw error;
    }
  }

  /**
   * Map database row to Contact object
   */
  private static mapRowToContact(row: any): Contact {
    return {
      id: row.id,
      userId: row.user_id,
      name: row.name,
      phone: row.phone,
      wechatId: row.wechat_id || undefined,
      avatarUrl: row.avatar_url || undefined,
      category: row.category as ContactCategory,
      tags: row.tags ? (typeof row.tags === 'string' ? JSON.parse(row.tags) : row.tags) : [],
      company: row.company || undefined,
      position: row.position || undefined,
      email: row.email || undefined,
      address: row.address || undefined,
      birthday: row.birthday ? new Date(row.birthday) : undefined,
      notes: row.notes || undefined,
      contactFrequency: row.contact_frequency as ContactFrequency,
      importanceLevel: row.importance_level,
      isFavorite: Boolean(row.is_favorite),
      lastContactAt: row.last_contact_at ? new Date(row.last_contact_at) : undefined,
      lastContactType: row.last_contact_type as ContactType || undefined,
      contactCount: row.contact_count || 0,
      isPublic: Boolean(row.is_public),
      sharePhone: Boolean(row.share_phone),
      shareWechat: Boolean(row.share_wechat),
      status: row.status,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}
