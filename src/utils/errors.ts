/**
 * 错误处理工具类
 * 提供统一的错误定义和处理方法
 */

// 重新导出AppError类型，保持向后兼容
export { AppError } from '../types';

/**
 * 业务错误代码枚举
 */
export enum ErrorCode {
  // 通用错误 1000-1999
  VALIDATION_ERROR = 1001,
  UNAUTHORIZED = 1002,
  FORBIDDEN = 1003,
  NOT_FOUND = 1004,
  CONFLICT = 1005,
  INTERNAL_ERROR = 1006,

  // 用户相关错误 2000-2999
  USER_NOT_FOUND = 2001,
  USER_ALREADY_EXISTS = 2002,
  INVALID_CREDENTIALS = 2003,
  USER_INACTIVE = 2004,

  // 配置相关错误 3000-3999
  CONFIG_NOT_FOUND = 3001,
  CONFIG_ALREADY_EXISTS = 3002,
  CONFIG_VALIDATION_ERROR = 3003,
  CONFIG_TYPE_INVALID = 3004,
  CONFIG_KEY_DUPLICATE = 3005,

  // 微信相关错误 4000-4999
  WECHAT_API_ERROR = 4001,
  WECHAT_CODE_INVALID = 4002,
  WECHAT_SESSION_EXPIRED = 4003,

  // 数据库相关错误 5000-5999
  DATABASE_CONNECTION_ERROR = 5001,
  DATABASE_QUERY_ERROR = 5002,
  DATABASE_CONSTRAINT_ERROR = 5003,
}

/**
 * 错误消息映射
 */
export const ErrorMessages: Record<ErrorCode, string> = {
  [ErrorCode.VALIDATION_ERROR]: '请求参数验证失败',
  [ErrorCode.UNAUTHORIZED]: '未授权访问',
  [ErrorCode.FORBIDDEN]: '权限不足',
  [ErrorCode.NOT_FOUND]: '资源不存在',
  [ErrorCode.CONFLICT]: '资源冲突',
  [ErrorCode.INTERNAL_ERROR]: '服务器内部错误',

  [ErrorCode.USER_NOT_FOUND]: '用户不存在',
  [ErrorCode.USER_ALREADY_EXISTS]: '用户已存在',
  [ErrorCode.INVALID_CREDENTIALS]: '用户名或密码错误',
  [ErrorCode.USER_INACTIVE]: '用户账户已被禁用',

  [ErrorCode.CONFIG_NOT_FOUND]: '配置不存在',
  [ErrorCode.CONFIG_ALREADY_EXISTS]: '配置已存在',
  [ErrorCode.CONFIG_VALIDATION_ERROR]: '配置数据验证失败',
  [ErrorCode.CONFIG_TYPE_INVALID]: '无效的配置类型',
  [ErrorCode.CONFIG_KEY_DUPLICATE]: '配置键名重复',

  [ErrorCode.WECHAT_API_ERROR]: '微信API调用失败',
  [ErrorCode.WECHAT_CODE_INVALID]: '微信授权码无效',
  [ErrorCode.WECHAT_SESSION_EXPIRED]: '微信会话已过期',

  [ErrorCode.DATABASE_CONNECTION_ERROR]: '数据库连接失败',
  [ErrorCode.DATABASE_QUERY_ERROR]: '数据库查询失败',
  [ErrorCode.DATABASE_CONSTRAINT_ERROR]: '数据库约束错误',
};

/**
 * 业务错误类
 * 扩展了基础AppError，添加了错误代码支持
 */
export class BusinessError extends Error {
  public statusCode: number;
  public errorCode: ErrorCode;
  public isOperational: boolean;
  public details?: any;

  constructor(
    errorCode: ErrorCode,
    message?: string,
    statusCode: number = 500,
    details?: any,
    isOperational: boolean = true
  ) {
    super(message || ErrorMessages[errorCode]);
    this.name = 'BusinessError';
    this.errorCode = errorCode;
    this.statusCode = statusCode;
    this.details = details;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 配置相关错误类
 */
export class ConfigError extends BusinessError {
  constructor(errorCode: ErrorCode, message?: string, details?: any) {
    const statusCode = getStatusCodeByErrorCode(errorCode);
    super(errorCode, message, statusCode, details);
    this.name = 'ConfigError';
  }
}

/**
 * 用户相关错误类
 */
export class UserError extends BusinessError {
  constructor(errorCode: ErrorCode, message?: string, details?: any) {
    const statusCode = getStatusCodeByErrorCode(errorCode);
    super(errorCode, message, statusCode, details);
    this.name = 'UserError';
  }
}

/**
 * 微信相关错误类
 */
export class WechatError extends BusinessError {
  constructor(errorCode: ErrorCode, message?: string, details?: any) {
    const statusCode = getStatusCodeByErrorCode(errorCode);
    super(errorCode, message, statusCode, details);
    this.name = 'WechatError';
  }
}

/**
 * 根据错误代码获取HTTP状态码
 */
function getStatusCodeByErrorCode(errorCode: ErrorCode): number {
  switch (errorCode) {
    case ErrorCode.VALIDATION_ERROR:
    case ErrorCode.CONFIG_VALIDATION_ERROR:
    case ErrorCode.CONFIG_TYPE_INVALID:
    case ErrorCode.WECHAT_CODE_INVALID:
      return 400;

    case ErrorCode.UNAUTHORIZED:
    case ErrorCode.INVALID_CREDENTIALS:
    case ErrorCode.WECHAT_SESSION_EXPIRED:
      return 401;

    case ErrorCode.FORBIDDEN:
    case ErrorCode.USER_INACTIVE:
      return 403;

    case ErrorCode.NOT_FOUND:
    case ErrorCode.USER_NOT_FOUND:
    case ErrorCode.CONFIG_NOT_FOUND:
      return 404;

    case ErrorCode.CONFLICT:
    case ErrorCode.USER_ALREADY_EXISTS:
    case ErrorCode.CONFIG_ALREADY_EXISTS:
    case ErrorCode.CONFIG_KEY_DUPLICATE:
    case ErrorCode.DATABASE_CONSTRAINT_ERROR:
      return 409;

    default:
      return 500;
  }
}

/**
 * 错误工厂函数
 */
export const createError = {
  validation: (message?: string, details?: any) =>
    new BusinessError(ErrorCode.VALIDATION_ERROR, message, 400, details),

  unauthorized: (message?: string, details?: any) =>
    new BusinessError(ErrorCode.UNAUTHORIZED, message, 401, details),

  forbidden: (message?: string, details?: any) =>
    new BusinessError(ErrorCode.FORBIDDEN, message, 403, details),

  notFound: (message?: string, details?: any) =>
    new BusinessError(ErrorCode.NOT_FOUND, message, 404, details),

  conflict: (message?: string, details?: any) =>
    new BusinessError(ErrorCode.CONFLICT, message, 409, details),

  internal: (message?: string, details?: any) =>
    new BusinessError(ErrorCode.INTERNAL_ERROR, message, 500, details),

  // 配置相关错误
  configNotFound: (message?: string, details?: any) =>
    new ConfigError(ErrorCode.CONFIG_NOT_FOUND, message, details),

  configExists: (message?: string, details?: any) =>
    new ConfigError(ErrorCode.CONFIG_ALREADY_EXISTS, message, details),

  configValidation: (message?: string, details?: any) =>
    new ConfigError(ErrorCode.CONFIG_VALIDATION_ERROR, message, details),

  configTypeInvalid: (message?: string, details?: any) =>
    new ConfigError(ErrorCode.CONFIG_TYPE_INVALID, message, details),

  configKeyDuplicate: (message?: string, details?: any) =>
    new ConfigError(ErrorCode.CONFIG_KEY_DUPLICATE, message, details),

  // 用户相关错误
  userNotFound: (message?: string, details?: any) =>
    new UserError(ErrorCode.USER_NOT_FOUND, message, details),

  userExists: (message?: string, details?: any) =>
    new UserError(ErrorCode.USER_ALREADY_EXISTS, message, details),

  invalidCredentials: (message?: string, details?: any) =>
    new UserError(ErrorCode.INVALID_CREDENTIALS, message, details),

  userInactive: (message?: string, details?: any) =>
    new UserError(ErrorCode.USER_INACTIVE, message, details),

  // 微信相关错误
  wechatApiError: (message?: string, details?: any) =>
    new WechatError(ErrorCode.WECHAT_API_ERROR, message, details),

  wechatCodeInvalid: (message?: string, details?: any) =>
    new WechatError(ErrorCode.WECHAT_CODE_INVALID, message, details),

  wechatSessionExpired: (message?: string, details?: any) =>
    new WechatError(ErrorCode.WECHAT_SESSION_EXPIRED, message, details),
};

/**
 * 判断是否为操作性错误（可预期的业务错误）
 */
export function isOperationalError(error: Error): boolean {
  if (error instanceof BusinessError) {
    return error.isOperational;
  }
  return false;
}

/**
 * 格式化错误信息用于日志记录
 */
export function formatErrorForLogging(error: Error, context?: any): object {
  const errorInfo: any = {
    name: error.name,
    message: error.message,
    stack: error.stack,
  };

  if (error instanceof BusinessError) {
    errorInfo.errorCode = error.errorCode;
    errorInfo.statusCode = error.statusCode;
    errorInfo.details = error.details;
    errorInfo.isOperational = error.isOperational;
  }

  if (context) {
    errorInfo.context = context;
  }

  return errorInfo;
}

/**
 * 格式化错误信息用于API响应
 */
export function formatErrorForResponse(error: Error): {
  success: false;
  message: string;
  errorCode?: ErrorCode;
  details?: any;
  timestamp: string;
} {
  const response = {
    success: false as const,
    message: error.message,
    timestamp: new Date().toISOString(),
  } as any;

  if (error instanceof BusinessError) {
    response.errorCode = error.errorCode;
    if (error.details) {
      response.details = error.details;
    }
  }

  return response;
}
