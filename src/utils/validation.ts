import { z } from 'zod';
import { UserRole } from '../types';

// Common validation schemas
export const emailSchema = z.string().email('Invalid email format');
export const passwordSchema = z.string().min(8, 'Password must be at least 8 characters');
export const usernameSchema = z.string().min(3, 'Username must be at least 3 characters').max(30, 'Username must be less than 30 characters');
export const phoneSchema = z.string().regex(/^1[3-9]\d{9}$/, 'Invalid Chinese phone number format');

// User validation schemas
export const createUserSchema = z.object({
  phone: phoneSchema,
  email: emailSchema.optional(),
  username: usernameSchema.optional(),
  password: passwordSchema.optional(),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  nickname: z.string().optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  role: z.nativeEnum(UserRole).optional(),

  // 微信相关字段
  wechatOpenId: z.string().optional(),
  wechatUnionId: z.string().optional(),
  wechatSessionKey: z.string().optional(),

  // 用户信息
  gender: z.enum(['male', 'female', 'unknown']).optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  country: z.string().optional(),
  language: z.string().optional(),
});

export const updateUserSchema = z.object({
  phone: phoneSchema.optional(),
  email: emailSchema.optional(),
  username: usernameSchema.optional(),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  nickname: z.string().optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  role: z.nativeEnum(UserRole).optional(),
  isActive: z.boolean().optional(),

  // 微信相关字段
  wechatOpenId: z.string().optional(),
  wechatUnionId: z.string().optional(),
  wechatSessionKey: z.string().optional(),

  // 用户信息
  gender: z.enum(['male', 'female', 'unknown']).optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  country: z.string().optional(),
  language: z.string().optional(),
});

export const loginSchema = z.object({
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  password: z.string().min(1, 'Password is required'),
}).refine(data => data.email || data.phone, {
  message: 'Either email or phone is required',
});

// 微信登录验证 schema
export const wechatLoginSchema = z.object({
  code: z.string().min(1, 'WeChat code is required'),
  encryptedData: z.string().optional(),
  iv: z.string().optional(),
  signature: z.string().optional(),
  rawData: z.string().optional(),
});

export const wechatPhoneSchema = z.object({
  code: z.string().min(1, 'WeChat phone authorization code is required'),
});

export const wechatPhoneDecryptSchema = z.object({
  encryptedData: z.string().min(1, 'Encrypted data is required'),
  iv: z.string().min(1, 'IV is required'),
});

// Contact validation schemas
export const createContactSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  phone: z.string().min(1, 'Phone is required').max(20, 'Phone too long'),
  wechatId: z.string().max(100, 'WeChat ID too long').optional(),
  avatarUrl: z.string().url('Invalid avatar URL').optional(),
  category: z.enum(['FRIEND', 'FAMILY', 'COLLEAGUE', 'BUSINESS', 'OTHER']).optional(),
  tags: z.array(z.string().max(50, 'Tag too long')).max(10, 'Too many tags').optional(),
  company: z.string().max(200, 'Company name too long').optional(),
  position: z.string().max(100, 'Position too long').optional(),
  email: z.string().email('Invalid email').optional(),
  address: z.string().max(500, 'Address too long').optional(),
  birthday: z.string().datetime().optional(),
  notes: z.string().max(1000, 'Notes too long').optional(),
  contactFrequency: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'RARELY', 'NEVER']).optional(),
  importanceLevel: z.number().int().min(1).max(5).optional(),
  isFavorite: z.boolean().optional(),
  isPublic: z.boolean().optional(),
  sharePhone: z.boolean().optional(),
  shareWechat: z.boolean().optional(),
});

export const updateContactSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  phone: z.string().min(1, 'Phone is required').max(20, 'Phone too long').optional(),
  wechatId: z.string().max(100, 'WeChat ID too long').optional(),
  avatarUrl: z.string().url('Invalid avatar URL').optional(),
  category: z.enum(['FRIEND', 'FAMILY', 'COLLEAGUE', 'BUSINESS', 'OTHER']).optional(),
  tags: z.array(z.string().max(50, 'Tag too long')).max(10, 'Too many tags').optional(),
  company: z.string().max(200, 'Company name too long').optional(),
  position: z.string().max(100, 'Position too long').optional(),
  email: z.string().email('Invalid email').optional(),
  address: z.string().max(500, 'Address too long').optional(),
  birthday: z.string().datetime().optional(),
  notes: z.string().max(1000, 'Notes too long').optional(),
  contactFrequency: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'RARELY', 'NEVER']).optional(),
  importanceLevel: z.number().int().min(1).max(5).optional(),
  isFavorite: z.boolean().optional(),
  lastContactAt: z.string().datetime().optional(),
  lastContactType: z.enum(['CALL', 'MESSAGE', 'WECHAT', 'EMAIL', 'MEETING', 'OTHER']).optional(),
  isPublic: z.boolean().optional(),
  sharePhone: z.boolean().optional(),
  shareWechat: z.boolean().optional(),
});

export const contactQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  limit: z.string().transform(Number).optional(),
  search: z.string().max(100, 'Search term too long').optional(),
  category: z.enum(['FRIEND', 'FAMILY', 'COLLEAGUE', 'BUSINESS', 'OTHER']).optional(),
  isFavorite: z.string().transform(val => val === 'true').optional(),
  importanceLevel: z.string().transform(Number).optional(),
  isPublic: z.string().transform(val => val === 'true').optional(),
  tags: z.string().transform(val => val.split(',')).optional(),
  sortBy: z.enum(['name', 'createdAt', 'lastContactAt', 'importanceLevel', 'contactCount']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export const contactInteractionSchema = z.object({
  contactType: z.enum(['CALL', 'MESSAGE', 'WECHAT', 'EMAIL', 'MEETING', 'OTHER']),
});

// Share config validation schemas
export const createShareConfigSchema = z.object({
  contactId: z.number().int().positive('Invalid contact ID'),
  shareType: z.enum(['QR_CODE', 'LINK', 'CARD', 'MINI_PROGRAM']),
  shareTitle: z.string().max(200, 'Title too long').optional(),
  shareDescription: z.string().max(1000, 'Description too long').optional(),
  shareImageUrl: z.string().url('Invalid image URL').optional(),
  shareScope: z.enum(['PUBLIC', 'FRIENDS', 'PRIVATE']).optional(),
  allowedUsers: z.array(z.string()).max(100, 'Too many allowed users').optional(),
  includePhone: z.boolean().optional(),
  includeWechat: z.boolean().optional(),
  includeEmail: z.boolean().optional(),
  includeCompany: z.boolean().optional(),
  includeAddress: z.boolean().optional(),
  includeNotes: z.boolean().optional(),
  expiresAt: z.string().datetime().optional(),
});

export const updateShareConfigSchema = z.object({
  shareTitle: z.string().max(200, 'Title too long').optional(),
  shareDescription: z.string().max(1000, 'Description too long').optional(),
  shareImageUrl: z.string().url('Invalid image URL').optional(),
  shareScope: z.enum(['PUBLIC', 'FRIENDS', 'PRIVATE']).optional(),
  allowedUsers: z.array(z.string()).max(100, 'Too many allowed users').optional(),
  includePhone: z.boolean().optional(),
  includeWechat: z.boolean().optional(),
  includeEmail: z.boolean().optional(),
  includeCompany: z.boolean().optional(),
  includeAddress: z.boolean().optional(),
  includeNotes: z.boolean().optional(),
  expiresAt: z.string().datetime().optional(),
  isActive: z.boolean().optional(),
});

// 管理员登录验证 schema
export const adminLoginSchema = z.object({
  email: emailSchema.optional(),
  username: usernameSchema.optional(),
  password: z.string().min(1, 'Password is required'),
}).refine(data => data.email || data.username, {
  message: 'Either email or username is required',
});

// 管理员创建验证 schema
export const adminCreateSchema = z.object({
  email: emailSchema,
  username: usernameSchema,
  password: passwordSchema.optional(),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  role: z.enum(['admin', 'moderator', 'super_admin']),
  phone: phoneSchema.optional(),
});

// 修改密码验证 schema
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
});

// 管理员状态切换验证 schema
export const toggleStatusSchema = z.object({
  isActive: z.boolean(),
});

// Query validation schemas
export const paginationSchema = z.object({
  page: z.string().transform(Number).refine(val => val > 0, 'Page must be greater than 0').optional(),
  limit: z.string().transform(Number).refine(val => val > 0 && val <= 100, 'Limit must be between 1 and 100').optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export const userQuerySchema = paginationSchema.extend({
  search: z.string().optional(),
  role: z.nativeEnum(UserRole).optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
});

// ID validation
export const idSchema = z.object({
  id: z.string().uuid('Invalid ID format'),
});

// Category validation schemas
export const createCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(200, 'Name must be less than 200 characters'),
  parentId: z.number().int().positive('Parent ID must be a positive integer').optional().nullable(),
  sortIndex: z.number().int().min(0, 'Sort index must be non-negative').optional(),
});

export const updateCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(200, 'Name must be less than 200 characters').optional(),
  parentId: z.number().int().positive('Parent ID must be a positive integer').optional().nullable(),
  sortIndex: z.number().int().min(0, 'Sort index must be non-negative').optional(),
});

export const updateSortIndexesSchema = z.object({
  updates: z.array(
    z.object({
      id: z.number().int().positive('ID must be a positive integer'),
      sortIndex: z.number().int().min(0, 'Sort index must be non-negative'),
    })
  ).min(1, 'At least one update is required'),
});

export const categoryQuerySchema = paginationSchema.extend({
  search: z.string().optional(),
  parentId: z.string().transform(Number).refine(val => val > 0, 'Parent ID must be greater than 0').optional(),
  level: z.string().transform(Number).refine(val => val >= 1 && val <= 3, 'Level must be between 1 and 3').optional(),
});

// Generic validation helper
export const validateSchema = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  return schema.parse(data);
};

// Async validation helper
export const validateSchemaAsync = async <T>(schema: z.ZodSchema<T>, data: unknown): Promise<T> => {
  return await schema.parseAsync(data);
};
