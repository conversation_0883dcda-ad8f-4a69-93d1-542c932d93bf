import axios, { AxiosResponse } from 'axios';
import crypto from 'crypto';
import { 
  wechatConfig, 
  WechatError, 
  WechatLoginResponse, 
  WechatPhoneResponse, 
  WechatAccessTokenResponse 
} from '../config/wechat';
import logger from '../config/logger';

/**
 * 微信API工具类
 */
export class WechatUtils {
  private static accessToken: string | null = null;
  private static accessTokenExpireTime: number = 0;

  /**
   * 获取微信小程序Access Token
   */
  static async getAccessToken(): Promise<string> {
    // 检查是否有有效的access_token
    if (this.accessToken && Date.now() < this.accessTokenExpireTime) {
      return this.accessToken;
    }

    try {
      const response: AxiosResponse<WechatAccessTokenResponse> = await axios.get(
        wechatConfig.accessTokenUrl,
        {
          params: {
            grant_type: 'client_credential',
            appid: wechatConfig.appId,
            secret: wechatConfig.appSecret,
          },
          timeout: wechatConfig.timeout,
        }
      );

      const data = response.data;

      if (data.errcode && data.errcode !== 0) {
        throw new WechatError(data.errcode, data.errmsg || 'Unknown error');
      }

      if (!data.access_token) {
        throw new WechatError(-1, 'Failed to get access token');
      }

      // 缓存access_token，提前5分钟过期
      this.accessToken = data.access_token;
      this.accessTokenExpireTime = Date.now() + (data.expires_in! - 300) * 1000;

      logger.info('WeChat access token obtained successfully');
      return this.accessToken;
    } catch (error) {
      logger.error('Failed to get WeChat access token:', error);
      if (error instanceof WechatError) {
        throw error;
      }
      throw new WechatError(-1, 'Network error while getting access token');
    }
  }

  /**
   * 微信小程序登录
   * @param code 微信登录凭证
   */
  static async login(code: string): Promise<WechatLoginResponse> {
    try {
      const response: AxiosResponse<WechatLoginResponse> = await axios.get(
        wechatConfig.loginUrl,
        {
          params: {
            appid: wechatConfig.appId,
            secret: wechatConfig.appSecret,
            js_code: code,
            grant_type: 'authorization_code',
          },
          timeout: wechatConfig.timeout,
        }
      );

      const data = response.data;

      if (data.errcode && data.errcode !== 0) {
        throw new WechatError(data.errcode, data.errmsg || 'Login failed');
      }

      if (!data.openid) {
        throw new WechatError(-1, 'Invalid login response');
      }

      logger.info(`WeChat login successful for openid: ${data.openid}`);
      return data;
    } catch (error) {
      logger.error('WeChat login failed:', error);
      if (error instanceof WechatError) {
        throw error;
      }
      throw new WechatError(-1, 'Network error during login');
    }
  }

  /**
   * 获取微信小程序用户手机号
   * @param code 手机号授权凭证
   */
  static async getPhoneNumber(code: string): Promise<WechatPhoneResponse> {
    try {
      const accessToken = await this.getAccessToken();
      
      const response: AxiosResponse<WechatPhoneResponse> = await axios.post(
        `${wechatConfig.phoneUrl}?access_token=${accessToken}`,
        {
          code: code,
        },
        {
          timeout: wechatConfig.timeout,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = response.data;

      if (data.errcode && data.errcode !== 0) {
        throw new WechatError(data.errcode, data.errmsg || 'Failed to get phone number');
      }

      if (!data.phone_info?.phoneNumber) {
        throw new WechatError(-1, 'Invalid phone number response');
      }

      logger.info('WeChat phone number obtained successfully');
      return data;
    } catch (error) {
      logger.error('Failed to get WeChat phone number:', error);
      if (error instanceof WechatError) {
        throw error;
      }
      throw new WechatError(-1, 'Network error while getting phone number');
    }
  }

  /**
   * 解密微信小程序数据
   * @param encryptedData 加密数据
   * @param iv 初始向量
   * @param sessionKey 会话密钥
   */
  static decryptData(encryptedData: string, iv: string, sessionKey: string): any {
    try {
      const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
      const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');

      const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
      decipher.setAutoPadding(true);

      let decrypted = decipher.update(encryptedDataBuffer, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      const decryptedData = JSON.parse(decrypted);

      // 验证水印
      if (decryptedData.watermark.appid !== wechatConfig.appId) {
        throw new Error('Invalid watermark appid');
      }

      return decryptedData;
    } catch (error) {
      logger.error('Failed to decrypt WeChat data:', error);
      throw new WechatError(-1, 'Data decryption failed');
    }
  }

  /**
   * 验证微信数据签名
   * @param rawData 原始数据
   * @param sessionKey 会话密钥
   * @param signature 数据签名
   */
  static verifySignature(rawData: string, sessionKey: string, signature: string): boolean {
    try {
      const hash = crypto.createHash('sha1');
      hash.update(rawData + sessionKey);
      const calculatedSignature = hash.digest('hex');
      
      return calculatedSignature === signature;
    } catch (error) {
      logger.error('Failed to verify WeChat signature:', error);
      return false;
    }
  }

  /**
   * 获取微信错误信息
   * @param errcode 错误码
   */
  static getErrorMessage(errcode: number): string {
    return wechatConfig.errorCodes[errcode.toString()] || '未知错误';
  }
}
