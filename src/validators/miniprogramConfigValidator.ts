import Joi from 'joi';
import { ConfigType } from '../types/miniprogramConfig';

// 配置类型枚举验证
const configTypeSchema = Joi.string().valid(...Object.values(ConfigType));

// 轮播图配置值验证
const carouselConfigValueSchema = Joi.object({
  image_url: Joi.string().uri().required(),
  title: Joi.string().max(200).required(),
  subtitle: Joi.string().max(200).optional(),
  link_type: Joi.string().valid('page', 'url', 'none').default('none'),
  link_value: Joi.string().max(500).optional(),
  background_color: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).optional()
});

// 公告配置值验证
const announcementConfigValueSchema = Joi.object({
  title: Joi.string().max(200).required(),
  content: Joi.string().max(1000).required(),
  type: Joi.string().valid('info', 'warning', 'success', 'error').default('info'),
  show_icon: Joi.boolean().default(true),
  closable: Joi.boolean().default(true),
  auto_close_time: Joi.number().integer().min(1000).max(30000).optional(),
  highlight: Joi.boolean().default(false)
});

// 电话联系配置值验证
const contactPhoneConfigValueSchema = Joi.object({
  phone_number: Joi.string().pattern(/^[\d\-\+\(\)\s]+$/).max(20).required(),
  display_name: Joi.string().max(100).required(),
  service_time: Joi.string().max(100).optional(),
  description: Joi.string().max(200).optional(),
  show_confirm: Joi.boolean().default(true),
  confirm_text: Joi.string().max(200).optional()
});

// 微信复制配置值验证
const wechatCopyConfigValueSchema = Joi.object({
  wechat_id: Joi.string().max(100).required(),
  display_name: Joi.string().max(100).required(),
  qr_code_url: Joi.string().uri().optional(),
  description: Joi.string().max(200).optional(),
  copy_success_text: Joi.string().max(200).optional(),
  show_qr_code: Joi.boolean().default(false)
});

// 分享配置值验证
const shareConfigValueSchema = Joi.object({
  title: Joi.string().max(200).required(),
  description: Joi.string().max(500).required(),
  image_url: Joi.string().uri().optional(),
  share_path: Joi.string().max(200).optional(),
  enable_timeline: Joi.boolean().default(true),
  enable_session: Joi.boolean().default(true),
  enable_qq: Joi.boolean().default(false)
});

// 系统配置值验证（较为宽松）
const systemConfigValueSchema = Joi.object().pattern(
  Joi.string(),
  Joi.alternatives().try(
    Joi.string(),
    Joi.number(),
    Joi.boolean(),
    Joi.object(),
    Joi.array()
  )
);

// 根据配置类型动态验证配置值
const configValueSchema = Joi.when('config_type', {
  switch: [
    { is: ConfigType.CAROUSEL, then: carouselConfigValueSchema },
    { is: ConfigType.ANNOUNCEMENT, then: announcementConfigValueSchema },
    { is: ConfigType.CONTACT_PHONE, then: contactPhoneConfigValueSchema },
    { is: ConfigType.WECHAT_COPY, then: wechatCopyConfigValueSchema },
    { is: ConfigType.SHARE_CONFIG, then: shareConfigValueSchema },
    { is: ConfigType.SYSTEM_CONFIG, then: systemConfigValueSchema }
  ],
  otherwise: Joi.object()
});

// 创建配置验证
export const createConfigSchema = Joi.object({
  config_type: configTypeSchema.required(),
  config_key: Joi.string().max(100).pattern(/^[a-zA-Z0-9_-]+$/).required(),
  config_name: Joi.string().max(200).required(),
  config_value: configValueSchema.required(),
  display_order: Joi.number().integer().min(0).default(0),
  is_enabled: Joi.boolean().default(true),
  start_time: Joi.date().iso().optional(),
  end_time: Joi.date().iso().greater(Joi.ref('start_time')).optional(),
  group_name: Joi.string().max(100).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
  description: Joi.string().max(500).optional(),
  extra_data: Joi.object().optional(),
  created_by: Joi.string().max(50).optional()
});

// 更新配置验证
export const updateConfigSchema = Joi.object({
  config_name: Joi.string().max(200).optional(),
  config_value: Joi.object().optional(), // 更新时不强制验证具体结构
  display_order: Joi.number().integer().min(0).optional(),
  is_enabled: Joi.boolean().optional(),
  start_time: Joi.date().iso().optional(),
  end_time: Joi.date().iso().optional(),
  group_name: Joi.string().max(100).allow(null).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).allow(null).optional(),
  description: Joi.string().max(500).allow(null).optional(),
  extra_data: Joi.object().allow(null).optional(),
  updated_by: Joi.string().max(50).optional()
}).min(1); // 至少需要一个字段

// 批量更新验证
export const batchUpdateSchema = Joi.object({
  config_ids: Joi.array().items(Joi.number().integer().positive()).min(1).max(100).required(),
  updates: Joi.object({
    is_enabled: Joi.boolean().optional(),
    display_order: Joi.number().integer().min(0).optional(),
    group_name: Joi.string().max(100).allow(null).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).allow(null).optional(),
    description: Joi.string().max(500).allow(null).optional(),
    extra_data: Joi.object().allow(null).optional()
  }).min(1).required(),
  operator: Joi.string().max(50).optional(),
  operation_reason: Joi.string().max(255).optional()
});

// 查询参数验证
export const configQuerySchema = Joi.object({
  config_type: configTypeSchema.optional(),
  config_key: Joi.string().max(100).optional(),
  group_name: Joi.string().max(100).optional(),
  is_enabled: Joi.boolean().optional(),
  tags: Joi.array().items(Joi.string().max(50)).optional(),
  start_date: Joi.date().iso().optional(),
  end_date: Joi.date().iso().greater(Joi.ref('start_date')).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sort_by: Joi.string().valid('display_order', 'created_at', 'updated_at').default('display_order'),
  sort_order: Joi.string().valid('ASC', 'DESC').default('ASC')
});

// ID参数验证
export const idParamSchema = Joi.object({
  id: Joi.number().integer().positive().required()
});

// 配置类型参数验证
export const typeParamSchema = Joi.object({
  type: configTypeSchema.required()
});

// 分组参数验证
export const groupParamSchema = Joi.object({
  group: Joi.string().max(100).required()
});

// 导出所有验证器
export default {
  createConfigSchema,
  updateConfigSchema,
  batchUpdateSchema,
  configQuerySchema,
  idParamSchema,
  typeParamSchema,
  groupParamSchema
};
