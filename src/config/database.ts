import mysql from 'mysql2/promise';
import config from './index';
import logger from './logger';

// 数据库连接配置
const dbConfig = {
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.name,
  charset: config.database.charset,
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  // 连接池配置
  connectionLimit: 10,
  queueLimit: 0,
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

/**
 * 数据库连接类
 */
export class Database {
  private static instance: Database;
  private pool: mysql.Pool;

  private constructor() {
    this.pool = pool;
  }

  /**
   * 获取数据库实例 (单例模式)
   */
  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  /**
   * 获取数据库连接
   */
  public async getConnection(): Promise<mysql.PoolConnection> {
    try {
      const connection = await this.pool.getConnection();
      return connection;
    } catch (error) {
      logger.error('Failed to get database connection:', error);
      throw error;
    }
  }

  /**
   * 执行查询
   */
  public async query(sql: string, params?: any[]): Promise<any> {
    const connection = await this.getConnection();
    try {
      const [rows] = await connection.execute(sql, params || []);
      return rows;
    } catch (error) {
      logger.error('Database query error:', { sql, params, error });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 执行事务
   */
  public async transaction(callback: (connection: mysql.PoolConnection) => Promise<any>): Promise<any> {
    const connection = await this.getConnection();
    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      logger.error('Transaction error:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 测试数据库连接
   */
  public async testConnection(): Promise<boolean> {
    try {
      const connection = await this.getConnection();
      await connection.ping();
      connection.release();
      logger.info('Database connection test successful');
      return true;
    } catch (error) {
      logger.error('Database connection test failed:', error);
      return false;
    }
  }

  /**
   * 关闭连接池
   */
  public async close(): Promise<void> {
    try {
      await this.pool.end();
      logger.info('Database connection pool closed');
    } catch (error) {
      logger.error('Error closing database connection pool:', error);
      throw error;
    }
  }
}

// 导出数据库实例
export const db = Database.getInstance();

// 导出原始连接池 (用于特殊情况)
export { pool };

/**
 * 数据库初始化函数
 */
export async function initializeDatabase(): Promise<void> {
  try {
    logger.info('Initializing database connection...');
    
    // 测试连接
    const isConnected = await db.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }

    // 检查数据库是否存在必要的表
    const tables = await db.query('SHOW TABLES');
    const tableNames = tables.map((table: any) => Object.values(table)[0]);
    
    const requiredTables = ['users', 'admins', 'sessions', 'operation_logs'];
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));
    
    if (missingTables.length > 0) {
      logger.warn('Missing database tables:', missingTables);
      logger.info('Please run the database setup script to create missing tables');
    } else {
      logger.info('All required database tables found');
    }

    logger.info('Database initialization completed');
  } catch (error) {
    logger.error('Database initialization failed:', error);
    throw error;
  }
}

/**
 * 优雅关闭数据库连接
 */
export async function closeDatabase(): Promise<void> {
  try {
    await db.close();
  } catch (error) {
    logger.error('Error during database shutdown:', error);
  }
}

// 进程退出时关闭数据库连接
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, closing database connections...');
  await closeDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, closing database connections...');
  await closeDatabase();
  process.exit(0);
});

export default db;
