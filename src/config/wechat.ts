import { config } from './index';

/**
 * 微信小程序配置
 */
export const wechatConfig = {
  // 微信小程序 AppID
  appId: config.wechat.appId,

  // 微信小程序 AppSecret
  appSecret: config.wechat.appSecret,
  
  // 微信API基础URL
  apiBaseUrl: 'https://api.weixin.qq.com',
  
  // 微信小程序登录API
  loginUrl: 'https://api.weixin.qq.com/sns/jscode2session',
  
  // 微信小程序获取手机号API
  phoneUrl: 'https://api.weixin.qq.com/wxa/business/getuserphonenumber',
  
  // 微信小程序获取access_token API
  accessTokenUrl: 'https://api.weixin.qq.com/cgi-bin/token',
  
  // 请求超时时间（毫秒）
  timeout: 10000,
  
  // 错误码映射
  errorCodes: {
    '-1': '系统繁忙，此时请开发者稍候再试',
    '0': '请求成功',
    '40013': 'invalid appid',
    '40014': 'invalid access_token',
    '40029': 'invalid code',
    '45011': 'API 调用太频繁，请稍候再试',
    '40226': '高风险等级用户，小程序登录拦截',
    '40001': 'invalid credential, access_token is invalid or not latest',
  } as Record<string, string>
};

/**
 * 微信错误处理
 */
export class WechatError extends Error {
  public errcode: number;
  public errmsg: string;

  constructor(errcode: number, errmsg: string) {
    super(`WeChat API Error: ${errcode} - ${errmsg}`);
    this.name = 'WechatError';
    this.errcode = errcode;
    this.errmsg = errmsg;
  }
}

/**
 * 微信API响应接口
 */
export interface WechatApiResponse {
  errcode?: number;
  errmsg?: string;
}

/**
 * 微信登录响应接口
 */
export interface WechatLoginResponse extends WechatApiResponse {
  openid?: string;
  session_key?: string;
  unionid?: string;
}

/**
 * 微信手机号响应接口
 */
export interface WechatPhoneResponse extends WechatApiResponse {
  phone_info?: {
    phoneNumber: string;
    purePhoneNumber: string;
    countryCode: string;
    watermark: {
      timestamp: number;
      appid: string;
    };
  };
}

/**
 * 微信Access Token响应接口
 */
export interface WechatAccessTokenResponse extends WechatApiResponse {
  access_token?: string;
  expires_in?: number;
}

/**
 * 微信用户信息接口
 */
export interface WechatUserInfo {
  openId: string;
  unionId?: string;
  sessionKey: string;
  phoneNumber?: string;
  nickname?: string;
  avatarUrl?: string;
  gender?: number;
  city?: string;
  province?: string;
  country?: string;
  language?: string;
}
