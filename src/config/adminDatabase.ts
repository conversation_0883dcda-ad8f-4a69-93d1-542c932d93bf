import mysql from 'mysql2/promise';
import config from './index';
import logger from './logger';

/**
 * 管理员数据库连接类 (baofeng_admin)
 */
class AdminDatabase {
  private static instance: AdminDatabase;
  private pool: mysql.Pool;

  private constructor() {
    this.pool = mysql.createPool({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password,
      database: 'baofeng_admin', // 固定使用 baofeng_admin 数据库
      charset: config.database.charset,
      timezone: '+08:00',
      // 连接池配置
      connectionLimit: 10,
      queueLimit: 0,
    });

    logger.info('Admin database connection pool created for baofeng_admin');
  }

  public static getInstance(): AdminDatabase {
    if (!AdminDatabase.instance) {
      AdminDatabase.instance = new AdminDatabase();
    }
    return AdminDatabase.instance;
  }

  /**
   * 获取数据库连接
   */
  public async getConnection(): Promise<mysql.PoolConnection> {
    try {
      return await this.pool.getConnection();
    } catch (error) {
      logger.error('Failed to get admin database connection:', error);
      throw error;
    }
  }

  /**
   * 执行查询
   */
  public async query(sql: string, params?: any[]): Promise<any> {
    const connection = await this.getConnection();
    try {
      const [rows] = await connection.execute(sql, params || []);
      return rows;
    } catch (error) {
      logger.error('Admin database query error:', { sql, params, error });
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 执行事务
   */
  public async transaction(callback: (connection: mysql.PoolConnection) => Promise<any>): Promise<any> {
    const connection = await this.getConnection();
    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      logger.error('Admin database transaction error:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 测试数据库连接
   */
  public async testConnection(): Promise<boolean> {
    try {
      const connection = await this.getConnection();
      await connection.ping();
      connection.release();
      logger.info('Admin database connection test successful');
      return true;
    } catch (error) {
      logger.error('Admin database connection test failed:', error);
      return false;
    }
  }

  /**
   * 关闭连接池
   */
  public async close(): Promise<void> {
    try {
      await this.pool.end();
      logger.info('Admin database connection pool closed');
    } catch (error) {
      logger.error('Error closing admin database connection pool:', error);
      throw error;
    }
  }
}

// 导出管理员数据库实例
export const adminDb = AdminDatabase.getInstance();

/**
 * 管理员数据库初始化函数
 */
export async function initializeAdminDatabase(): Promise<void> {
  try {
    logger.info('Initializing admin database connection...');
    
    // 测试连接
    const isConnected = await adminDb.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to admin database');
    }

    // 检查数据库是否存在必要的表
    const tables = await adminDb.query('SHOW TABLES');
    const tableNames = tables.map((table: any) => Object.values(table)[0]);
    
    const requiredTables = ['admins', 'users', 'sessions', 'operation_logs'];
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));
    
    if (missingTables.length > 0) {
      logger.warn('Missing admin database tables:', missingTables);
    } else {
      logger.info('All required admin database tables found');
    }

    logger.info('Admin database initialization completed');
  } catch (error) {
    logger.error('Admin database initialization failed:', error);
    throw error;
  }
}

/**
 * 优雅关闭管理员数据库连接
 */
export async function closeAdminDatabase(): Promise<void> {
  try {
    await adminDb.close();
  } catch (error) {
    logger.error('Error during admin database shutdown:', error);
  }
}

export default adminDb;
