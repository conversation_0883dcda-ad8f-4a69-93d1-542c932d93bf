import { Response } from 'express';
import { RecycleStoreModel } from '../models/RecycleStore';
import { CreateRecycleStoreDto, UpdateRecycleStoreDto, AuthenticatedRequest } from '../types';
import logger from '../config/logger';

export class RecycleStoreController {
  /**
   * 创建回收门店
   */
  static async createStore(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const storeData: CreateRecycleStoreDto = req.body;

      // 基本验证
      if (!storeData.storeCode || !storeData.storeName || !storeData.mainPhone) {
        res.status(400).json({
          success: false,
          message: 'Store code, store name, and main phone are required'
        });
        return;
      }

      if (!storeData.province || !storeData.city || !storeData.district || !storeData.detailedAddress) {
        res.status(400).json({
          success: false,
          message: 'Complete address information is required'
        });
        return;
      }

      const newStore = await RecycleStoreModel.create(storeData);

      logger.info('Store created successfully', {
        storeId: newStore.id,
        storeCode: newStore.storeCode,
        operator: req.user?.userId
      });

      res.status(201).json({
        success: true,
        message: 'Store created successfully',
        data: newStore
      });
    } catch (error: any) {
      logger.error('Error creating store:', error);
      
      if (error.message === 'Store code already exists') {
        res.status(409).json({
          success: false,
          message: 'Store code already exists'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 获取门店详情
   */
  static async getStore(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const storeId = parseInt(id!);

      if (isNaN(storeId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid store ID'
        });
        return;
      }

      const store = await RecycleStoreModel.findById(storeId);

      if (!store) {
        res.status(404).json({
          success: false,
          message: 'Store not found'
        });
        return;
      }

      res.json({
        success: true,
        data: store
      });
    } catch (error: any) {
      logger.error('Error getting store:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 获取门店列表
   */
  static async getStores(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const {
        page = '1',
        limit = '5',
      } = req.query;

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);

      if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
        res.status(400).json({
          success: false,
          message: 'Invalid page or limit parameters'
        });
        return;
      }

      const options = {
        page: pageNum,
        limit: Math.min(limitNum, 100), // 限制最大每页数量
      };

      const result = await RecycleStoreModel.findAll(options);

      res.json({
        success: true,
        data: {
          list: result.stores,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: result.total,
            totalPages: Math.ceil(result.total / limitNum)
          }
        }
      });
    } catch (error: any) {
      logger.error('Error getting stores:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 更新门店信息
   */
  static async updateStore(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const storeId = parseInt(id!);
      const updateData: UpdateRecycleStoreDto = req.body;

      if (isNaN(storeId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid store ID'
        });
        return;
      }

      const updatedStore = await RecycleStoreModel.update(storeId, updateData);

      logger.info('Store updated successfully', {
        storeId: updatedStore.id,
        storeCode: updatedStore.storeCode,
        operator: req.user?.userId
      });

      res.json({
        success: true,
        message: 'Store updated successfully',
        data: updatedStore
      });
    } catch (error: any) {
      logger.error('Error updating store:', error);
      
      if (error.message === 'Store not found') {
        res.status(404).json({
          success: false,
          message: 'Store not found'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 删除门店
   */
  static async deleteStore(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const storeId = parseInt(id!);

      if (isNaN(storeId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid store ID'
        });
        return;
      }

      const success = await RecycleStoreModel.delete(storeId);

      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Store not found'
        });
        return;
      }

      logger.info('Store deleted successfully', {
        storeId,
        operator: req.user?.userId
      });

      res.json({
        success: true,
        message: 'Store deleted successfully'
      });
    } catch (error: any) {
      logger.error('Error deleting store:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 根据位置查找附近门店
   */
  static async getNearbyStores(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { longitude, latitude, radius = '10' } = req.query;

      if (!longitude || !latitude) {
        res.status(400).json({
          success: false,
          message: 'Longitude and latitude are required'
        });
        return;
      }

      const lng = parseFloat(longitude as string);
      const lat = parseFloat(latitude as string);
      const radiusNum = parseFloat(radius as string);

      if (isNaN(lng) || isNaN(lat) || isNaN(radiusNum)) {
        res.status(400).json({
          success: false,
          message: 'Invalid coordinates or radius'
        });
        return;
      }

      const stores = await RecycleStoreModel.findNearby(lng, lat, radiusNum);

      res.json({
        success: true,
        data: stores
      });
    } catch (error: any) {
      logger.error('Error getting nearby stores:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 根据门店编码获取门店信息
   */
  static async getStoreByCode(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { storeCode } = req.params;

      if (!storeCode) {
        res.status(400).json({
          success: false,
          message: 'Store code is required'
        });
        return;
      }

      const store = await RecycleStoreModel.findByStoreCode(storeCode);

      if (!store) {
        res.status(404).json({
          success: false,
          message: 'Store not found'
        });
        return;
      }

      res.json({
        success: true,
        data: store
      });
    } catch (error: any) {
      logger.error('Error getting store by code:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}
