import { Request, Response } from 'express';
import { AuthenticatedRequest, Admin } from '../types';
import { AdminAuthService } from '../services/adminAuthService';
import { sendSuccess, sendCreated } from '../utils/response';
import { async<PERSON>and<PERSON> } from '../middleware/errorHandler';

/**
 * Remove sensitive fields from admin object for safe response
 */
const sanitizeAdminResponse = (admin: Admin) => ({
  id: admin.id,
  email: admin.email,
  username: admin.username,
  firstName: admin.firstName,
  lastName: admin.lastName,
  role: admin.role,
  isActive: admin.isActive,
});

/**
 * Remove sensitive fields from admin object for profile response (includes more fields)
 */
const sanitizeAdminProfile = (admin: Admin) => ({
  id: admin.id,
  email: admin.email,
  username: admin.username,
  firstName: admin.firstName,
  lastName: admin.lastName,
  role: admin.role,
  isActive: admin.isActive,
  avatar: admin.avatar,
  createdAt: admin.createdAt,
  updatedAt: admin.updatedAt,
  lastLoginAt: admin.lastLoginAt,
});

export class AdminAuthController {
  /**
   * Admin login
   */
  static login = asyncHandler(
    async (req: Request, res: Response) => {
      try {
        const loginData = req.body;
        const { tokens } = await AdminAuthService.login(loginData);
        sendSuccess(res, {
          tokens:tokens.accessToken,
        }, 'Admin login successful');
      } catch (error) {
        console.error('Admin login error:', error);
        throw error;
      }
    }
  );

  /**
   * Get current admin profile
   */
  static getProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const adminId = req.user!.userId;
      const admin = await AdminAuthService.getProfile(adminId);

      // Only return safe profile information
      const profileResponse = sanitizeAdminProfile(admin);

      sendSuccess(res, profileResponse, 'Admin profile retrieved successfully');
    }
  );

  /**
   * Update current admin profile
   */
  static updateProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const adminId = req.user!.userId;
      const updateData = req.body;
      const admin = await AdminAuthService.updateProfile(adminId, updateData);

      // Only return safe profile information
      const profileResponse = sanitizeAdminProfile(admin);

      sendSuccess(res, profileResponse, 'Admin profile updated successfully');
    }
  );

  /**
   * Change admin password
   */
  static changePassword = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const adminId = req.user!.userId;
      const { currentPassword, newPassword } = req.body;
      
      await AdminAuthService.changePassword(adminId, currentPassword, newPassword);

      sendSuccess(res, null, 'Password changed successfully');
    }
  );

  /**
   * Admin logout
   */
  static logout = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const adminId = req.user!.userId;
      await AdminAuthService.logout(adminId);

      sendSuccess(res, null, 'Admin logout successful');
    }
  );

  /**
   * Create new admin (super admin only)
   */
  static createAdmin = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const adminData = req.body;
      const createdBy = req.user!.userId;

      const admin = await AdminAuthService.createAdmin({ ...adminData, createdBy });

      // Only return safe admin information
      const adminResponse = sanitizeAdminProfile(admin);

      sendCreated(res, adminResponse, 'Admin created successfully');
    }
  );
}
