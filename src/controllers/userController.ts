import { Response } from 'express';
import { AuthenticatedRequest, UserQuery } from '../types';
import { UserService } from '../services/userService';
import { sendSuccess, sendCreated, sendPaginated, sendNoContent } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';

export class UserController {
  /**
   * Get all users with pagination and filtering
   */
  static getUsers = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const query = req.query as UserQuery;
      const { users, total } = await UserService.getUsers(query);
      
      const page = Number(query.page) || 1;
      const limit = Number(query.limit) || 10;

      sendPaginated(res, users, page, limit, total, 'Users retrieved successfully');
    }
  );

  /**
   * Get user by ID
   */
  static getUserById = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { id } = req.params;
      if (!id) {
        throw new Error('User ID is required');
      }
      const user = await UserService.getUserById(id);

      sendSuccess(res, user, 'User retrieved successfully');
    }
  );

  /**
   * Create new user
   */
  static createUser = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userData = req.body;
      const user = await UserService.createUser(userData);

      sendCreated(res, user, 'User created successfully');
    }
  );

  /**
   * Update user
   */
  static updateUser = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { id } = req.params;
      if (!id) {
        throw new Error('User ID is required');
      }
      const updateData = req.body;

      const user = await UserService.updateUser(id, updateData);

      sendSuccess(res, user, 'User updated successfully');
    }
  );

  /**
   * Delete user
   */
  static deleteUser = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { id } = req.params;
      if (!id) {
        throw new Error('User ID is required');
      }
      await UserService.deleteUser(id);

      sendNoContent(res);
    }
  );

  /**
   * Get current user's profile
   */
  static getProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const user = await UserService.getUserById(userId);

      sendSuccess(res, user, 'Profile retrieved successfully');
    }
  );

  /**
   * Update current user's profile
   */
  static updateProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const updateData = req.body;
      
      const user = await UserService.updateUser(userId, updateData);

      sendSuccess(res, user, 'Profile updated successfully');
    }
  );
}
