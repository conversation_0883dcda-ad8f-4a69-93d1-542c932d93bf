import { Request, Response } from 'express';
import { ExportService } from '../services/exportService';
import { sendSuccess, sendError } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../types';
import logger from '../config/logger';

/**
 * 数据导出控制器
 */
export class ExportController {
  /**
   * 导出用户数据到Excel并发送邮件
   */
  static exportUsers = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const { email } = req.body;
      const userId = req.user!.userId;
      const userRole = req.user!.role;

      // 验证权限（只有管理员可以导出）
      if (userRole !== 'admin' && userRole !== 'super_admin') {
        sendError(res, '权限不足，只有管理员可以导出用户数据', 403);
        return;
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!email || !emailRegex.test(email)) {
        sendError(res, '请提供有效的邮箱地址', 400);
        return;
      }

      const clientIp = req.ip || req.socket.remoteAddress || 'unknown';

      logger.info('User data export request', {
        userId,
        userRole,
        email,
        ip: clientIp
      });

      try {
        // 异步执行导出任务
        ExportService.exportUsersToExcelAndEmail(email).catch(error => {
          logger.error('Background export task failed:', error);
        });

        sendSuccess(res, {
          message: '导出任务已启动，Excel文件将发送到指定邮箱',
          email: email
        }, '用户数据导出任务已启动');

      } catch (error) {
        logger.error('Export users failed:', error);
        sendError(res, '导出失败，请稍后重试', 500);
      }
    }
  );

  /**
   * 获取用户统计信息
   */
  static getUserStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const userRole = req.user!.role;

      // 验证权限（只有管理员可以查看统计）
      if (userRole !== 'admin' && userRole !== 'super_admin') {
        sendError(res, '权限不足，只有管理员可以查看用户统计', 403);
        return;
      }

      logger.info('User stats request', { userId, userRole });

      const stats = await ExportService.getUserStats();

      sendSuccess(res, stats, '用户统计信息获取成功');
    }
  );

  /**
   * 直接下载用户数据Excel文件
   */
  static downloadUsersExcel = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const userRole = req.user!.role;

      // 验证权限（只有管理员可以下载）
      if (userRole !== 'admin' && userRole !== 'super_admin') {
        sendError(res, '权限不足，只有管理员可以下载用户数据', 403);
        return;
      }

      const clientIp = req.ip || req.socket.remoteAddress || 'unknown';

      logger.info('User data download request', {
        userId,
        userRole,
        ip: clientIp
      });

      try {
        // 获取所有用户数据
        const users = await ExportService.getUserStats();
        
        // 这里可以直接生成Excel并返回文件流
        // 为了简化，我们返回统计信息
        sendSuccess(res, {
          message: '请使用邮件导出功能获取完整Excel文件',
          stats: users
        }, '用户数据统计');

      } catch (error) {
        logger.error('Download users excel failed:', error);
        sendError(res, '下载失败，请稍后重试', 500);
      }
    }
  );
}
