import { Request, Response } from 'express';
import knexDb from '../config/knex';
import { db } from '../config/database';
import { sendSuccess, sendCreated, sendError } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';

export class SimpleCategoryController {
  /**
   * Get all brands (phone_brands)
   * GET /api/v1/categories/brands
   */
  static getBrands = asyncHandler(
    async (req: Request, res: Response) => {
      try {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 50;
        const search = req.query.search as string;
        const offset = (page - 1) * limit;

        let query = knexDb('phone_brands').orderBy(['sort_index', 'id']);

        if (search) {
          query = query.where('name', 'like', `%${search}%`);
        }

        const rows = await query.limit(limit).offset(offset);

        // Get total count
        let countQuery = knexDb('phone_brands').count('* as total');
        if (search) {
          countQuery = countQuery.where('name', 'like', `%${search}%`);
        }
        const countResult = await countQuery;
        const total = countResult[0] ? Number(countResult[0]['total']) : 0;

        sendSuccess(res, {
          data: rows,
          pagination: {
            page,
            limit,
            total: Number(total),
            totalPages: Math.ceil(Number(total) / limit),
          },
        }, 'Brands retrieved successfully');
      } catch (error) {
        console.error('Error in getBrands:', error);
        throw error;
      }
    }
  );

  /**
   * Soft delete brand (假删除)
   * PUT /api/v1/categories/brands/:id/delete
   */
  static softDeleteBrand = asyncHandler(
    async (req: Request, res: Response) => {
      const id = parseInt(req.params.id as string);

      // Check if brand exists
      const existing = await knexDb('phone_brands').where('id', id).first();
      if (!existing) {
        sendError(res, 'Brand not found', 404);
        return;
      }

      // Update the brand to mark as deleted (假删除)
      const deletedName = existing.name.startsWith('[已删除]') ? existing.name : `[已删除]${existing.name}`;

      await knexDb('phone_brands')
        .where('id', id)
        .update({
          name: deletedName,
          updated_at: knexDb.fn.now()
        });

      sendSuccess(res, null, 'Brand soft deleted successfully');
    }
  );

  /**
   * Get models by brand ID
   * GET /api/v1/categories/brands/:brandId/models
   */
  static getModelsByBrand = asyncHandler(
    async (req: Request, res: Response) => {
      const brandId = parseInt(req.params.brandId as string);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string;
      const offset = (page - 1) * limit;

      let sql = 'SELECT * FROM phone_models WHERE brand_id = ?';
      const params: any[] = [brandId];

      if (search) {
        sql += ' AND name LIKE ?';
        params.push(`%${search}%`);
      }

      sql += ' ORDER BY sort_index ASC, id ASC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      const rows = await db.query(sql, params);
      
      // Get total count
      let countSql = 'SELECT COUNT(*) as total FROM phone_models WHERE brand_id = ?';
      const countParams: any[] = [brandId];
      if (search) {
        countSql += ' AND name LIKE ?';
        countParams.push(`%${search}%`);
      }
      const countRows= await db.query(countSql, countParams);
      const total = (countRows as any)[0].total;

      sendSuccess(res, {
        data: rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      }, 'Models retrieved successfully');
    }
  );

  /**
   * Soft delete model (假删除)
   * PUT /api/v1/categories/models/:id/delete
   */
  static softDeleteModel = asyncHandler(
    async (req: Request, res: Response) => {
      const id = parseInt(req.params.id as string);

      // Check if model exists
      const existing= await db.query('SELECT * FROM phone_models WHERE id = ?', [id]);
      if (!existing || (existing as any[]).length === 0) {
        sendError(res, 'Model not found', 404);
        return;
      }

      // Update the model to mark as deleted (假删除)
      const model = (existing as any[])[0];
      const deletedName = model.name.startsWith('[已删除]') ? model.name : `[已删除]${model.name}`;

      await db.query('UPDATE phone_models SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [deletedName, id]);

      sendSuccess(res, null, 'Model soft deleted successfully');
    }
  );

  /**
   * Get sub-models by model ID
   * GET /api/v1/categories/models/:modelId/sub-models
   */
  static getSubModelsByModel = asyncHandler(
    async (req: Request, res: Response) => {
      const modelId = parseInt(req.params.modelId as string);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string;
      const offset = (page - 1) * limit;

      let sql = 'SELECT * FROM phone_sub_models WHERE model_id = ?';
      const params: any[] = [modelId];

      if (search) {
        sql += ' AND name LIKE ?';
        params.push(`%${search}%`);
      }

      sql += ' ORDER BY sort_index ASC, id ASC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      const rows= await db.query(sql, params);
      
      // Get total count
      let countSql = 'SELECT COUNT(*) as total FROM phone_sub_models WHERE model_id = ?';
      const countParams: any[] = [modelId];
      if (search) {
        countSql += ' AND name LIKE ?';
        countParams.push(`%${search}%`);
      }
      const countRows= await db.query(countSql, countParams);
      const total = (countRows as any)[0].total;

      sendSuccess(res, {
        data: rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      }, 'Sub-models retrieved successfully');
    }
  );

  /**
   * Soft delete sub-model (假删除)
   * PUT /api/v1/categories/sub-models/:id/delete
   */
  static softDeleteSubModel = asyncHandler(
    async (req: Request, res: Response) => {
      const id = parseInt(req.params.id as string);

      // Check if sub-model exists
      const existing= await db.query('SELECT * FROM phone_sub_models WHERE id = ?', [id]);
      if (!existing || (existing as any[]).length === 0) {
        sendError(res, 'Sub-model not found', 404);
        return;
      }

      // Update the sub-model to mark as deleted (假删除)
      const subModel = (existing as any[])[0];
      const deletedName = subModel.name.startsWith('[已删除]') ? subModel.name : `[已删除]${subModel.name}`;

      await db.query('UPDATE phone_sub_models SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [deletedName, id]);

      sendSuccess(res, null, 'Sub-model soft deleted successfully');
    }
  );

  /**
   * Get category tree
   * GET /api/v1/categories/tree
   */
  static getCategoryTree = asyncHandler(
    async (req: Request, res: Response) => {
      // Get all brands
      const brands= await db.query('SELECT * FROM phone_brands ORDER BY sort_index ASC, id ASC');
      
      const tree = [];
      for (const brand of brands as any[]) {
        const brandNode = { ...brand, children: [] };
        
        // Get models for this brand
        const models= await db.query('SELECT * FROM phone_models WHERE brand_id = ? ORDER BY sort_index ASC, id ASC', [brand.id]);
        
        for (const model of models as any[]) {
          const modelNode = { ...model, children: [] };
          
          // Get sub-models for this model
          const subModels= await db.query('SELECT * FROM phone_sub_models WHERE model_id = ? ORDER BY sort_index ASC, id ASC', [model.id]);
          modelNode.children = subModels;
          
          brandNode.children.push(modelNode);
        }
        
        tree.push(brandNode);
      }

      sendSuccess(res, tree, 'Category tree retrieved successfully');
    }
  );

  /**
   * Get category details by ID and table
   * GET /api/v1/categories/:table/:id
   */
  static getCategoryDetails = asyncHandler(
    async (req: Request, res: Response) => {
      const table = req.params.table as string;
      const id = parseInt(req.params.id as string);

      // Validate table name
      const validTables = ['phone_brands', 'phone_models', 'phone_sub_models'];
      if (!validTables.includes(table)) {
        sendError(res, 'Invalid table name', 400);
        return;
      }

      // Get category details
      const category= await db.query(`SELECT * FROM ${table} WHERE id = ?`, [id]);
      if (!category || (category as any[]).length === 0) {
        sendError(res, 'Category not found', 404);
        return;
      }

      const categoryData = (category as any[])[0];

      // Get children count
      let childrenCount = 0;
      if (table === 'phone_brands') {
        const models= await db.query('SELECT COUNT(*) as count FROM phone_models WHERE brand_id = ?', [id]);
        childrenCount = (models as any[])[0].count;
      } else if (table === 'phone_models') {
        const subModels= await db.query('SELECT COUNT(*) as count FROM phone_sub_models WHERE model_id = ?', [id]);
        childrenCount = (subModels as any[])[0].count;
      }

      sendSuccess(res, {
        ...categoryData,
        childrenCount,
        isDeleted: categoryData.name.startsWith('[已删除]'),
      }, 'Category details retrieved successfully');
    }
  );

  /**
   * Restore soft deleted category (恢复假删除)
   * PUT /api/v1/categories/:table/:id/restore
   */
  static restoreCategory = asyncHandler(
    async (req: Request, res: Response) => {
      const table = req.params.table as string;
      const id = parseInt(req.params.id as string);

      // Validate table name
      const validTables = ['phone_brands', 'phone_models', 'phone_sub_models'];
      if (!validTables.includes(table)) {
        sendError(res, 'Invalid table name', 400);
        return;
      }

      // Check if category exists
      const existing= await db.query(`SELECT * FROM ${table} WHERE id = ?`, [id]);
      if (!existing || (existing as any[]).length === 0) {
        sendError(res, 'Category not found', 404);
        return;
      }

      const category = (existing as any[])[0];

      // Check if it's actually deleted
      if (!category.name.startsWith('[已删除]')) {
        sendError(res, 'Category is not deleted', 400);
        return;
      }

      // Restore the category by removing the [已删除] prefix
      const restoredName = category.name.replace(/^\[已删除\]/, '');

      await db.query(`UPDATE ${table} SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`, [restoredName, id]);

      sendSuccess(res, null, 'Category restored successfully');
    }
  );
}
