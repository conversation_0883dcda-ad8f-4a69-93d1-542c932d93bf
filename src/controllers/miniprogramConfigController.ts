import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../types';
import { MiniprogramConfigService } from '../services/miniprogramConfigService';
import { 
  CreateMiniprogramConfigDto, 
  UpdateMiniprogramConfigDto,
  MiniprogramConfigQuery,
  ConfigType,
  BatchUpdateConfigDto
} from '../types/miniprogramConfig';
import { AppError } from '../utils/errors';
import logger from '../config/logger';

export class MiniprogramConfigController {
  /**
   * 创建配置
   * POST /api/v1/miniprogram/configs
   */
  static async createConfig(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const configData: CreateMiniprogramConfigDto = {
        ...req.body,
        created_by: (req.user as any)?.id || 'system'
      };

      const config = await MiniprogramConfigService.createConfig(configData);

      res.status(201).json({
        success: true,
        message: 'Config created successfully',
        data: config
      });
    } catch (error) {
      logger.error('Error in createConfig controller:', error);
      
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error'
        });
      }
    }
  }

  /**
   * 获取配置详情
   * GET /api/v1/miniprogram/configs/:id
   */
  static async getConfig(req: Request, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      if (isNaN(id)) {
        throw new AppError('Invalid config ID', 400);
      }

      const config = await MiniprogramConfigService.getConfigById(id);

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      logger.error('Error in getConfig controller:', error);
      
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error'
        });
      }
    }
  }

  /**
   * 获取配置列表
   * GET /api/v1/miniprogram/configs
   */
  static async getConfigs(req: Request, res: Response): Promise<void> {
    try {
      const query: MiniprogramConfigQuery = {
        config_type: req.query.config_type as ConfigType,
        config_key: req.query.config_key as string,
        group_name: req.query.group_name as string,
        is_enabled: req.query.is_enabled ? req.query.is_enabled === 'true' : undefined,
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        sort_by: req.query.sort_by as 'display_order' | 'created_at' | 'updated_at',
        sort_order: req.query.sort_order as 'ASC' | 'DESC'
      };

      const result = await MiniprogramConfigService.getConfigs(query);

      res.json({
        success: true,
        data: result.configs,
        total: result.total,
        page: query.page,
        limit: query.limit
      });
    } catch (error) {
      logger.error('Error in getConfigs controller:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 获取有效配置（前端使用）
   * GET /api/v1/miniprogram/configs/active
   */
  static async getActiveConfigs(req: Request, res: Response): Promise<void> {
    try {
      const configType = req.query.type as ConfigType;
      const groupName = req.query.group as string;

      const configs = await MiniprogramConfigService.getActiveConfigs(configType, groupName);

      res.json({
        success: true,
        data: configs
      });
    } catch (error) {
      logger.error('Error in getActiveConfigs controller:', error);

      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 获取所有配置并按类型分组（小程序统一接口）
   * GET /api/v1/miniprogram/configs/all
   */
  static async getAllConfigsGrouped(req: Request, res: Response): Promise<void> {
    try {
      const groupedConfigs = await MiniprogramConfigService.getAllConfigsGrouped();

      res.json({
        success: true,
        data: groupedConfigs,
        message: 'Successfully retrieved all configs'
      });
    } catch (error) {
      logger.error('Error in getAllConfigsGrouped controller:', error);

      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 根据类型获取配置
   * GET /api/v1/miniprogram/configs/type/:type
   */
  static async getConfigsByType(req: Request, res: Response): Promise<void> {
    try {
      const configType = req.params.type as ConfigType;
      
      if (!Object.values(ConfigType).includes(configType)) {
        throw new AppError('Invalid config type', 400);
      }

      const configs = await MiniprogramConfigService.getConfigsByType(configType);

      res.json({
        success: true,
        data: configs
      });
    } catch (error) {
      logger.error('Error in getConfigsByType controller:', error);
      
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error'
        });
      }
    }
  }

  /**
   * 根据分组获取配置
   * GET /api/v1/miniprogram/configs/group/:group
   */
  static async getConfigsByGroup(req: Request, res: Response): Promise<void> {
    try {
      const groupName = req.params.group!;
      const configs = await MiniprogramConfigService.getConfigsByGroup(groupName);

      res.json({
        success: true,
        data: configs
      });
    } catch (error) {
      logger.error('Error in getConfigsByGroup controller:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 更新配置
   * PUT /api/v1/miniprogram/configs/:id
   */
  static async updateConfig(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      if (isNaN(id)) {
        throw new AppError('Invalid config ID', 400);
      }

      const updateData: UpdateMiniprogramConfigDto = {
        ...req.body,
        updated_by: (req.user as any)?.id || 'system'
      };

      const config = await MiniprogramConfigService.updateConfig(id, updateData);

      res.json({
        success: true,
        message: 'Config updated successfully',
        data: config
      });
    } catch (error) {
      logger.error('Error in updateConfig controller:', error);
      
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error'
        });
      }
    }
  }

  /**
   * 删除配置
   * DELETE /api/v1/miniprogram/configs/:id
   */
  static async deleteConfig(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      if (isNaN(id)) {
        throw new AppError('Invalid config ID', 400);
      }

      const operator = (req.user as any)?.id || 'system';
      await MiniprogramConfigService.deleteConfig(id, operator);

      res.json({
        success: true,
        message: 'Config deleted successfully'
      });
    } catch (error) {
      logger.error('Error in deleteConfig controller:', error);
      
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error'
        });
      }
    }
  }

  /**
   * 批量更新配置
   * POST /api/v1/miniprogram/configs/batch
   */
  static async batchUpdateConfigs(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const batchData: BatchUpdateConfigDto = {
        ...req.body,
        operator: (req.user as any)?.id || 'system'
      };

      const updatedCount = await MiniprogramConfigService.batchUpdateStatus(batchData);

      res.json({
        success: true,
        message: `Successfully updated ${updatedCount} configs`,
        data: { updated_count: updatedCount }
      });
    } catch (error) {
      logger.error('Error in batchUpdateConfigs controller:', error);
      
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error'
        });
      }
    }
  }

  /**
   * 获取配置统计信息
   * GET /api/v1/miniprogram/configs/statistics
   */
  static async getStatistics(req: Request, res: Response): Promise<void> {
    try {
      const statistics = await MiniprogramConfigService.getStatistics();

      res.json({
        success: true,
        data: statistics
      });
    } catch (error) {
      logger.error('Error in getStatistics controller:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * 获取配置分组信息
   * GET /api/v1/miniprogram/configs/groups
   */
  static async getGroups(req: Request, res: Response): Promise<void> {
    try {
      const groups = await MiniprogramConfigService.getGroups();

      res.json({
        success: true,
        data: groups
      });
    } catch (error) {
      logger.error('Error in getGroups controller:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}
