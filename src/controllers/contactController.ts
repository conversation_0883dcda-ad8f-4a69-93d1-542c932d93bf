import { Request, Response } from 'express';
import { ContactService } from '../services/contactService';
import { ShareConfigService } from '../services/shareConfigService';
import { AuthenticatedRequest } from '../types';
import { sendSuccess, sendCreated, sendError } from '../utils/response';
import { asyncHandler } from '../utils/asyncHandler';
import logger from '../config/logger';

export class ContactController {
  /**
   * Create a new contact
   */
  static createContact = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactData = req.body;

      logger.info('Creating contact', { userId, contactData: { ...contactData, phone: '***' } });

      const contact = await ContactService.createContact(userId, contactData);

      return sendCreated(res, contact, 'Contact created successfully');
    }
  );

  /**
   * Get contact by ID
   */
  static getContactById = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactId = parseInt(req.params.id!);

      if (isNaN(contactId)) {
        return sendError(res, 'Invalid contact ID', 400);
      }

      const contact = await ContactService.getContactById(contactId, userId);

      return sendSuccess(res, contact, 'Contact retrieved successfully');
    }
  );

  /**
   * Get contacts with filtering and pagination
   */
  static getContacts = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const query = req.query;

      logger.info('Getting contacts', { userId, query });

      const result = await ContactService.getContactsByUserId(userId, query);

      return sendSuccess(res, result, 'Contacts retrieved successfully');
    }
  );

  /**
   * Update contact
   */
  static updateContact = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactId = parseInt(req.params.id!);
      const updateData = req.body;

      if (isNaN(contactId)) {
        return sendError(res, 'Invalid contact ID', 400);
      }

      logger.info('Updating contact', { userId, contactId, updateData: { ...updateData, phone: updateData.phone ? '***' : undefined } });

      const contact = await ContactService.updateContact(contactId, userId, updateData);

      return sendSuccess(res, contact, 'Contact updated successfully');
    }
  );

  /**
   * Delete contact
   */
  static deleteContact = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactId = parseInt(req.params.id!);

      if (isNaN(contactId)) {
        return sendError(res, 'Invalid contact ID', 400);
      }

      logger.info('Deleting contact', { userId, contactId });

      await ContactService.deleteContact(contactId, userId);

      return sendSuccess(res, null, 'Contact deleted successfully');
    }
  );

  /**
   * Record contact interaction
   */
  static recordInteraction = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactId = parseInt(req.params.id!);
      const { contactType } = req.body;

      if (isNaN(contactId)) {
        return sendError(res, 'Invalid contact ID', 400);
      }

      if (!contactType) {
        return sendError(res, 'Contact type is required', 400);
      }

      logger.info('Recording contact interaction', { userId, contactId, contactType });

      const contact = await ContactService.recordContactInteraction(contactId, userId, contactType);

      return sendSuccess(res, contact, 'Contact interaction recorded successfully');
    }
  );

  /**
   * Toggle favorite status
   */
  static toggleFavorite = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactId = parseInt(req.params.id!);

      if (isNaN(contactId)) {
        return sendError(res, 'Invalid contact ID', 400);
      }

      logger.info('Toggling favorite status', { userId, contactId });

      const contact = await ContactService.toggleFavorite(contactId, userId);

      return sendSuccess(res, contact, 'Favorite status updated successfully');
    }
  );

  /**
   * Search public contacts
   */
  static searchPublicContacts = asyncHandler(
    async (req: Request, res: Response) => {
      const query = req.query;

      logger.info('Searching public contacts - START', { query });

      const result = await ContactService.searchPublicContacts(query);

      return sendSuccess(res, result, 'Public contacts retrieved successfully');
    }
  );

  /**
   * Get contact statistics
   */
  static getContactStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;

      logger.info('Getting contact statistics', { userId });

      const stats = await ContactService.getContactStats(userId);

      return sendSuccess(res, stats, 'Contact statistics retrieved successfully');
    }
  );

  /**
   * Batch update contacts
   */
  static batchUpdateContacts = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const { contactIds, updateData } = req.body;

      if (!Array.isArray(contactIds) || contactIds.length === 0) {
        return sendError(res, 'Contact IDs array is required', 400);
      }

      if (!updateData || Object.keys(updateData).length === 0) {
        return sendError(res, 'Update data is required', 400);
      }

      logger.info('Batch updating contacts', { userId, contactIds, updateData });

      const contacts = await ContactService.batchUpdateContacts(contactIds, userId, updateData);

      return sendSuccess(res, contacts, 'Contacts updated successfully');
    }
  );

  /**
   * Get contacts by category
   */
  static getContactsByCategory = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const { category } = req.params;

      if (!category) {
        return sendError(res, 'Category is required', 400);
      }

      logger.info('Getting contacts by category', { userId, category });

      const contacts = await ContactService.getContactsByCategory(userId, category);

      return sendSuccess(res, contacts, 'Contacts retrieved successfully');
    }
  );

  // Share Config related methods

  /**
   * Create share config for contact
   */
  static createShareConfig = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactId = parseInt(req.params.id!);
      const configData = req.body;

      if (isNaN(contactId)) {
        return sendError(res, 'Invalid contact ID', 400);
      }

      configData.contactId = contactId;

      logger.info('Creating share config', { userId, contactId, configData });

      const shareConfig = await ShareConfigService.createShareConfig(userId, configData);

      return sendCreated(res, shareConfig, 'Share config created successfully');
    }
  );

  /**
   * Get share configs for contact
   */
  static getShareConfigs = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const contactId = parseInt(req.params.id!);

      if (isNaN(contactId)) {
        return sendError(res, 'Invalid contact ID', 400);
      }

      logger.info('Getting share configs', { userId, contactId });

      const shareConfigs = await ShareConfigService.getShareConfigsByContactId(contactId, userId);

      return sendSuccess(res, shareConfigs, 'Share configs retrieved successfully');
    }
  );

  /**
   * Update share config
   */
  static updateShareConfig = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const configId = parseInt(req.params.configId!);
      const updateData = req.body;

      if (isNaN(configId)) {
        return sendError(res, 'Invalid config ID', 400);
      }

      logger.info('Updating share config', { userId, configId, updateData });

      const shareConfig = await ShareConfigService.updateShareConfig(configId, userId, updateData);

      return sendSuccess(res, shareConfig, 'Share config updated successfully');
    }
  );

  /**
   * Delete share config
   */
  static deleteShareConfig = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const configId = parseInt(req.params.configId!);

      if (isNaN(configId)) {
        return sendError(res, 'Invalid config ID', 400);
      }

      logger.info('Deleting share config', { userId, configId });

      await ShareConfigService.deleteShareConfig(configId, userId);

      return sendSuccess(res, null, 'Share config deleted successfully');
    }
  );

  /**
   * Generate share link
   */
  static generateShareLink = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const configId = parseInt(req.params.configId!);

      if (isNaN(configId)) {
        return sendError(res, 'Invalid config ID', 400);
      }

      logger.info('Generating share link', { userId, configId });

      const shareData = await ShareConfigService.generateShareLink(configId, userId);

      return sendSuccess(res, shareData, 'Share link generated successfully');
    }
  );

  /**
   * Access shared contact (public endpoint)
   */
  static accessSharedContact = asyncHandler(
    async (req: Request, res: Response) => {
      const configId = parseInt(req.params.configId!);
      const visitorUserId = (req as any).user?.userId; // Optional authentication

      if (isNaN(configId)) {
        return sendError(res, 'Invalid config ID', 400);
      }

      logger.info('Accessing shared contact', { configId, visitorUserId });

      const result = await ShareConfigService.accessSharedContact(configId, visitorUserId);

      return sendSuccess(res, result, 'Shared contact accessed successfully');
    }
  );

  /**
   * Get share statistics
   */
  static getShareStats = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const configId = parseInt(req.params.configId!);

      if (isNaN(configId)) {
        return sendError(res, 'Invalid config ID', 400);
      }

      logger.info('Getting share statistics', { userId, configId });

      const stats = await ShareConfigService.getShareStats(configId, userId);

      return sendSuccess(res, stats, 'Share statistics retrieved successfully');
    }
  );
}
