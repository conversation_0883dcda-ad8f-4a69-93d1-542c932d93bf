# 🎮 控制器模块详细文档

## 📋 **模块概述**

控制器层负责处理HTTP请求，调用业务逻辑服务，并返回响应。所有控制器都使用统一的错误处理和响应格式。

## 📁 **文件结构**

```
src/controllers/
├── README.md                    # 本文档
├── authController.ts           # C端用户认证控制器
├── userController.ts           # C端用户管理控制器
├── adminAuthController.ts      # B端管理员认证控制器
├── categoryControllerV2.ts     # 类目管理控制器V2
└── simpleCategoryController.ts # 简单类目控制器(未使用)
```

---

## 👤 **用户认证模块**
**文件**: `authController.ts`  
**路由前缀**: `/api/v1/auth`

### 📋 **接口列表**

| 方法 | 路径 | 功能 | 认证 | 描述 |
|------|------|------|------|------|
| POST | `/register` | 用户注册 | ❌ | 新用户注册 |
| POST | `/login` | 用户登录 | ❌ | 用户登录获取Token |
| POST | `/refresh` | 刷新Token | ❌ | 使用RefreshToken获取新的AccessToken |
| POST | `/logout` | 用户登出 | ✅ | 用户登出，清除Token |
| GET | `/profile` | 获取个人资料 | ✅ | 获取当前用户资料 |
| PUT | `/profile` | 更新个人资料 | ✅ | 更新当前用户资料 |
| GET | `/user` | 获取当前用户 | ❌ | 通用接口，自动识别用户类型 |

### 🔧 **控制器方法详情**

#### 1. 用户注册 `register`
```typescript
static register = asyncHandler(async (req: Request, res: Response) => {
  const userData = req.body;
  const { user, tokens } = await AuthService.register(userData);
  // 返回用户信息和Token
});
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "张",
  "lastName": "三",
  "phone": "13800138000",
  "role": "user"
}
```

#### 2. 用户登录 `login`
```typescript
static login = asyncHandler(async (req: Request, res: Response) => {
  const loginData = req.body;
  const { user, tokens } = await AuthService.login(loginData);
  // 返回用户信息和Token
});
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 3. 刷新Token `refreshToken`
```typescript
static refreshToken = asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;
  const tokens = await AuthService.refreshToken(refreshToken);
  // 返回新的Token
});
```

#### 4. 用户登出 `logout`
```typescript
static logout = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.userId;
  await AuthService.logout(userId);
  // 清除用户Token
});
```

#### 5. 获取个人资料 `getProfile`
```typescript
static getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.userId;
  const user = await AuthService.getProfile(userId);
  // 返回用户资料
});
```

#### 6. 更新个人资料 `updateProfile`
```typescript
static updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.userId;
  const updateData = req.body;
  const user = await AuthService.updateProfile(userId, updateData);
  // 返回更新后的用户资料
});
```

#### 7. 获取当前用户 `getCurrentUser`
```typescript
static getCurrentUser = asyncHandler(async (req: Request, res: Response) => {
  // 自动识别Token类型，返回对应的用户信息
  // 支持普通用户和管理员Token
});
```

---

## 👥 **用户管理模块**
**文件**: `userController.ts`  
**路由前缀**: `/api/v1/users`

### 📋 **接口列表**

| 方法 | 路径 | 功能 | 认证 | 权限 | 描述 |
|------|------|------|------|------|------|
| GET | `/` | 获取用户列表 | ✅ | Admin/Moderator | 分页获取用户列表 |
| GET | `/:id` | 获取用户详情 | ✅ | Admin/Moderator | 根据ID获取用户详情 |
| POST | `/` | 创建用户 | ✅ | Admin | 创建新用户 |
| PUT | `/:id` | 更新用户 | ✅ | Admin | 更新用户信息 |
| DELETE | `/:id` | 删除用户 | ✅ | Admin | 删除用户 |
| GET | `/profile` | 获取个人资料 | ✅ | User | 获取当前用户资料 |
| PUT | `/profile` | 更新个人资料 | ✅ | User | 更新当前用户资料 |

### 🔧 **控制器方法详情**

#### 1. 获取用户列表 `getUsers`
```typescript
static getUsers = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const query = req.query as UserQuery;
  const { users, total } = await UserService.getUsers(query);
  // 返回分页用户列表
});
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `search`: 搜索关键词
- `role`: 用户角色过滤
- `isActive`: 激活状态过滤

#### 2. 获取用户详情 `getUserById`
```typescript
static getUserById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const user = await UserService.getUserById(id);
  // 返回用户详情
});
```

#### 3. 创建用户 `createUser`
```typescript
static createUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userData = req.body;
  const user = await UserService.createUser(userData);
  // 返回创建的用户信息
});
```

#### 4. 更新用户 `updateUser`
```typescript
static updateUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const updateData = req.body;
  const user = await UserService.updateUser(id, updateData);
  // 返回更新后的用户信息
});
```

#### 5. 删除用户 `deleteUser`
```typescript
static deleteUser = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  await UserService.deleteUser(id);
  // 返回删除成功状态
});
```

---

## 🔐 **管理员认证模块**
**文件**: `adminAuthController.ts`  
**路由前缀**: `/api/v1/admin/auth`

### 📋 **接口列表**

| 方法 | 路径 | 功能 | 认证 | 权限 | 描述 |
|------|------|------|------|------|------|
| GET | `/test` | 测试接口 | ❌ | - | 测试管理员路由 |
| POST | `/login` | 管理员登录 | ❌ | - | 管理员登录 |
| GET | `/profile` | 获取管理员资料 | ✅ | Admin | 获取当前管理员资料 |
| PUT | `/profile` | 更新管理员资料 | ✅ | Admin | 更新管理员资料 |
| POST | `/change-password` | 修改密码 | ✅ | Admin | 修改管理员密码 |
| POST | `/logout` | 管理员登出 | ✅ | Admin | 管理员登出 |
| POST | `/create-admin` | 创建管理员 | ✅ | Super Admin | 创建新管理员 |

### 🔧 **控制器方法详情**

#### 1. 管理员登录 `login`
```typescript
static login = asyncHandler(async (req: Request, res: Response) => {
  const loginData = req.body;
  const { tokens } = await AdminAuthService.login(loginData);
  // 返回管理员Token
});
```

**请求体**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```

#### 2. 获取管理员资料 `getProfile`
```typescript
static getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const adminId = req.user!.userId;
  const admin = await AdminAuthService.getProfile(adminId);
  // 返回管理员资料
});
```

#### 3. 修改密码 `changePassword`
```typescript
static changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const adminId = req.user!.userId;
  const { currentPassword, newPassword } = req.body;
  await AdminAuthService.changePassword(adminId, currentPassword, newPassword);
  // 返回修改成功状态
});
```

#### 4. 创建管理员 `createAdmin`
```typescript
static createAdmin = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const adminData = req.body;
  const createdBy = req.user!.userId;
  const admin = await AdminAuthService.createAdmin({ ...adminData, createdBy });
  // 返回创建的管理员信息
});
```

---

## 📱 **类目管理模块**
**文件**: `categoryControllerV2.ts`  
**路由前缀**: `/api/v1/categories`

### 📋 **接口列表**

| 方法 | 路径 | 功能 | 认证 | 描述 |
|------|------|------|------|------|
| GET | `/` | 获取根类目 | ❌ | 获取所有根类目 |
| GET | `/tree` | 获取类目树 | ❌ | 获取完整类目树结构 |
| GET | `/brands` | 获取所有品牌 | ❌ | 获取品牌列表，支持搜索和过滤 |
| GET | `/:categoryId/brands` | 根据类目获取品牌树 | ❌ | 根据类目ID获取品牌树状结构(品牌->型号->子型号)，categoryId通过路径参数传递 |
| PUT | `/brands/:id/delete` | 软删除品牌 | ❌ | 标记品牌为已删除 |
| GET | `/brands/:brandId/models` | 获取品牌型号 | ❌ | 根据品牌ID获取型号 |
| PUT | `/models/:id/delete` | 软删除型号 | ✅ | 标记型号为已删除 |
| GET | `/models/:modelId/sub-models` | 获取子型号 | ❌ | 根据型号ID获取子型号 |
| PUT | `/sub-models/:id/delete` | 软删除子型号 | ✅ | 标记子型号为已删除 |
| GET | `/:table/:id` | 获取类目详情 | ❌ | 获取指定类目详情 |
| PUT | `/:table/:id/restore` | 恢复类目 | ✅ | 恢复软删除的类目 |
| GET | `/:table/:id/price` | 获取价格信息 | ❌ | 获取类目价格信息 |
| GET | `/:table/:id/price-trend` | 获取价格趋势 | ❌ | 获取价格趋势分析 |
| GET | `/sub-models/:subModelId/complete` | 获取完整型号详情 | ❌ | 根据子型号ID获取完整的型号、品牌、价格信息 |

### 🔧 **参数传递说明**

#### 路径参数 (URL Params)
以下接口使用路径参数传递关键信息：

- `GET /:categoryId/brands` - categoryId 通过URL路径传递
  ```typescript
  const categoryId = parseInt(req.params.categoryId as string);
  ```

- `GET /brands/:brandId/models` - brandId 通过URL路径传递
  ```typescript
  const brandId = parseInt(req.params.brandId as string);
  ```

- `GET /models/:modelId/sub-models` - modelId 通过URL路径传递
  ```typescript
  const modelId = parseInt(req.params.modelId as string);
  ```

#### 查询参数 (Query Params)
分页和搜索参数通过查询字符串传递：
- `page` - 页码
- `limit` - 每页数量
- `search` - 搜索关键词
- `parent_id` - 父级ID过滤

### 🔗 **数据关联关系**

#### 品牌树状结构接口 `/:categoryId/brands`
该接口实现了三级树状结构查询：

1. **第一级 - 品牌查询**:
   ```sql
   SELECT * FROM phone_brands WHERE parent_id = categoryId
   ```

2. **第二级 - 型号查询**:
   ```sql
   SELECT * FROM phone_models WHERE brand_id = phone_brands.id
   ```

3. **第三级 - 子型号查询**:
   ```sql
   SELECT * FROM phone_sub_models WHERE model_id = phone_models.id
   ```

#### 数据结构关系图
```
Category (类目)
    ↓ parent_id
Brand (品牌)
    ↓ brand_id
Model (型号)
    ↓ model_id
Sub-Model (子型号)
```

### 🔧 **特殊功能说明**

#### 价格查看功能
- 自动记录查看日志到 `price_view_logs` 表
- 记录查看者IP、User-Agent等信息

#### 价格趋势分析
- 支持自定义分析天数 (默认30天)
- 自动计算涨跌趋势和统计信息
- 记录分析日志到 `price_trend_logs` 表

#### 软删除机制
- 通过在名称前添加 `[已删除]` 前缀实现
- 支持恢复功能，移除前缀即可恢复

---

## 🔧 **通用特性**

### 错误处理
所有控制器都使用 `asyncHandler` 包装，自动捕获异步错误。

### 响应格式
使用统一的响应工具函数：
- `sendSuccess()`: 成功响应
- `sendCreated()`: 创建成功响应
- `sendError()`: 错误响应

### 参数验证
使用 Joi 进行请求参数验证，确保数据格式正确。

### 认证授权
使用 JWT Token 进行身份认证，支持角色权限控制。

---

📝 **文档最后更新**: 2025-06-16  
🔧 **维护者**: Baofeng R&D Team
