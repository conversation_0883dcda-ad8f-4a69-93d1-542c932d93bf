import { Request, Response } from 'express';
import { WechatService } from '../services/wechatService';
import { WechatLoginDto, WechatPhoneDto, AuthenticatedRequest } from '../types';
import { sendSuccess, sendCreated, sendError } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../config/logger';

/**
 * 微信小程序控制器
 */
export class WechatController {
  /**
   * 微信小程序登录
   */
  static login = asyncHandler(
    async (req: Request, res: Response) => {
      const loginData: WechatLoginDto = req.body;
      const clientIp = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';

      logger.info('WeChat login attempt', {
        code: loginData.code ? 'provided' : 'missing',
        ip: clientIp,
        userAgent: userAgent.substring(0, 100) // 限制长度避免日志过长
      });

      const result = await WechatService.login(loginData);

      // 记录详细的登录日志，使用 openId 作为主要标识符
      logger.info('WeChat login successful', {
        openId: result.user.wechatOpenId,
        userId: result.user.id, // 保留内部 UUID 用于数据库关联
        isNewUser: result.isNewUser,
        ip: clientIp,
        userAgent: userAgent.substring(0, 100),
        loginTime: new Date().toISOString()
      });

      // 只返回 openId 作为唯一标识符和基本状态信息
      const responseData = {
        openId: result.user.wechatOpenId,
        isNewUser: result.isNewUser,
        accessToken: result.tokens.accessToken
      };

      if (result.isNewUser) {
        sendSuccess(res, responseData, '微信登录成功，新用户已创建');
      } else {
        sendSuccess(res, responseData, '微信登录成功');
      }
    }
  );

  /**
   * 获取微信小程序用户手机号
   */
  static getPhoneNumber = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const phoneData: WechatPhoneDto = req.body;
      const clientIp = req.ip || req.socket.remoteAddress || 'unknown';

      logger.info('WeChat get phone number attempt', {
        userId,
        code: phoneData.code ? 'provided' : 'missing',
        ip: clientIp
      });

      const user = await WechatService.getPhoneNumber(userId, phoneData);

      // 记录手机号获取成功日志
      logger.info('WeChat phone number obtained', {
        openId: user.wechatOpenId,
        userId: user.id,
        phone: user.phone ? `${user.phone.substring(0, 3)}****${user.phone.substring(7)}` : null, // 脱敏处理
        ip: clientIp,
        timestamp: new Date().toISOString()
      });

      // 只返回 openId 确认操作成功
      sendSuccess(res, {
        openId: user.wechatOpenId,
        phoneUpdated: true
      }, '手机号获取成功');
    }
  );

  /**
   * 通过加密数据获取手机号（兼容旧版本API）
   */
  static getPhoneNumberByEncryptedData = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const { encryptedData, iv } = req.body;
      const clientIp = req.ip || req.socket.remoteAddress || 'unknown';

      if (!encryptedData || !iv) {
        sendError(res, '缺少必要的加密数据参数', 400);
        return;
      }

      logger.info('WeChat get phone number by encrypted data attempt', {
        userId,
        ip: clientIp
      });

      const user = await WechatService.getPhoneNumberByEncryptedData(userId, {
        encryptedData,
        iv,
      });

      // 记录手机号获取成功日志
      logger.info('WeChat phone number obtained via encrypted data', {
        openId: user.wechatOpenId,
        userId: user.id,
        phone: user.phone ? `${user.phone.substring(0, 3)}****${user.phone.substring(7)}` : null, // 脱敏处理
        ip: clientIp,
        timestamp: new Date().toISOString()
      });

      // 只返回 openId 确认操作成功
      sendSuccess(res, {
        openId: user.wechatOpenId,
        phoneUpdated: true
      }, '手机号获取成功');
    }
  );

  /**
   * 检查微信登录状态
   */
  static checkLoginStatus = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      
      // 这里可以添加额外的微信登录状态检查逻辑
      // 比如检查session_key是否还有效等
      
      sendSuccess(res, {
        userId,
        isLoggedIn: true,
        loginType: 'wechat',
      }, '微信登录状态正常');
    }
  );

  /**
   * 微信小程序数据解密（通用接口）
   */
  static decryptData = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const { encryptedData, iv } = req.body;

      if (!encryptedData || !iv) {
        sendError(res, '缺少必要的加密数据参数', 400);
        return;
      }

      try {
        // 这里需要获取用户的session_key
        // 在实际应用中，你可能需要从数据库获取用户的session_key
        logger.info('WeChat decrypt data attempt', { userId });

        // 注意：这个接口需要谨慎使用，确保不会泄露敏感信息
        sendError(res, '此接口暂未实现，请使用具体的业务接口', 501);
      } catch (error) {
        logger.error('WeChat decrypt data error:', error);
        sendError(res, '数据解密失败', 500);
      }
    }
  );

  /**
   * 获取微信小程序配置信息（前端需要的公开信息）
   */
  static getConfig = asyncHandler(
    async (req: Request, res: Response) => {
      // 只返回前端需要的公开配置信息
      const config = {
        // 这里可以返回一些前端需要的配置信息
        // 注意：不要返回敏感信息如appSecret
        loginRequired: true,
        phoneAuthRequired: false, // 根据业务需求设置
        supportedFeatures: [
          'login',
          'phoneAuth',
          'userInfo',
        ],
      };
      
      sendSuccess(res, config, '微信配置信息获取成功');
    }
  );

  /**
   * 微信小程序登出
   */
  static logout = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;

      logger.info('WeChat logout attempt', { userId });

      // 在实际应用中，这里可以：
      // 1. 清除用户的session_key
      // 2. 将token加入黑名单
      // 3. 记录登出日志

      sendSuccess(res, null, '微信登出成功');
    }
  );

  /**
   * 测试手机号更新（仅开发环境）
   */
  static testUpdatePhone = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      // 仅在开发环境允许
      if (process.env.NODE_ENV !== 'development') {
        sendError(res, '此接口仅在开发环境可用', 403);
        return;
      }

      const userId = req.user!.userId;
      const { phone } = req.body;
      const clientIp = req.ip || req.socket.remoteAddress || 'unknown';

      if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
        sendError(res, '请提供有效的手机号', 400);
        return;
      }

      logger.info('Test phone number update attempt', {
        userId,
        phone: `${phone.substring(0, 3)}****${phone.substring(7)}`,
        ip: clientIp
      });

      const user = await WechatService.testUpdatePhone(userId, phone);

      logger.info('Test phone number updated', {
        openId: user.wechatOpenId,
        userId: user.id,
        phone: user.phone ? `${user.phone.substring(0, 3)}****${user.phone.substring(7)}` : null,
        ip: clientIp,
        timestamp: new Date().toISOString()
      });

      sendSuccess(res, {
        openId: user.wechatOpenId,
        phoneUpdated: true
      }, '测试手机号更新成功');
    }
  );
}
