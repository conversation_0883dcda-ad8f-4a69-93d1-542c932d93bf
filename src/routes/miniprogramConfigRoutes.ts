import { Router } from 'express';
import { MiniprogramConfigController } from '../controllers/miniprogramConfigController';
import { validateRequest } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import { 
  createConfigSchema, 
  updateConfigSchema, 
  batchUpdateSchema,
  configQuerySchema 
} from '../validators/miniprogramConfigValidator';

const router = Router();

// 公开接口（小程序前端使用，不需要认证）
/**
 * @swagger
 * /api/v1/miniprogram/configs/all:
 *   get:
 *     summary: 获取所有配置（按类型分组）
 *     description: 获取所有有效的小程序配置，按配置类型分组返回，一次调用获取所有配置
 *     tags: [Miniprogram Config]
 *     responses:
 *       200:
 *         description: 成功获取所有配置
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     CAROUSEL:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DisplayConfig'
 *                     ANNOUNCEMENT:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DisplayConfig'
 *                     CONTACT_PHONE:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DisplayConfig'
 *                     WECHAT_COPY:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DisplayConfig'
 *                     SHARE_CONFIG:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DisplayConfig'
 *                     SYSTEM_CONFIG:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DisplayConfig'
 *                 message:
 *                   type: string
 */
router.get('/all', MiniprogramConfigController.getAllConfigsGrouped);

/**
 * @swagger
 * /api/v1/miniprogram/configs/active:
 *   get:
 *     summary: 获取有效配置（支持筛选）
 *     description: 获取当前有效的小程序配置（考虑时间范围和启用状态），支持按类型和分组筛选
 *     tags: [Miniprogram Config]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [CAROUSEL, ANNOUNCEMENT, CONTACT_PHONE, WECHAT_COPY, SHARE_CONFIG, SYSTEM_CONFIG]
 *         description: 配置类型
 *       - in: query
 *         name: group
 *         schema:
 *           type: string
 *         description: 配置分组
 *     responses:
 *       200:
 *         description: 成功获取有效配置
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DisplayConfig'
 */
router.get('/active', MiniprogramConfigController.getActiveConfigs);

// 管理接口（需要认证）
/**
 * @swagger
 * /api/v1/miniprogram/configs:
 *   post:
 *     summary: 创建配置
 *     description: 创建新的小程序配置
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateMiniprogramConfigDto'
 *     responses:
 *       201:
 *         description: 配置创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.post('/', authenticateToken, validateRequest(createConfigSchema), MiniprogramConfigController.createConfig);

/**
 * @swagger
 * /api/v1/miniprogram/configs:
 *   get:
 *     summary: 获取配置列表
 *     description: 获取小程序配置列表（支持分页和筛选）
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: config_type
 *         schema:
 *           type: string
 *           enum: [CAROUSEL, ANNOUNCEMENT, CONTACT_PHONE, WECHAT_COPY, SHARE_CONFIG, SYSTEM_CONFIG]
 *         description: 配置类型
 *       - in: query
 *         name: config_key
 *         schema:
 *           type: string
 *         description: 配置键名
 *       - in: query
 *         name: group_name
 *         schema:
 *           type: string
 *         description: 分组名称
 *       - in: query
 *         name: is_enabled
 *         schema:
 *           type: boolean
 *         description: 是否启用
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           enum: [display_order, created_at, updated_at]
 *         description: 排序字段
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 成功获取配置列表
 */
router.get('/', authenticateToken, validateRequest(configQuerySchema, 'query'), MiniprogramConfigController.getConfigs);

/**
 * @swagger
 * /api/v1/miniprogram/configs/{id}:
 *   get:
 *     summary: 获取配置详情
 *     description: 根据ID获取配置详情
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 成功获取配置详情
 *       404:
 *         description: 配置不存在
 */
router.get('/:id', authenticateToken, MiniprogramConfigController.getConfig);

/**
 * @swagger
 * /api/v1/miniprogram/configs/{id}:
 *   put:
 *     summary: 更新配置
 *     description: 更新指定ID的配置
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 配置ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateMiniprogramConfigDto'
 *     responses:
 *       200:
 *         description: 配置更新成功
 *       404:
 *         description: 配置不存在
 */
router.put('/:id', authenticateToken, validateRequest(updateConfigSchema), MiniprogramConfigController.updateConfig);

/**
 * @swagger
 * /api/v1/miniprogram/configs/{id}:
 *   delete:
 *     summary: 删除配置
 *     description: 删除指定ID的配置
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 配置删除成功
 *       404:
 *         description: 配置不存在
 */
router.delete('/:id', authenticateToken, MiniprogramConfigController.deleteConfig);

/**
 * @swagger
 * /api/v1/miniprogram/configs/type/{type}:
 *   get:
 *     summary: 根据类型获取配置
 *     description: 获取指定类型的所有配置
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [CAROUSEL, ANNOUNCEMENT, CONTACT_PHONE, WECHAT_COPY, SHARE_CONFIG, SYSTEM_CONFIG]
 *         description: 配置类型
 *     responses:
 *       200:
 *         description: 成功获取指定类型的配置
 */
router.get('/type/:type', authenticateToken, MiniprogramConfigController.getConfigsByType);

/**
 * @swagger
 * /api/v1/miniprogram/configs/group/{group}:
 *   get:
 *     summary: 根据分组获取配置
 *     description: 获取指定分组的所有配置
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: group
 *         required: true
 *         schema:
 *           type: string
 *         description: 分组名称
 *     responses:
 *       200:
 *         description: 成功获取指定分组的配置
 */
router.get('/group/:group', authenticateToken, MiniprogramConfigController.getConfigsByGroup);

/**
 * @swagger
 * /api/v1/miniprogram/configs/batch:
 *   post:
 *     summary: 批量更新配置
 *     description: 批量更新多个配置的状态或属性
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/BatchUpdateConfigDto'
 *     responses:
 *       200:
 *         description: 批量更新成功
 */
router.post('/batch', authenticateToken, validateRequest(batchUpdateSchema), MiniprogramConfigController.batchUpdateConfigs);

/**
 * @swagger
 * /api/v1/miniprogram/configs/statistics:
 *   get:
 *     summary: 获取配置统计信息
 *     description: 获取配置的统计信息，包括总数、启用数量等
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取统计信息
 */
router.get('/statistics', authenticateToken, MiniprogramConfigController.getStatistics);

/**
 * @swagger
 * /api/v1/miniprogram/configs/groups:
 *   get:
 *     summary: 获取配置分组信息
 *     description: 获取所有配置分组的信息和统计
 *     tags: [Miniprogram Config]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取分组信息
 */
router.get('/groups', authenticateToken, MiniprogramConfigController.getGroups);

export default router;
