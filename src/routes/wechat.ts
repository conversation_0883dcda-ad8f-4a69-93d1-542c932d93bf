import { Router } from 'express';
import { WechatController } from '../controllers/wechatController';
import { validate } from '../middleware/validation';
import { authenticate } from '../middleware/auth';
import { wechatLoginSchema, wechatPhoneSchema, wechatPhoneDecryptSchema } from '../utils/validation';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     WechatLoginRequest:
 *       type: object
 *       required:
 *         - code
 *       properties:
 *         code:
 *           type: string
 *           description: 微信登录凭证
 *         encryptedData:
 *           type: string
 *           description: 加密的用户数据
 *         iv:
 *           type: string
 *           description: 初始向量
 *         signature:
 *           type: string
 *           description: 数据签名
 *         rawData:
 *           type: string
 *           description: 原始数据
 *     
 *     WechatPhoneRequest:
 *       type: object
 *       required:
 *         - code
 *       properties:
 *         code:
 *           type: string
 *           description: 手机号授权凭证
 *     
 *     WechatLoginResponse:
 *       type: object
 *       properties:
 *         user:
 *           $ref: '#/components/schemas/User'
 *         tokens:
 *           $ref: '#/components/schemas/AuthTokens'
 *         isNewUser:
 *           type: boolean
 *           description: 是否为新用户
 */

/**
 * @swagger
 * /api/v1/wechat/login:
 *   post:
 *     summary: 微信小程序登录
 *     tags: [WeChat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/WechatLoginRequest'
 *           examples:
 *             basic:
 *               summary: 基础登录
 *               value:
 *                 code: "wx_login_code_from_frontend"
 *             withUserInfo:
 *               summary: 包含用户信息的登录
 *               value:
 *                 code: "wx_login_code_from_frontend"
 *                 encryptedData: "encrypted_user_data"
 *                 iv: "initialization_vector"
 *                 signature: "data_signature"
 *                 rawData: "raw_user_data"
 *     responses:
 *       200:
 *         description: 登录成功（已有用户）
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/WechatLoginResponse'
 *       201:
 *         description: 登录成功（新用户创建）
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/WechatLoginResponse'
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/login', validate({ body: wechatLoginSchema }), WechatController.login);

/**
 * @swagger
 * /api/v1/wechat/phone:
 *   post:
 *     summary: 获取微信小程序用户手机号
 *     tags: [WeChat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/WechatPhoneRequest'
 *           example:
 *             code: "phone_auth_code_from_frontend"
 *     responses:
 *       200:
 *         description: 手机号获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: 请求参数错误或手机号已被使用
 *       401:
 *         description: 未授权访问
 *       404:
 *         description: 用户不存在
 *       500:
 *         description: 服务器内部错误
 */
router.post('/phone', authenticate, validate({ body: wechatPhoneSchema }), WechatController.getPhoneNumber);

/**
 * @swagger
 * /api/v1/wechat/phone/decrypt:
 *   post:
 *     summary: 通过加密数据获取手机号（兼容旧版本）
 *     tags: [WeChat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - encryptedData
 *               - iv
 *             properties:
 *               encryptedData:
 *                 type: string
 *                 description: 加密的手机号数据
 *               iv:
 *                 type: string
 *                 description: 初始向量
 *           example:
 *             encryptedData: "encrypted_phone_data"
 *             iv: "initialization_vector"
 *     responses:
 *       200:
 *         description: 手机号获取成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权访问或会话已过期
 *       500:
 *         description: 服务器内部错误
 */
router.post('/phone/decrypt', authenticate, validate({ body: wechatPhoneDecryptSchema }), WechatController.getPhoneNumberByEncryptedData);

/**
 * @swagger
 * /api/v1/wechat/status:
 *   get:
 *     summary: 检查微信登录状态
 *     tags: [WeChat]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 登录状态正常
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     isLoggedIn:
 *                       type: boolean
 *                     loginType:
 *                       type: string
 *       401:
 *         description: 未授权访问
 */
router.get('/status', authenticate, WechatController.checkLoginStatus);

/**
 * @swagger
 * /api/v1/wechat/config:
 *   get:
 *     summary: 获取微信小程序配置信息
 *     tags: [WeChat]
 *     responses:
 *       200:
 *         description: 配置信息获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     loginRequired:
 *                       type: boolean
 *                     phoneAuthRequired:
 *                       type: boolean
 *                     supportedFeatures:
 *                       type: array
 *                       items:
 *                         type: string
 */
router.get('/config', WechatController.getConfig);

/**
 * @swagger
 * /api/v1/wechat/logout:
 *   post:
 *     summary: 微信小程序登出
 *     tags: [WeChat]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 登出成功
 *       401:
 *         description: 未授权访问
 */
router.post('/logout', authenticate, WechatController.logout);

/**
 * @swagger
 * /api/v1/wechat/test/phone:
 *   post:
 *     summary: 测试更新手机号（仅开发环境）
 *     tags: [WeChat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone
 *             properties:
 *               phone:
 *                 type: string
 *                 description: 手机号
 *           example:
 *             phone: "13800138000"
 *     responses:
 *       200:
 *         description: 手机号更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权访问
 *       403:
 *         description: 仅开发环境可用
 */
router.post('/test/phone', authenticate, WechatController.testUpdatePhone);

export default router;
