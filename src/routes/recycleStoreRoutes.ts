import { Router } from 'express';
import { RecycleStoreController } from '../controllers/recycleStoreController';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '../types';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     RecycleStore:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 门店ID
 *         storeCode:
 *           type: string
 *           description: 门店编码
 *         storeName:
 *           type: string
 *           description: 门店名称
 *         province:
 *           type: string
 *           description: 省份
 *         city:
 *           type: string
 *           description: 城市
 *         district:
 *           type: string
 *           description: 区县
 *         detailedAddress:
 *           type: string
 *           description: 详细地址
 *         fullAddress:
 *           type: string
 *           description: 完整地址
 *         longitude:
 *           type: number
 *           description: 经度
 *         latitude:
 *           type: number
 *           description: 纬度
 *         salesPhone:
 *           type: string
 *           description: 销售电话
 *         salesWechat:
 *           type: string
 *           description: 销售微信
 *         recyclePhone:
 *           type: string
 *           description: 回收电话
 *         recycleWechat:
 *           type: string
 *           description: 回收微信
 *         mainPhone:
 *           type: string
 *           description: 主要联系电话
 *         email:
 *           type: string
 *           description: 邮箱地址
 *         businessHours:
 *           type: object
 *           description: 营业时间
 *         businessStatus:
 *           type: string
 *           enum: [OPEN, CLOSED, MAINTENANCE]
 *           description: 营业状态
 *         storeType:
 *           type: string
 *           enum: [FLAGSHIP, STANDARD, MINI, ONLINE]
 *           description: 门店类型
 *         storeArea:
 *           type: number
 *           description: 门店面积
 *         employeeCount:
 *           type: integer
 *           description: 员工数量
 *         serviceRadius:
 *           type: integer
 *           description: 服务半径
 *         supportedCategories:
 *           type: array
 *           items:
 *             type: integer
 *           description: 支持的回收类别
 *         status:
 *           type: integer
 *           description: 状态
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *     CreateRecycleStoreDto:
 *       type: object
 *       required:
 *         - storeCode
 *         - storeName
 *         - province
 *         - city
 *         - district
 *         - detailedAddress
 *         - mainPhone
 *       properties:
 *         storeCode:
 *           type: string
 *           description: 门店编码
 *         storeName:
 *           type: string
 *           description: 门店名称
 *         province:
 *           type: string
 *           description: 省份
 *         city:
 *           type: string
 *           description: 城市
 *         district:
 *           type: string
 *           description: 区县
 *         detailedAddress:
 *           type: string
 *           description: 详细地址
 *         longitude:
 *           type: number
 *           description: 经度
 *         latitude:
 *           type: number
 *           description: 纬度
 *         salesPhone:
 *           type: string
 *           description: 销售电话
 *         salesWechat:
 *           type: string
 *           description: 销售微信
 *         recyclePhone:
 *           type: string
 *           description: 回收电话
 *         recycleWechat:
 *           type: string
 *           description: 回收微信
 *         mainPhone:
 *           type: string
 *           description: 主要联系电话
 *         email:
 *           type: string
 *           description: 邮箱地址
 *         businessHours:
 *           type: object
 *           description: 营业时间
 *         businessStatus:
 *           type: string
 *           enum: [OPEN, CLOSED, MAINTENANCE]
 *           description: 营业状态
 *         storeType:
 *           type: string
 *           enum: [FLAGSHIP, STANDARD, MINI, ONLINE]
 *           description: 门店类型
 *         storeArea:
 *           type: number
 *           description: 门店面积
 *         serviceRadius:
 *           type: integer
 *           description: 服务半径
 *         supportedCategories:
 *           type: array
 *           items:
 *             type: integer
 *           description: 支持的回收类别
 */

/**
 * @swagger
 * /api/recycle-stores:
 *   post:
 *     summary: 创建回收门店
 *     tags: [RecycleStores]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRecycleStoreDto'
 *     responses:
 *       201:
 *         description: 门店创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/RecycleStore'
 *       400:
 *         description: 请求参数错误
 *       409:
 *         description: 门店编码已存在
 *       500:
 *         description: 服务器内部错误
 */
router.post('/', authenticate, authorize(UserRole.ADMIN, UserRole.SUPER_ADMIN), RecycleStoreController.createStore);

/**
 * @swagger
 * /api/recycle-stores:
 *   get:
 *     summary: 获取门店列表
 *     tags: [RecycleStores]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: province
 *         schema:
 *           type: string
 *         description: 省份筛选
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: 城市筛选
 *       - in: query
 *         name: district
 *         schema:
 *           type: string
 *         description: 区县筛选
 *       - in: query
 *         name: businessStatus
 *         schema:
 *           type: string
 *           enum: [OPEN, CLOSED, MAINTENANCE]
 *         description: 营业状态筛选
 *       - in: query
 *         name: storeType
 *         schema:
 *           type: string
 *           enum: [FLAGSHIP, STANDARD, MINI, ONLINE]
 *         description: 门店类型筛选
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词（门店名称、编码、地址）
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     stores:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/RecycleStore'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 */
router.get('/', RecycleStoreController.getStores);

/**
 * @swagger
 * /api/recycle-stores/nearby:
 *   get:
 *     summary: 根据位置查找附近门店
 *     tags: [RecycleStores]
 *     parameters:
 *       - in: query
 *         name: longitude
 *         required: true
 *         schema:
 *           type: number
 *         description: 经度
 *       - in: query
 *         name: latitude
 *         required: true
 *         schema:
 *           type: number
 *         description: 纬度
 *       - in: query
 *         name: radius
 *         schema:
 *           type: number
 *           default: 10
 *         description: 搜索半径（公里）
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RecycleStore'
 */
router.get('/nearby', RecycleStoreController.getNearbyStores);

/**
 * @swagger
 * /api/recycle-stores/code/{storeCode}:
 *   get:
 *     summary: 根据门店编码获取门店信息
 *     tags: [RecycleStores]
 *     parameters:
 *       - in: path
 *         name: storeCode
 *         required: true
 *         schema:
 *           type: string
 *         description: 门店编码
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/RecycleStore'
 *       404:
 *         description: 门店不存在
 */
router.get('/code/:storeCode', RecycleStoreController.getStoreByCode);

/**
 * @swagger
 * /api/recycle-stores/{id}:
 *   get:
 *     summary: 获取门店详情
 *     tags: [RecycleStores]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 门店ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/RecycleStore'
 *       404:
 *         description: 门店不存在
 */
router.get('/:id', RecycleStoreController.getStore);

/**
 * @swagger
 * /api/recycle-stores/{id}:
 *   put:
 *     summary: 更新门店信息
 *     tags: [RecycleStores]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 门店ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRecycleStoreDto'
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/RecycleStore'
 *       404:
 *         description: 门店不存在
 */
router.put('/:id', authenticate, authorize(UserRole.ADMIN, UserRole.SUPER_ADMIN), RecycleStoreController.updateStore);

/**
 * @swagger
 * /api/recycle-stores/{id}:
 *   delete:
 *     summary: 删除门店
 *     tags: [RecycleStores]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 门店ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       404:
 *         description: 门店不存在
 */
router.delete('/:id', authenticate, authorize(UserRole.ADMIN, UserRole.SUPER_ADMIN), RecycleStoreController.deleteStore);

export default router;
