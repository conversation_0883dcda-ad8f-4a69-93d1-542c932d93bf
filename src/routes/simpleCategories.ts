import { Router } from 'express';
import { CategoryControllerV2 } from '../controllers/categoryControllerV2';
import { authenticate } from '../middleware/auth';

const router = Router();

/**
 * @swagger
 * /api/v1/simple-categories/tree:
 *   get:
 *     summary: Get category tree structure
 *     tags: [Simple Categories]
 *     responses:
 *       200:
 *         description: Category tree retrieved succezssfully
 */
router.get('/tree', CategoryControllerV2.getCategoryTree);

/**
 * @swagger
 * /api/v1/simple-categories/brands:
 *   get:
 *     summary: Get all brands
 *     tags: [Simple Categories]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by brand name
 *       - in: query
 *         name: parent_id
 *         schema:
 *           type: integer
 *         description: Filter by parent category ID
 *     responses:
 *       200:
 *         description: Brands retrieved successfully
 */
router.get('/brands', CategoryControllerV2.getBrands);

/**
 * @swagger
 * /api/v1/simple-categories/brands/{id}/delete:
 *   put:
 *     summary: Soft delete brand (假删除)
 *     tags: [Simple Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Brand soft deleted successfully
 */
router.put('/brands/:id/delete', authenticate, CategoryControllerV2.softDeleteBrand);

/**
 * @swagger
 * /api/v1/simple-categories/brands/{brandId}/models:
 *   get:
 *     summary: Get models by brand ID
 *     tags: [Simple Categories]
 *     parameters:
 *       - in: path
 *         name: brandId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Models retrieved successfully
 */
router.get('/brands/:brandId/models', CategoryControllerV2.getModelsByBrand);

/**
 * @swagger
 * /api/v1/simple-categories/models/{id}/delete:
 *   put:
 *     summary: Soft delete model (假删除)
 *     tags: [Simple Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Model soft deleted successfully
 */
router.put('/models/:id/delete', authenticate, CategoryControllerV2.softDeleteModel);

/**
 * @swagger
 * /api/v1/simple-categories/models/{modelId}/sub-models:
 *   get:
 *     summary: Get sub-models by model ID
 *     tags: [Simple Categories]
 *     parameters:
 *       - in: path
 *         name: modelId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Sub-models retrieved successfully
 */
router.get('/models/:modelId/sub-models', CategoryControllerV2.getSubModelsByModel);

/**
 * @swagger
 * /api/v1/simple-categories/sub-models/{id}/delete:
 *   put:
 *     summary: Soft delete sub-model (假删除)
 *     tags: [Simple Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Sub-model soft deleted successfully
 */
router.put('/sub-models/:id/delete', authenticate, CategoryControllerV2.softDeleteSubModel);

/**
 * @swagger
 * /api/v1/simple-categories/{table}/{id}:
 *   get:
 *     summary: Get category details
 *     tags: [Simple Categories]
 *     parameters:
 *       - in: path
 *         name: table
 *         required: true
 *         schema:
 *           type: string
 *           enum: [phone_brands, phone_models, phone_sub_models]
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Category details retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:table/:id', CategoryControllerV2.getCategoryDetails);

/**
 * @swagger
 * /api/v1/simple-categories/{table}/{id}/restore:
 *   put:
 *     summary: Restore soft deleted category
 *     tags: [Simple Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: table
 *         required: true
 *         schema:
 *           type: string
 *           enum: [phone_brands, phone_models, phone_sub_models]
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Category restored successfully
 *       400:
 *         description: Category is not deleted
 *       404:
 *         description: Category not found
 */
router.put('/:table/:id/restore', authenticate, CategoryControllerV2.restoreCategory);

export default router;
