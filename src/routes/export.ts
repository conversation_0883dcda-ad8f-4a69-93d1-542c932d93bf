import { Router } from 'express';
import { ExportController } from '../controllers/exportController';
import { validate } from '../middleware/validation';
import { authenticate } from '../middleware/auth';
import { z } from 'zod';

const router = Router();

// 邮件导出验证schema
const exportEmailSchema = z.object({
  email: z.string().email('请提供有效的邮箱地址'),
});

/**
 * @swagger
 * components:
 *   schemas:
 *     ExportEmailRequest:
 *       type: object
 *       required:
 *         - email
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: 接收Excel文件的邮箱地址
 *           example: <EMAIL>
 *     
 *     UserStats:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: 总用户数
 *         active:
 *           type: integer
 *           description: 激活用户数
 *         inactive:
 *           type: integer
 *           description: 未激活用户数
 *         withPhone:
 *           type: integer
 *           description: 有手机号的用户数
 *         withWechat:
 *           type: integer
 *           description: 绑定微信的用户数
 *         byRole:
 *           type: object
 *           description: 按角色统计
 *           additionalProperties:
 *             type: integer
 */

/**
 * @swagger
 * /api/v1/export/users:
 *   post:
 *     summary: 导出用户数据到Excel并发送邮件
 *     tags: [Export]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ExportEmailRequest'
 *     responses:
 *       200:
 *         description: 导出任务启动成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *                     email:
 *                       type: string
 *       400:
 *         description: 请求参数错误
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器内部错误
 */
router.post('/users', authenticate, validate({ body: exportEmailSchema }), ExportController.exportUsers);

/**
 * @swagger
 * /api/v1/export/users/stats:
 *   get:
 *     summary: 获取用户统计信息
 *     tags: [Export]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 用户统计信息获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/UserStats'
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器内部错误
 */
router.get('/users/stats', authenticate, ExportController.getUserStats);

/**
 * @swagger
 * /api/v1/export/users/download:
 *   get:
 *     summary: 下载用户数据Excel文件
 *     tags: [Export]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 下载信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器内部错误
 */
router.get('/users/download', authenticate, ExportController.downloadUsersExcel);

export default router;
