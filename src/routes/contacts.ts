import { Router } from 'express';
import { Contact<PERSON>ontroller } from '../controllers/contactController';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { 
  createContactSchema, 
  updateContactSchema, 
  contactQuerySchema,
  contactInteractionSchema,
  createShareConfigSchema,
  updateShareConfigSchema
} from '../utils/validation';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Contact:
 *       type: object
 *       required:
 *         - id
 *         - userId
 *         - name
 *         - phone
 *       properties:
 *         id:
 *           type: integer
 *           description: Contact ID
 *         userId:
 *           type: string
 *           description: User ID who owns this contact
 *         name:
 *           type: string
 *           description: Contact name
 *         phone:
 *           type: string
 *           description: Phone number
 *         wechatId:
 *           type: string
 *           description: WeChat ID
 *         avatarUrl:
 *           type: string
 *           description: Avatar URL
 *         category:
 *           type: string
 *           enum: [FRIEND, FAMILY, COLLEAGUE, BUSINESS, OTHER]
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *         company:
 *           type: string
 *         position:
 *           type: string
 *         email:
 *           type: string
 *         address:
 *           type: string
 *         birthday:
 *           type: string
 *           format: date
 *         notes:
 *           type: string
 *         contactFrequency:
 *           type: string
 *           enum: [DAILY, WEEKLY, MONTHLY, RARELY, NEVER]
 *         importanceLevel:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *         isFavorite:
 *           type: boolean
 *         lastContactAt:
 *           type: string
 *           format: date-time
 *         lastContactType:
 *           type: string
 *           enum: [CALL, MESSAGE, WECHAT, EMAIL, MEETING, OTHER]
 *         contactCount:
 *           type: integer
 *         isPublic:
 *           type: boolean
 *         sharePhone:
 *           type: boolean
 *         shareWechat:
 *           type: boolean
 *         status:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     
 *     ShareConfig:
 *       type: object
 *       required:
 *         - id
 *         - userId
 *         - contactId
 *         - shareType
 *       properties:
 *         id:
 *           type: integer
 *         userId:
 *           type: string
 *         contactId:
 *           type: integer
 *         shareType:
 *           type: string
 *           enum: [QR_CODE, LINK, CARD, MINI_PROGRAM]
 *         shareTitle:
 *           type: string
 *         shareDescription:
 *           type: string
 *         shareImageUrl:
 *           type: string
 *         shareScope:
 *           type: string
 *           enum: [PUBLIC, FRIENDS, PRIVATE]
 *         allowedUsers:
 *           type: array
 *           items:
 *             type: string
 *         includePhone:
 *           type: boolean
 *         includeWechat:
 *           type: boolean
 *         includeEmail:
 *           type: boolean
 *         includeCompany:
 *           type: boolean
 *         includeAddress:
 *           type: boolean
 *         includeNotes:
 *           type: boolean
 *         shareCount:
 *           type: integer
 *         viewCount:
 *           type: integer
 *         lastSharedAt:
 *           type: string
 *           format: date-time
 *         lastViewedAt:
 *           type: string
 *           format: date-time
 *         expiresAt:
 *           type: string
 *           format: date-time
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /api/v1/contacts:
 *   post:
 *     summary: Create a new contact
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - phone
 *             properties:
 *               name:
 *                 type: string
 *                 example: "张三"
 *               phone:
 *                 type: string
 *                 example: "***********"
 *               wechatId:
 *                 type: string
 *                 example: "zhangsan_wx"
 *               avatarUrl:
 *                 type: string
 *                 example: "https://example.com/avatar.jpg"
 *               category:
 *                 type: string
 *                 enum: [FRIEND, FAMILY, COLLEAGUE, BUSINESS, OTHER]
 *                 example: "FRIEND"
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["朋友", "同事"]
 *               company:
 *                 type: string
 *                 example: "ABC公司"
 *               position:
 *                 type: string
 *                 example: "产品经理"
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *               address:
 *                 type: string
 *                 example: "北京市朝阳区"
 *               birthday:
 *                 type: string
 *                 format: date
 *                 example: "1990-01-01"
 *               notes:
 *                 type: string
 *                 example: "重要客户"
 *               contactFrequency:
 *                 type: string
 *                 enum: [DAILY, WEEKLY, MONTHLY, RARELY, NEVER]
 *                 example: "WEEKLY"
 *               importanceLevel:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 example: 4
 *               isFavorite:
 *                 type: boolean
 *                 example: false
 *               isPublic:
 *                 type: boolean
 *                 example: false
 *               sharePhone:
 *                 type: boolean
 *                 example: true
 *               shareWechat:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       201:
 *         description: Contact created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Contact'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       409:
 *         description: Contact already exists
 */
router.post('/', authenticate, validate({ body: createContactSchema }), ContactController.createContact);

/**
 * @swagger
 * /api/v1/contacts:
 *   get:
 *     summary: Get contacts with filtering and pagination
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, phone, wechat, company
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [FRIEND, FAMILY, COLLEAGUE, BUSINESS, OTHER]
 *         description: Filter by category
 *       - in: query
 *         name: isFavorite
 *         schema:
 *           type: boolean
 *         description: Filter by favorite status
 *       - in: query
 *         name: importanceLevel
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *         description: Filter by importance level
 *       - in: query
 *         name: isPublic
 *         schema:
 *           type: boolean
 *         description: Filter by public status
 *       - in: query
 *         name: tags
 *         schema:
 *           type: string
 *         description: Comma-separated tags to filter by
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, createdAt, lastContactAt, importanceLevel, contactCount]
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Contacts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     contacts:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Contact'
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticate, validate({ query: contactQuerySchema }), ContactController.getContacts);

/**
 * @swagger
 * /api/v1/contacts/stats:
 *   get:
 *     summary: Get contact statistics
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Contact statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalContacts:
 *                       type: integer
 *                     favoriteContacts:
 *                       type: integer
 *                     publicContacts:
 *                       type: integer
 *                     categoryCounts:
 *                       type: object
 *                     importanceLevelCounts:
 *                       type: object
 *                     recentContacts:
 *                       type: integer
 *                     topTags:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           tag:
 *                             type: string
 *                           count:
 *                             type: integer
 *       401:
 *         description: Unauthorized
 */
router.get('/stats', authenticate, ContactController.getContactStats);

/**
 * @swagger
 * /api/v1/contacts/public:
 *   get:
 *     summary: Search public contacts
 *     tags: [Contacts]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [FRIEND, FAMILY, COLLEAGUE, BUSINESS, OTHER]
 *         description: Filter by category
 *       - in: query
 *         name: tags
 *         schema:
 *           type: string
 *         description: Comma-separated tags to filter by
 *     responses:
 *       200:
 *         description: Public contacts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     contacts:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Contact'
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 */
router.get('/public', validate({ query: contactQuerySchema }), ContactController.searchPublicContacts);

/**
 * @swagger
 * /api/v1/contacts/category/{category}:
 *   get:
 *     summary: Get contacts by category
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: [FRIEND, FAMILY, COLLEAGUE, BUSINESS, OTHER]
 *         description: Contact category
 *     responses:
 *       200:
 *         description: Contacts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Contact'
 *       401:
 *         description: Unauthorized
 */
router.get('/category/:category', authenticate, ContactController.getContactsByCategory);

/**
 * @swagger
 * /api/v1/contacts/{id}:
 *   get:
 *     summary: Get contact by ID
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     responses:
 *       200:
 *         description: Contact retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Contact'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contact not found
 */
router.get('/:id', authenticate, ContactController.getContactById);

/**
 * @swagger
 * /api/v1/contacts/{id}:
 *   put:
 *     summary: Update contact
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               phone:
 *                 type: string
 *               wechatId:
 *                 type: string
 *               avatarUrl:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [FRIEND, FAMILY, COLLEAGUE, BUSINESS, OTHER]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               company:
 *                 type: string
 *               position:
 *                 type: string
 *               email:
 *                 type: string
 *               address:
 *                 type: string
 *               birthday:
 *                 type: string
 *                 format: date
 *               notes:
 *                 type: string
 *               contactFrequency:
 *                 type: string
 *                 enum: [DAILY, WEEKLY, MONTHLY, RARELY, NEVER]
 *               importanceLevel:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *               isFavorite:
 *                 type: boolean
 *               isPublic:
 *                 type: boolean
 *               sharePhone:
 *                 type: boolean
 *               shareWechat:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Contact updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Contact'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contact not found
 */
router.put('/:id', authenticate, validate({ body: updateContactSchema }), ContactController.updateContact);

/**
 * @swagger
 * /api/v1/contacts/{id}:
 *   delete:
 *     summary: Delete contact
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     responses:
 *       200:
 *         description: Contact deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contact not found
 */
router.delete('/:id', authenticate, ContactController.deleteContact);

/**
 * @swagger
 * /api/v1/contacts/{id}/interaction:
 *   post:
 *     summary: Record contact interaction
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contactType
 *             properties:
 *               contactType:
 *                 type: string
 *                 enum: [CALL, MESSAGE, WECHAT, EMAIL, MEETING, OTHER]
 *                 example: "CALL"
 *     responses:
 *       200:
 *         description: Contact interaction recorded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Contact'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contact not found
 */
router.post('/:id/interaction', authenticate, validate({ body: contactInteractionSchema }), ContactController.recordInteraction);

/**
 * @swagger
 * /api/v1/contacts/{id}/favorite:
 *   post:
 *     summary: Toggle favorite status
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     responses:
 *       200:
 *         description: Favorite status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Contact'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contact not found
 */
router.post('/:id/favorite', authenticate, ContactController.toggleFavorite);

// Share Config Routes

/**
 * @swagger
 * /api/v1/contacts/{id}/share:
 *   post:
 *     summary: Create share config for contact
 *     tags: [Contacts, Share]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - shareType
 *             properties:
 *               shareType:
 *                 type: string
 *                 enum: [QR_CODE, LINK, CARD, MINI_PROGRAM]
 *                 example: "QR_CODE"
 *               shareTitle:
 *                 type: string
 *                 example: "我的名片"
 *               shareDescription:
 *                 type: string
 *                 example: "这是我的联系方式"
 *               shareImageUrl:
 *                 type: string
 *                 example: "https://example.com/image.jpg"
 *               shareScope:
 *                 type: string
 *                 enum: [PUBLIC, FRIENDS, PRIVATE]
 *                 example: "PRIVATE"
 *               allowedUsers:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["user1", "user2"]
 *               includePhone:
 *                 type: boolean
 *                 example: true
 *               includeWechat:
 *                 type: boolean
 *                 example: true
 *               includeEmail:
 *                 type: boolean
 *                 example: false
 *               includeCompany:
 *                 type: boolean
 *                 example: false
 *               includeAddress:
 *                 type: boolean
 *                 example: false
 *               includeNotes:
 *                 type: boolean
 *                 example: false
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-12-31T23:59:59Z"
 *     responses:
 *       201:
 *         description: Share config created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/ShareConfig'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contact not found
 *       409:
 *         description: Share config already exists
 */
router.post('/:id/share', authenticate, validate({ body: createShareConfigSchema }), ContactController.createShareConfig);

/**
 * @swagger
 * /api/v1/contacts/{id}/share:
 *   get:
 *     summary: Get share configs for contact
 *     tags: [Contacts, Share]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Contact ID
 *     responses:
 *       200:
 *         description: Share configs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ShareConfig'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contact not found
 */
router.get('/:id/share', authenticate, ContactController.getShareConfigs);

/**
 * @swagger
 * /api/v1/contacts/share/{configId}:
 *   put:
 *     summary: Update share config
 *     tags: [Contacts, Share]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Share config ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               shareTitle:
 *                 type: string
 *               shareDescription:
 *                 type: string
 *               shareImageUrl:
 *                 type: string
 *               shareScope:
 *                 type: string
 *                 enum: [PUBLIC, FRIENDS, PRIVATE]
 *               allowedUsers:
 *                 type: array
 *                 items:
 *                   type: string
 *               includePhone:
 *                 type: boolean
 *               includeWechat:
 *                 type: boolean
 *               includeEmail:
 *                 type: boolean
 *               includeCompany:
 *                 type: boolean
 *               includeAddress:
 *                 type: boolean
 *               includeNotes:
 *                 type: boolean
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Share config updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/ShareConfig'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Share config not found
 */
router.put('/share/:configId', authenticate, validate({ body: updateShareConfigSchema }), ContactController.updateShareConfig);

/**
 * @swagger
 * /api/v1/contacts/share/{configId}:
 *   delete:
 *     summary: Delete share config
 *     tags: [Contacts, Share]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Share config ID
 *     responses:
 *       200:
 *         description: Share config deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Share config not found
 */
router.delete('/share/:configId', authenticate, ContactController.deleteShareConfig);

/**
 * @swagger
 * /api/v1/contacts/share/{configId}/link:
 *   post:
 *     summary: Generate share link
 *     tags: [Contacts, Share]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Share config ID
 *     responses:
 *       200:
 *         description: Share link generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     shareUrl:
 *                       type: string
 *                       example: "https://your-app.com/share/contact/123"
 *                     qrCodeUrl:
 *                       type: string
 *                       example: "https://your-app.com/api/qr?url=..."
 *       400:
 *         description: Share config expired or invalid
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Share config not found
 */
router.post('/share/:configId/link', authenticate, ContactController.generateShareLink);

/**
 * @swagger
 * /api/v1/contacts/share/{configId}/stats:
 *   get:
 *     summary: Get share statistics
 *     tags: [Contacts, Share]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Share config ID
 *     responses:
 *       200:
 *         description: Share statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     shareCount:
 *                       type: integer
 *                     viewCount:
 *                       type: integer
 *                     lastSharedAt:
 *                       type: string
 *                       format: date-time
 *                     lastViewedAt:
 *                       type: string
 *                       format: date-time
 *                     isActive:
 *                       type: boolean
 *                     expiresAt:
 *                       type: string
 *                       format: date-time
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Share config not found
 */
router.get('/share/:configId/stats', authenticate, ContactController.getShareStats);

// Public share access route (no authentication required)
/**
 * @swagger
 * /api/v1/contacts/shared/{configId}:
 *   get:
 *     summary: Access shared contact (public endpoint)
 *     tags: [Contacts, Share]
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Share config ID
 *     responses:
 *       200:
 *         description: Shared contact accessed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     contact:
 *                       type: object
 *                       description: Filtered contact data based on share config
 *                     shareConfig:
 *                       $ref: '#/components/schemas/ShareConfig'
 *       400:
 *         description: Share link expired
 *       403:
 *         description: Access denied
 *       404:
 *         description: Share config not found
 */
router.get('/shared/:configId', ContactController.accessSharedContact);

// Batch operations
/**
 * @swagger
 * /api/v1/contacts/batch:
 *   put:
 *     summary: Batch update contacts
 *     tags: [Contacts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contactIds
 *               - updateData
 *             properties:
 *               contactIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3]
 *               updateData:
 *                 type: object
 *                 properties:
 *                   category:
 *                     type: string
 *                     enum: [FRIEND, FAMILY, COLLEAGUE, BUSINESS, OTHER]
 *                   isFavorite:
 *                     type: boolean
 *                   isPublic:
 *                     type: boolean
 *                   tags:
 *                     type: array
 *                     items:
 *                       type: string
 *     responses:
 *       200:
 *         description: Contacts updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Contact'
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.put('/batch', authenticate, ContactController.batchUpdateContacts);

export default router;
