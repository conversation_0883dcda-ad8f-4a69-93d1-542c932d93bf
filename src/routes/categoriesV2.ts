import { Router } from 'express';
import { CategoryControllerV2 } from '../controllers/categoryControllerV2';
import { authenticate } from '../middleware/auth';

const router = Router();

/**
 * @swagger
 * /api/v1/categories/tree:
 *   get:
 *     summary: Get category tree structure
 *     tags: [Categories V2]
 *     responses:
 *       200:
 *         description: Category tree retrieved successfully
 */
router.get('/tree', CategoryControllerV2.getCategoryTree);

/**
 * @swagger
 * /api/v1/categories/brands:
 *   get:
 *     summary: Get all brands
 *     tags: [Categories V2]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by brand name
 *       - in: query
 *         name: parent_id
 *         schema:
 *           type: integer
 *         description: Filter by parent category ID
 *     responses:
 *       200:
 *         description: Brands retrieved successfully
 */
router.get('/brands', CategoryControllerV2.getBrands);

/**
 * @swagger
 * /api/v1/categories/{categoryId}/brands:
 *   get:
 *     summary: Get brands tree by category ID (brands -> models -> sub-models)
 *     tags: [Categories V2]
 *     parameters:
 *       - in: path
 *         name: categoryId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Category ID
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by brand name
 *     responses:
 *       200:
 *         description: Brand tree structure retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           parent_id:
 *                             type: integer
 *                           sort_index:
 *                             type: integer
 *                           models:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                 name:
 *                                   type: string
 *                                 brand_id:
 *                                   type: integer
 *                                 sort_index:
 *                                   type: integer
 *                                 sub_models:
 *                                   type: array
 *                                   items:
 *                                     type: object
 *                                     properties:
 *                                       id:
 *                                         type: integer
 *                                       name:
 *                                         type: string
 *                                       model_id:
 *                                         type: integer
 *                                       sort_index:
 *                                         type: integer
 *                     total:
 *                       type: integer
 *       404:
 *         description: Category not found
 */
router.get('/:categoryId/brands', CategoryControllerV2.getBrandsByCategory);

/**
 * @swagger
 * /api/v1/categories:
 *   get:
 *     summary: Get all root categories
 *     tags: [Categories V2]
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 */
router.get('/', CategoryControllerV2.getCategories);

/**
 * @swagger
 * /api/v1/categories/brands/{id}/delete:
 *   put:
 *     summary: Soft delete brand (假删除)
 *     tags: [Categories V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Brand soft deleted successfully
 */
router.put('/brands/:id/delete', CategoryControllerV2.softDeleteBrand);

/**
 * @swagger
 * /api/v1/categories/brands/{brandId}/models:
 *   get:
 *     summary: Get models by brand ID
 *     tags: [Categories V2]
 *     parameters:
 *       - in: path
 *         name: brandId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Models retrieved successfully
 */
router.get('/brands/:brandId/models', CategoryControllerV2.getModelsByBrand);

/**
 * @swagger
 * /api/v1/categories/models/{id}/delete:
 *   put:
 *     summary: Soft delete model (假删除)
 *     tags: [Categories V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Model soft deleted successfully
 */
router.put('/models/:id/delete', authenticate, CategoryControllerV2.softDeleteModel);

/**
 * @swagger
 * /api/v1/categories/models/{modelId}/sub-models:
 *   get:
 *     summary: Get sub-models by model ID
 *     tags: [Categories V2]
 *     parameters:
 *       - in: path
 *         name: modelId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Sub-models retrieved successfully
 */
router.get('/models/:modelId/sub-models', CategoryControllerV2.getSubModelsByModel);

/**
 * @swagger
 * /api/v1/categories/sub-models/{id}/delete:
 *   put:
 *     summary: Soft delete sub-model (假删除)
 *     tags: [Categories V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Sub-model soft deleted successfully
 */
router.put('/sub-models/:id/delete', authenticate, CategoryControllerV2.softDeleteSubModel);

/**
 * @swagger
 * /api/v1/categories/search:
 *   get:
 *     summary: Search categories by name
 *     tags: [Categories V2 - Search]
 *     parameters:
 *       - in: query
 *         name: search
 *         required: true
 *         schema:
 *           type: string
 *         description: Search term for category name
 *       - in: query
 *         name: level
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: Filter by category level (1-品牌, 2-型号, 3-子型号)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Categories search completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           level:
 *                             type: integer
 *                           level_name:
 *                             type: string
 *                           parent:
 *                             type: object
 *                           brand:
 *                             type: object
 *                     pagination:
 *                       type: object
 *                     meta:
 *                       type: object
 *       400:
 *         description: Search parameter is required
 */
router.get('/search', CategoryControllerV2.searchCategories);

/**
 * @swagger
 * /api/v1/categories/model/{id}:
 *   get:
 *     summary: Get complete model details with memory specs and price tags
 *     tags: [Categories V2 - Model Details]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Category ID
 *     responses:
 *       200:
 *         description: Model details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     model:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         name:
 *                           type: string
 *                         level:
 *                           type: integer
 *                         wechat_tag:
 *                           type: object
 *                     brand:
 *                       type: object
 *                     classify:
 *                       type: object
 *                     sub_model:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           sub_model_id:
 *                             type: integer
 *                           tag_name:
 *                             type: string
 *                           price:
 *                             type: number
 *                     tags:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           sub_model_id:
 *                             type: integer
 *                           id:
 *                             type: integer
 *                           price_rate:
 *                             type: number
 *                           tag_name:
 *                             type: string
 *                           group_name:
 *                             type: string
 *       400:
 *         description: Invalid category ID
 *       404:
 *         description: Category not found
 */
router.get('/model/:id', CategoryControllerV2.getModelComplete);

/**
 * @swagger
 * /api/v1/categories/price-rankings:
 *   get:
 *     summary: Get price rankings (涨跌榜)
 *     description: Retrieve price rankings for categories showing price increases or decreases
 *     tags: [Categories V2 - Price Rankings]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *           minimum: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [RISE_RANKING, FALL_RANKING]
 *           default: RISE_RANKING
 *         description: Ranking type - RISE_RANKING for price increases, FALL_RANKING for price decreases
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *           example: "2025-06-19"
 *         description: Ranking date in YYYY-MM-DD format, defaults to today
 *       - in: query
 *         name: category_level
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: Filter by category level (1-品牌/Brand, 2-型号/Model, 3-子型号/Sub-model)
 *     responses:
 *       200:
 *         description: Price rankings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Price rankings retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           ranking_date:
 *                             type: string
 *                             format: date-time
 *                           category_id:
 *                             type: integer
 *                           category_level:
 *                             type: integer
 *                           category_name:
 *                             type: string
 *                           brand_name:
 *                             type: string
 *                           model_name:
 *                             type: string
 *                           sub_model_name:
 *                             type: string
 *                           memory_size:
 *                             type: string
 *                           tag_name:
 *                             type: string
 *                           group_name:
 *                             type: string
 *                           current_price:
 *                             type: string
 *                           previous_price:
 *                             type: string
 *                           price_change:
 *                             type: string
 *                           change_percentage:
 *                             type: string
 *                           trend_type:
 *                             type: string
 *                             enum: [RISE, FALL, STABLE]
 *                           ranking_position:
 *                             type: integer
 *                           ranking_type:
 *                             type: string
 *                             enum: [RISE_RANKING, FALL_RANKING]
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                     meta:
 *                       type: object
 *                       properties:
 *                         ranking_type:
 *                           type: string
 *                         ranking_date:
 *                           type: string
 *                         category_level:
 *                           type: string
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid ranking type. Must be RISE_RANKING or FALL_RANKING"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/price-rankings', CategoryControllerV2.getPriceRankings);

/**
 * @swagger
 * /api/v1/categories/price-rankings/stats:
 *   get:
 *     summary: Get price ranking statistics
 *     description: Retrieve statistical analysis of price rankings including distribution and top movers
 *     tags: [Categories V2 - Price Rankings]
 *     parameters:
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *           example: "2025-06-19"
 *         description: Statistics date in YYYY-MM-DD format, defaults to today
 *     responses:
 *       200:
 *         description: Price ranking statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Price ranking statistics retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     statistics:
 *                       type: array
 *                       description: Overall statistics by ranking type
 *                       items:
 *                         type: object
 *                         properties:
 *                           ranking_type:
 *                             type: string
 *                             enum: [RISE_RANKING, FALL_RANKING]
 *                           total_count:
 *                             type: integer
 *                           avg_change_percentage:
 *                             type: number
 *                           max_change_percentage:
 *                             type: number
 *                           min_change_percentage:
 *                             type: number
 *                     levelDistribution:
 *                       type: array
 *                       description: Distribution by category level
 *                       items:
 *                         type: object
 *                         properties:
 *                           category_level:
 *                             type: integer
 *                           ranking_type:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     topRise:
 *                       type: array
 *                       description: Top 5 price increases
 *                       items:
 *                         type: object
 *                         properties:
 *                           category_name:
 *                             type: string
 *                           brand_name:
 *                             type: string
 *                           model_name:
 *                             type: string
 *                           sub_model_name:
 *                             type: string
 *                           change_percentage:
 *                             type: string
 *                           current_price:
 *                             type: string
 *                           previous_price:
 *                             type: string
 *                     topFall:
 *                       type: array
 *                       description: Top 5 price decreases
 *                       items:
 *                         type: object
 *                         properties:
 *                           category_name:
 *                             type: string
 *                           brand_name:
 *                             type: string
 *                           model_name:
 *                             type: string
 *                           sub_model_name:
 *                             type: string
 *                           change_percentage:
 *                             type: string
 *                           current_price:
 *                             type: string
 *                           previous_price:
 *                             type: string
 *                     meta:
 *                       type: object
 *                       properties:
 *                         date:
 *                           type: string
 *                         total_categories:
 *                           type: integer
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/price-rankings/stats', CategoryControllerV2.getPriceRankingStats);

/**
 * @swagger
 * /api/v1/categories/price-rankings/consecutive:
 *   get:
 *     summary: Get consecutive trend items (连续涨跌商品)
 *     description: Retrieve items with consecutive price rises or falls for 2+ days
 *     tags: [Categories V2 - Price Rankings]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: trend_type
 *         schema:
 *           type: string
 *           enum: [RISE, FALL]
 *         description: Filter by trend type (RISE for consecutive rises, FALL for consecutive falls)
 *       - in: query
 *         name: min_days
 *         schema:
 *           type: integer
 *           default: 2
 *         description: Minimum consecutive days (default 2)
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Target date (YYYY-MM-DD format, defaults to today)
 *     responses:
 *       200:
 *         description: Consecutive trends retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           category_name:
 *                             type: string
 *                           brand_name:
 *                             type: string
 *                           consecutive_rise_days:
 *                             type: integer
 *                           consecutive_fall_days:
 *                             type: integer
 *                           trend_signal:
 *                             type: string
 *                           change_percentage:
 *                             type: number
 *                           current_price:
 *                             type: number
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                     meta:
 *                       type: object
 *                       properties:
 *                         trend_type:
 *                           type: string
 *                         min_days:
 *                           type: integer
 *                         ranking_date:
 *                           type: string
 *                 message:
 *                   type: string
 *                   example: "Consecutive trends retrieved successfully"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid parameters"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Failed to retrieve consecutive trends"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/price-rankings/consecutive', CategoryControllerV2.getConsecutiveTrends);

/**
 * @swagger
 * /api/v1/categories/price-rankings/calculate:
 *   post:
 *     summary: Manually trigger price ranking calculation
 *     description: Manually trigger the calculation of price rankings. Requires authentication.
 *     tags: [Categories V2 - Price Rankings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Price rankings calculation completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Price rankings calculation completed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     processed:
 *                       type: integer
 *                       description: Number of records processed
 *                     created:
 *                       type: integer
 *                       description: Number of new rankings created
 *                     updated:
 *                       type: integer
 *                       description: Number of existing rankings updated
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Unauthorized - Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Access token is required"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       500:
 *         description: Internal server error during calculation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to calculate price rankings"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.post('/price-rankings/calculate', authenticate, CategoryControllerV2.calculatePriceRankings);

/**
 * @swagger
 * /api/v1/categories/{table}/{id}:
 *   get:
 *     summary: Get category details
 *     tags: [Categories V2]
 *     parameters:
 *       - in: path
 *         name: table
 *         required: true
 *         schema:
 *           type: string
 *           enum: [phone_brands, phone_models, phone_sub_models]
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Category details retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:table/:id', CategoryControllerV2.getCategoryDetails);

/**
 * @swagger
 * /api/v1/categories/{table}/{id}/restore:
 *   put:
 *     summary: Restore soft deleted category
 *     tags: [Categories V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: table
 *         required: true
 *         schema:
 *           type: string
 *           enum: [phone_brands, phone_models, phone_sub_models]
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Category restored successfully
 *       400:
 *         description: Category is not deleted
 *       404:
 *         description: Category not found
 */
router.put('/:table/:id/restore', CategoryControllerV2.restoreCategory);

/**
 * @swagger
 * /api/v1/categories/{table}/{id}/price:
 *   get:
 *     summary: Get price information for a category
 *     tags: [Categories V2 - Price]
 *     parameters:
 *       - in: path
 *         name: table
 *         required: true
 *         schema:
 *           type: string
 *           enum: [phone_brands, phone_models, phone_sub_models]
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Category price information retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:table/:id/price', CategoryControllerV2.getCategoryPrice);

/**
 * @swagger
 * /api/v1/categories/{table}/{id}/price-trend:
 *   get:
 *     summary: Get price trend analysis (涨跌结果)
 *     tags: [Categories V2 - Price]
 *     parameters:
 *       - in: path
 *         name: table
 *         required: true
 *         schema:
 *           type: string
 *           enum: [phone_brands, phone_models, phone_sub_models]
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to analyze (default 30)
 *     responses:
 *       200:
 *         description: Price trend analysis completed successfully
 *       404:
 *         description: Category not found
 */
router.get('/:table/:id/price-trend', CategoryControllerV2.getPriceTrend);

/**
 * @swagger
 * /api/v1/categories/sub-models/{subModelId}/complete:
 *   get:
 *     summary: Get complete model details with pricing information by sub-model ID
 *     tags: [Categories V2]
 *     parameters:
 *       - in: path
 *         name: subModelId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Sub-model ID
 *     responses:
 *       200:
 *         description: Complete model details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     model:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         parent_id:
 *                           type: integer
 *                         level:
 *                           type: integer
 *                         name:
 *                           type: string
 *                         sort_index:
 *                           type: integer
 *                         remake:
 *                           type: string
 *                         wechat_tag:
 *                           type: object
 *                     brand:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         parent_id:
 *                           type: integer
 *                         level:
 *                           type: integer
 *                         name:
 *                           type: string
 *                         sort_index:
 *                           type: integer
 *                         remake:
 *                           type: string
 *                     classify:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                         parent_id:
 *                           type: integer
 *                         level:
 *                           type: integer
 *                         name:
 *                           type: string
 *                         sort_index:
 *                           type: integer
 *                         remake:
 *                           type: string
 *                     sub_model:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           sub_model_id:
 *                             type: integer
 *                           price:
 *                             type: number
 *                           tag_name:
 *                             type: string
 *                     tags:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           sub_model_id:
 *                             type: integer
 *                           id:
 *                             type: integer
 *                           price_rate:
 *                             type: number
 *                           tag_name:
 *                             type: string
 *                           group_name:
 *                             type: string
 *       404:
 *         description: Sub-model not found
 */
router.get('/sub-models/:subModelId/complete', CategoryControllerV2.getCompleteModelDetails);

/**
 * @swagger
 * /api/v1/categories/sub-models/{subModelId}/memory-specs:
 *   get:
 *     summary: Get memory specs for a sub-model
 *     tags: [Categories V2 - Memory]
 *     parameters:
 *       - in: path
 *         name: subModelId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Sub-model ID
 *     responses:
 *       200:
 *         description: Memory specs retrieved successfully
 *       404:
 *         description: Sub-model not found
 */
router.get('/sub-models/:subModelId/memory-specs', CategoryControllerV2.getMemorySpecs);

/**
 * @swagger
 * /api/v1/categories/memory-specs/{memorySpecId}/price-tags:
 *   get:
 *     summary: Get price tags for a memory spec
 *     tags: [Categories V2 - Price]
 *     parameters:
 *       - in: path
 *         name: memorySpecId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Memory spec ID
 *     responses:
 *       200:
 *         description: Price tags retrieved successfully
 *       404:
 *         description: Memory spec not found
 */
router.get('/memory-specs/:memorySpecId/price-tags', CategoryControllerV2.getPriceTagsByMemorySpec);

/**
 * @swagger
 * /api/v1/categories/sync:
 *   post:
 *     summary: Sync categories from external API
 *     tags: [Categories V2 - Sync]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Sync operation completed
 */
router.post('/sync', CategoryControllerV2.syncCategories);

/**
 * @swagger
 * /api/v1/categories/sub-models/{subModelId}/memory-specs:
 *   get:
 *     summary: Get memory specs for a sub-model
 *     tags: [Categories V2 - Memory]
 *     parameters:
 *       - in: path
 *         name: subModelId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Sub-model ID
 *     responses:
 *       200:
 *         description: Memory specs retrieved successfully
 *       404:
 *         description: Sub-model not found
 */
router.get('/sub-models/:subModelId/memory-specs', CategoryControllerV2.getMemorySpecs);

/**
 * @swagger
 * /api/v1/categories/memory-specs/{memorySpecId}/price-tags:
 *   get:
 *     summary: Get price tags for a memory spec
 *     tags: [Categories V2 - Price]
 *     parameters:
 *       - in: path
 *         name: memorySpecId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Memory spec ID
 *     responses:
 *       200:
 *         description: Price tags retrieved successfully
 *       404:
 *         description: Memory spec not found
 */
router.get('/memory-specs/:memorySpecId/price-tags', CategoryControllerV2.getPriceTagsByMemorySpec);

/**
 * @swagger
 * /api/v1/categories/sync:
 *   post:
 *     summary: Sync categories from external API
 *     tags: [Categories V2 - Sync]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Sync operation completed
 */
router.post('/sync', CategoryControllerV2.syncCategories);



// Debug route
router.get('/debug/:subModelId', CategoryControllerV2.debugSubModelData);

export default router;
