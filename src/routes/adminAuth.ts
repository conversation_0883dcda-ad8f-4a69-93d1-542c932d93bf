import { Router } from 'express';
import { AdminAuthController } from '../controllers/adminAuthController';
import { validate } from '../middleware/validation';
import { authenticate } from '../middleware/auth';
import { adminLoginSchema, adminCreateSchema, changePasswordSchema } from '../utils/validation';

const router = Router();

// Test endpoint to verify admin routes are working
router.get('/test', (req, res) => {
  res.json({ success: true, message: 'Admin auth routes are working!' });
});

/**
 * @swagger
 * /api/v1/admin/auth/login:
 *   post:
 *     summary: Admin login
 *     tags: [Admin Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *             oneOf:
 *               - required: [email]
 *               - required: [username]
 *     responses:
 *       200:
 *         description: Admin login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/Admin'
 *                     tokens:
 *                       $ref: '#/components/schemas/AuthTokens'
 *       401:
 *         description: Invalid credentials or account disabled
 */
router.post('/login', AdminAuthController.login);

/**
 * @swagger
 * /api/v1/admin/auth/profile:
 *   get:
 *     summary: Get current admin profile
 *     tags: [Admin Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Admin profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Admin'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Admin not found
 */
router.get('/profile', authenticate, AdminAuthController.getProfile);

/**
 * @swagger
 * /api/v1/admin/auth/user:
 *   get:
 *     summary: Get current logged-in admin user info (alias for profile)
 *     tags: [Admin Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Admin user info retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Admin'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Admin not found
 */
router.get('/user', authenticate, AdminAuthController.getProfile);

/**
 * @swagger
 * /api/v1/admin/auth/profile:
 *   put:
 *     summary: Update current admin profile
 *     tags: [Admin Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *     responses:
 *       200:
 *         description: Admin profile updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Admin not found
 *       409:
 *         description: Email or username already exists
 */
router.put('/profile', authenticate, AdminAuthController.updateProfile);

/**
 * @swagger
 * /api/v1/admin/auth/change-password:
 *   post:
 *     summary: Change admin password
 *     tags: [Admin Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: Password changed successfully
 *       400:
 *         description: Invalid current password or validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Admin not found
 */
router.post('/change-password', authenticate, validate({ body: changePasswordSchema }), AdminAuthController.changePassword);

/**
 * @swagger
 * /api/v1/admin/auth/logout:
 *   post:
 *     summary: Admin logout
 *     tags: [Admin Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Admin logout successful
 *       401:
 *         description: Unauthorized
 */
router.post('/logout', authenticate, AdminAuthController.logout);

/**
 * @swagger
 * /api/v1/admin/auth/create:
 *   post:
 *     summary: Create new admin (super admin only)
 *     tags: [Admin Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - username
 *               - role
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               username:
 *                 type: string
 *               password:
 *                 type: string
 *                 minLength: 8
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [admin, moderator, super_admin]
 *               phone:
 *                 type: string
 *     responses:
 *       201:
 *         description: Admin created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       409:
 *         description: Admin already exists
 */
router.post('/create', authenticate, validate({ body: adminCreateSchema }), AdminAuthController.createAdmin);

export default router;
