# 🛣️ 路由配置详细文档

## 📋 **模块概述**

路由层负责定义API端点，配置中间件，并将请求分发到对应的控制器。所有路由都包含完整的Swagger文档注释。

## 📁 **文件结构**

```
src/routes/
├── README.md              # 本文档
├── index.ts              # 主路由配置
├── auth.ts               # C端用户认证路由
├── users.ts              # C端用户管理路由
├── adminAuth.ts          # B端管理员认证路由
├── categoriesV2.ts       # 类目管理路由V2
└── simpleCategories.ts   # 简单类目路由(未启用)
```

---

## 🏠 **主路由配置**
**文件**: `index.ts`

### 📋 **路由注册**

```typescript
// 健康检查
router.get('/health', healthCheckHandler);

// 通用用户信息接口
router.get('/auth/user', AuthController.getCurrentUser);

// 模块路由
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/admin/auth', adminAuthRoutes);
router.use('/categories', categoriesV2Routes);
```

### 🏥 **系统健康检查**

| 方法 | 路径 | 功能 | 认证 | 描述 |
|------|------|------|------|------|
| GET | `/health` | 系统健康检查 | ❌ | 返回系统状态信息 |

**响应示例**:
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "status": "OK",
    "timestamp": "2025-06-16T10:30:00.000Z",
    "uptime": 3600,
    "environment": "development",
    "version": "1.0.0"
  }
}
```

---

## 👤 **C端用户认证路由**
**文件**: `auth.ts`  
**路由前缀**: `/api/v1/auth`

### 📋 **路由配置**

| 方法 | 路径 | 控制器方法 | 中间件 | 描述 |
|------|------|-----------|--------|------|
| POST | `/register` | `AuthController.register` | `validate` | 用户注册 |
| POST | `/login` | `AuthController.login` | `validate` | 用户登录 |
| POST | `/refresh` | `AuthController.refreshToken` | - | 刷新Token |
| POST | `/logout` | `AuthController.logout` | `authenticate` | 用户登出 |
| GET | `/profile` | `AuthController.getProfile` | `authenticate` | 获取个人资料 |
| PUT | `/profile` | `AuthController.updateProfile` | `authenticate`, `validate` | 更新个人资料 |

### 🔧 **中间件配置**

#### 参数验证中间件
```typescript
// 注册验证
router.post('/register', validate({ body: createUserSchema }), AuthController.register);

// 登录验证
router.post('/login', validate({ body: loginSchema }), AuthController.login);

// 更新资料验证
router.put('/profile', authenticate, validate({ body: updateUserSchema }), AuthController.updateProfile);
```

#### 认证中间件
```typescript
// 需要认证的路由
router.post('/logout', authenticate, AuthController.logout);
router.get('/profile', authenticate, AuthController.getProfile);
```

### 📝 **Swagger文档**

每个路由都包含完整的Swagger注释：

```typescript
/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
```

---

## 👥 **C端用户管理路由**
**文件**: `users.ts`  
**路由前缀**: `/api/v1/users`

### 📋 **路由配置**

| 方法 | 路径 | 控制器方法 | 中间件 | 权限 | 描述 |
|------|------|-----------|--------|------|------|
| GET | `/` | `UserController.getUsers` | `authenticate`, `authorize`, `validate` | Admin/Moderator | 获取用户列表 |
| GET | `/:id` | `UserController.getUserById` | `authenticate`, `authorize`, `validate` | Admin/Moderator | 获取用户详情 |
| POST | `/` | `UserController.createUser` | `authenticate`, `authorize`, `validate` | Admin | 创建用户 |
| PUT | `/:id` | `UserController.updateUser` | `authenticate`, `authorize`, `validate` | Admin | 更新用户 |
| DELETE | `/:id` | `UserController.deleteUser` | `authenticate`, `authorize`, `validate` | Admin | 删除用户 |
| GET | `/profile` | `UserController.getProfile` | `authenticate` | User | 获取个人资料 |
| PUT | `/profile` | `UserController.updateProfile` | `authenticate`, `validate` | User | 更新个人资料 |

### 🔧 **权限控制**

#### 管理员权限
```typescript
// 需要管理员权限
router.get('/', 
  authenticate, 
  authorize(UserRole.ADMIN, UserRole.MODERATOR), 
  validate({ query: userQuerySchema }), 
  UserController.getUsers
);

// 仅超级管理员
router.post('/', 
  authenticate, 
  authorize(UserRole.ADMIN), 
  validate({ body: createUserSchema }), 
  UserController.createUser
);
```

#### 用户权限
```typescript
// 普通用户权限
router.get('/profile', authenticate, UserController.getProfile);
router.put('/profile', authenticate, validate({ body: updateUserSchema }), UserController.updateProfile);
```

---

## 🔐 **B端管理员认证路由**
**文件**: `adminAuth.ts`  
**路由前缀**: `/api/v1/admin/auth`

### 📋 **路由配置**

| 方法 | 路径 | 控制器方法 | 中间件 | 权限 | 描述 |
|------|------|-----------|--------|------|------|
| GET | `/test` | 测试处理器 | - | - | 测试管理员路由 |
| POST | `/login` | `AdminAuthController.login` | - | - | 管理员登录 |
| GET | `/profile` | `AdminAuthController.getProfile` | `authenticate` | Admin | 获取管理员资料 |
| PUT | `/profile` | `AdminAuthController.updateProfile` | `authenticate`, `validate` | Admin | 更新管理员资料 |
| POST | `/change-password` | `AdminAuthController.changePassword` | `authenticate`, `validate` | Admin | 修改密码 |
| POST | `/logout` | `AdminAuthController.logout` | `authenticate` | Admin | 管理员登出 |
| POST | `/create-admin` | `AdminAuthController.createAdmin` | `authenticate`, `validate` | Super Admin | 创建管理员 |

### 🔧 **特殊配置**

#### 测试路由
```typescript
// 测试端点，验证管理员路由是否正常工作
router.get('/test', (req, res) => {
  res.json({ success: true, message: 'Admin auth routes are working!' });
});
```

#### 管理员登录
```typescript
// 管理员登录不需要验证中间件，但有特殊的登录逻辑
router.post('/login', AdminAuthController.login);
```

---

## 📱 **类目管理路由V2**
**文件**: `categoriesV2.ts`  
**路由前缀**: `/api/v1/categories`

### 📋 **路由配置**

#### 基础类目路由
| 方法 | 路径 | 控制器方法 | 认证 | 描述 |
|------|------|-----------|------|------|
| GET | `/` | `CategoryControllerV2.getCategories` | ❌ | 获取根类目 |
| GET | `/tree` | `CategoryControllerV2.getCategoryTree` | ❌ | 获取类目树 |

#### 品牌管理路由
| 方法 | 路径 | 控制器方法 | 认证 | 描述 |
|------|------|-----------|------|------|
| GET | `/brands` | `CategoryControllerV2.getBrands` | ❌ | 获取所有品牌 |
| GET | `/:categoryId/brands` | `CategoryControllerV2.getBrandsByCategory` | ❌ | 根据类目获取品牌 |
| PUT | `/brands/:id/delete` | `CategoryControllerV2.softDeleteBrand` | ❌ | 软删除品牌 |
| GET | `/brands/:brandId/models` | `CategoryControllerV2.getModelsByBrand` | ❌ | 获取品牌型号 |

#### 型号管理路由
| 方法 | 路径 | 控制器方法 | 认证 | 描述 |
|------|------|-----------|------|------|
| PUT | `/models/:id/delete` | `CategoryControllerV2.softDeleteModel` | ✅ | 软删除型号 |
| GET | `/models/:modelId/sub-models` | `CategoryControllerV2.getSubModelsByModel` | ❌ | 获取子型号 |
| PUT | `/sub-models/:id/delete` | `CategoryControllerV2.softDeleteSubModel` | ✅ | 软删除子型号 |

#### 通用管理路由
| 方法 | 路径 | 控制器方法 | 认证 | 描述 |
|------|------|-----------|------|------|
| GET | `/:table/:id` | `CategoryControllerV2.getCategoryDetails` | ❌ | 获取类目详情 |
| PUT | `/:table/:id/restore` | `CategoryControllerV2.restoreCategory` | ✅ | 恢复类目 |

#### 价格功能路由
| 方法 | 路径 | 控制器方法 | 认证 | 描述 |
|------|------|-----------|------|------|
| GET | `/:table/:id/price` | `CategoryControllerV2.getCategoryPrice` | ❌ | 获取价格信息 |
| GET | `/:table/:id/price-trend` | `CategoryControllerV2.getPriceTrend` | ❌ | 获取价格趋势 |

### 🔧 **路由顺序优化**

为避免路由冲突，按照以下顺序定义：

```typescript
// 1. 具体路径在前
router.get('/tree', CategoryControllerV2.getCategoryTree);
router.get('/brands', CategoryControllerV2.getBrands);

// 2. 带参数的路径
router.get('/:categoryId/brands', CategoryControllerV2.getBrandsByCategory);

// 3. 通用路径在最后
router.get('/', CategoryControllerV2.getCategories);
```

---

## 🚫 **未启用路由**
**文件**: `simpleCategories.ts`

此文件包含简化版的类目管理路由，但**未在主路由中注册**，因此当前不可用。

如需启用，需要在 `index.ts` 中添加：
```typescript
import simpleCategoriesRoutes from './simpleCategories';
router.use('/simple-categories', simpleCategoriesRoutes);
```

---

## 🔧 **通用特性**

### 中间件链
```typescript
// 典型的中间件链
router.post('/', 
  authenticate,           // 身份认证
  authorize(UserRole.ADMIN), // 权限验证
  validate({ body: schema }), // 参数验证
  Controller.method       // 控制器方法
);
```

### Swagger标签
- `Authentication`: C端用户认证
- `Users`: C端用户管理
- `Admin Auth`: B端管理员认证
- `Categories V2`: 类目管理V2
- `Categories V2 - Price`: 价格功能

### 错误处理
所有路由都通过全局错误处理中间件统一处理错误。

---

📝 **文档最后更新**: 2025-06-16  
🔧 **维护者**: Baofeng R&D Team
