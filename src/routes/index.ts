import { Router } from 'express';
import authRoutes from './auth';
import userRoutes from './users';
import adminAuthRoutes from './adminAuth';
import categoriesV2Routes from './categoriesV2';
import wechatRoutes from './wechat';
import contactRoutes from './contacts';
import exportRoutes from './export';
import recycleStoreRoutes from './recycleStoreRoutes';
import miniprogramConfigRoutes from './miniprogramConfigRoutes';
import { sendSuccess } from '../utils/response';
import { AuthController } from '../controllers/authController';

const router = Router();

// Health check endpoint
router.get('/health', (req, res) => {
  sendSuccess(res, {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  }, 'Service is healthy');
});

// API routes
// 注意：具体的路由必须在通用路由之前定义
// 通用用户信息接口 (支持管理员和普通用户)
router.get('/auth/user', AuthController.getCurrentUser);
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/admin/auth', adminAuthRoutes);
router.use('/categories', categoriesV2Routes);
router.use('/wechat', wechatRoutes);
router.use('/contacts', contactRoutes);
router.use('/export', exportRoutes);
router.use('/recycle-stores', recycleStoreRoutes);
router.use('/miniprogram/configs', miniprogramConfigRoutes);

export default router;
