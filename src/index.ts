import { createApp } from './app';
import config from './config';
import logger from './config/logger';
import { initializeDatabase } from './config/database';
import { initializeAdminDatabase } from './config/adminDatabase';

/**
 * Start the server
 */
const startServer = async (): Promise<void> => {
  try {
    // Initialize databases
    await initializeDatabase();
    await initializeAdminDatabase();

    const app = createApp();

    const server = app.listen(config.port, config.host, () => {
      logger.info(`🚀 Server is running on http://${config.host}:${config.port}`);
      logger.info(`📚 API Documentation available at http://${config.host}:${config.port}/api-docs`);
      logger.info(`🏥 Health check available at http://${config.host}:${config.port}/health`);
      logger.info(`🌍 Environment: ${config.env}`);
      logger.info(`📝 Log level: ${config.logging.level}`);
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      server.close((err) => {
        if (err) {
          logger.error('Error during server shutdown:', err);
          process.exit(1);
        }
        
        logger.info('Server closed successfully');
        process.exit(0);
      });

      // Force shutdown after 10 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();
