import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import <PERSON><PERSON> from 'joi';
import { asyncHandler } from './errorHandler';
import { AppError } from '../types';

/**
 * Zod validation middleware factory
 */
export const validate = (schema: {
  body?: z.ZodSchema;
  query?: z.ZodSchema;
  params?: z.ZodSchema;
}) => {
  return asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      // Validate request body
      if (schema.body) {
        req.body = schema.body.parse(req.body);
      }

      // Validate query parameters
      if (schema.query) {
        req.query = schema.query.parse(req.query);
      }

      // Validate route parameters
      if (schema.params) {
        req.params = schema.params.parse(req.params);
      }

      next();
    }
  );
};

/**
 * Joi validation middleware factory
 */
export const validateRequest = (schema: Joi.ObjectSchema, target: 'body' | 'query' | 'params' = 'body') => {
  return asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      const { error, value } = schema.validate(req[target], {
        abortEarly: false,
        allowUnknown: false,
        stripUnknown: true
      });

      if (error) {
        const errorMessage = error.details.map((detail: any) => detail.message).join(', ');
        throw new AppError(`Validation error: ${errorMessage}`, 400);
      }

      // Replace the original data with validated data
      req[target] = value;
      next();
    }
  );
};
