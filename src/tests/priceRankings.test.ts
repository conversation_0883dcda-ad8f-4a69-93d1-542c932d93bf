import request from 'supertest';
import { createApp } from '../app';
import db from '../config/knex';

const app = createApp();

describe('Price Rankings API', () => {
  beforeAll(async () => {
    // 确保数据库连接正常
    await db.raw('SELECT 1');
  });

  afterAll(async () => {
    // 清理测试数据
    await db('price_trend_rankings').where('ranking_date', '>=', '2025-01-01').del();
    await db.destroy();
  });

  describe('GET /api/v1/categories/price-rankings', () => {
    beforeEach(async () => {
      // 插入测试数据
      const testData = [
        {
          ranking_date: '2025-06-19',
          category_id: 1,
          category_level: 3,
          category_name: 'iPhone 15 Pro Max',
          brand_name: '苹果',
          model_name: 'iPhone 15',
          sub_model_name: 'iPhone 15 Pro Max',
          memory_size: '256GB',
          tag_name: '靓机',
          group_name: '成色',
          current_price: 8999.00,
          previous_price: 8799.00,
          price_change: 200.00,
          change_percentage: 2.27,
          trend_type: 'RISE',
          ranking_position: 1,
          ranking_type: 'RISE_RANKING',
          status: 1
        },
        {
          ranking_date: '2025-06-19',
          category_id: 2,
          category_level: 3,
          category_name: 'Mate 60 Pro',
          brand_name: '华为',
          model_name: 'Mate 60',
          sub_model_name: 'Mate 60 Pro',
          memory_size: '512GB',
          tag_name: '靓机',
          group_name: '成色',
          current_price: 5999.00,
          previous_price: 6399.00,
          price_change: -400.00,
          change_percentage: -6.25,
          trend_type: 'FALL',
          ranking_position: 1,
          ranking_type: 'FALL_RANKING',
          status: 1
        }
      ];

      await db('price_trend_rankings').insert(testData);
    });

    afterEach(async () => {
      // 清理测试数据
      await db('price_trend_rankings').where('ranking_date', '2025-06-19').del();
    });

    it('should get rise rankings successfully', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings')
        .query({
          type: 'RISE_RANKING',
          date: '2025-06-19',
          limit: 10
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(1);
      expect(response.body.data.list[0].ranking_type).toBe('RISE_RANKING');
      expect(response.body.data.list[0].trend_type).toBe('RISE');
      expect(response.body.data.list[0].change_percentage).toBe(2.27);
    });

    it('should get fall rankings successfully', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings')
        .query({
          type: 'FALL_RANKING',
          date: '2025-06-19',
          limit: 10
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(1);
      expect(response.body.data.list[0].ranking_type).toBe('FALL_RANKING');
      expect(response.body.data.list[0].trend_type).toBe('FALL');
      expect(response.body.data.list[0].change_percentage).toBe(-6.25);
    });

    it('should filter by category level', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings')
        .query({
          type: 'RISE_RANKING',
          date: '2025-06-19',
          category_level: 3
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list.every((item: any) => item.category_level === 3)).toBe(true);
    });

    it('should return pagination info', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings')
        .query({
          type: 'RISE_RANKING',
          date: '2025-06-19',
          page: 1,
          limit: 10
        });

      expect(response.status).toBe(200);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(10);
      expect(typeof response.body.data.pagination.total).toBe('number');
      expect(typeof response.body.data.pagination.totalPages).toBe('number');
    });

    it('should return 400 for invalid ranking type', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings')
        .query({
          type: 'INVALID_TYPE',
          date: '2025-06-19'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid ranking type');
    });
  });

  describe('GET /api/v1/categories/price-rankings/stats', () => {
    beforeEach(async () => {
      // 插入测试统计数据
      const testData = [
        {
          ranking_date: '2025-06-19',
          category_id: 1,
          category_level: 3,
          category_name: 'iPhone 15 Pro Max',
          brand_name: '苹果',
          current_price: 8999.00,
          previous_price: 8799.00,
          price_change: 200.00,
          change_percentage: 2.27,
          trend_type: 'RISE',
          ranking_position: 1,
          ranking_type: 'RISE_RANKING',
          status: 1
        },
        {
          ranking_date: '2025-06-19',
          category_id: 2,
          category_level: 3,
          category_name: 'Mate 60 Pro',
          brand_name: '华为',
          current_price: 5999.00,
          previous_price: 6399.00,
          price_change: -400.00,
          change_percentage: -6.25,
          trend_type: 'FALL',
          ranking_position: 1,
          ranking_type: 'FALL_RANKING',
          status: 1
        }
      ];

      await db('price_trend_rankings').insert(testData);
    });

    afterEach(async () => {
      await db('price_trend_rankings').where('ranking_date', '2025-06-19').del();
    });

    it('should get statistics successfully', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings/stats')
        .query({ date: '2025-06-19' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.date).toBe('2025-06-19');
      expect(response.body.data.statistics).toBeDefined();
      expect(Array.isArray(response.body.data.statistics)).toBe(true);
      expect(response.body.data.levelDistribution).toBeDefined();
      expect(response.body.data.topMovers).toBeDefined();
      expect(response.body.data.topMovers.topRise).toBeDefined();
      expect(response.body.data.topMovers.topFall).toBeDefined();
    });

    it('should use today date when no date provided', async () => {
      const today = new Date().toISOString().split('T')[0];
      const response = await request(app)
        .get('/api/v1/categories/price-rankings/stats');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.date).toBe(today);
    });
  });

  describe('POST /api/v1/categories/price-rankings/calculate', () => {
    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/v1/categories/price-rankings/calculate');

      expect(response.status).toBe(401);
    });

    // Note: 实际的计算测试需要有效的JWT token和完整的数据库数据
    // 这里只测试认证要求
  });
});

describe('Price Ranking Calculator', () => {
  it('should be importable', async () => {
    const PriceRankingCalculator = (await import('../scripts/calculatePriceRankings')).default;
    expect(PriceRankingCalculator).toBeDefined();
    expect(typeof PriceRankingCalculator).toBe('function');
  });
});
