import { RecycleStoreModel } from '../models/RecycleStore';
import { CreateRecycleStoreDto, UpdateRecycleStoreDto } from '../types';

// Mock database
jest.mock('../config/database', () => ({
  db: {
    raw: jest.fn(),
  }
}));

import { db } from '../config/database';

describe('RecycleStoreModel', () => {
  const mockDb = db as jest.Mocked<typeof db>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new recycle store successfully', async () => {
      const storeData: CreateRecycleStoreDto = {
        storeCode: 'CD001',
        storeName: '宝丰回收成都青羊店',
        province: '四川省',
        city: '成都市',
        district: '青羊区',
        detailedAddress: '赛格广场一楼5032-5033号',
        longitude: 104.0668,
        latitude: 30.6598,
        salesPhone: '***********',
        salesWechat: '***********',
        recyclePhone: '***********',
        recycleWechat: '***********',
        mainPhone: '***********',
        email: '<EMAIL>',
        businessHours: {
          monday: '09:00-18:00',
          tuesday: '09:00-18:00',
          wednesday: '09:00-18:00',
          thursday: '09:00-18:00',
          friday: '09:00-18:00',
          saturday: '09:00-17:00',
          sunday: '10:00-17:00'
        },
        businessStatus: 'OPEN',
        storeType: 'STANDARD',
        storeArea: 120.5,
        serviceRadius: 10,
        supportedCategories: [1, 2, 3, 4, 5]
      };

      // Mock findByStoreCode to return null (store doesn't exist)
      mockDb.raw.mockResolvedValueOnce([[]]);

      // Mock insert operation
      mockDb.raw.mockResolvedValueOnce([{ insertId: 1 }]);

      // Mock findById to return the created store
      const mockStore = {
        id: 1,
        store_code: 'CD001',
        store_name: '宝丰回收成都青羊店',
        province: '四川省',
        city: '成都市',
        district: '青羊区',
        detailed_address: '赛格广场一楼5032-5033号',
        full_address: '四川省成都市青羊区赛格广场一楼5032-5033号',
        longitude: 104.0668,
        latitude: 30.6598,
        sales_phone: '***********',
        sales_wechat: '***********',
        recycle_phone: '***********',
        recycle_wechat: '***********',
        main_phone: '***********',
        email: '<EMAIL>',
        business_hours: JSON.stringify(storeData.businessHours),
        business_status: 'OPEN',
        store_type: 'STANDARD',
        store_area: 120.5,
        employee_count: 0,
        service_radius: 10,
        supported_categories: JSON.stringify([1, 2, 3, 4, 5]),
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockDb.raw.mockResolvedValueOnce([[mockStore]]);

      const result = await RecycleStoreModel.create(storeData);

      expect(result).toBeDefined();
      expect(result.storeCode).toBe('CD001');
      expect(result.storeName).toBe('宝丰回收成都青羊店');
      expect(result.fullAddress).toBe('四川省成都市青羊区赛格广场一楼5032-5033号');
      expect(mockDb.raw).toHaveBeenCalledTimes(3);
    });

    it('should throw error if store code already exists', async () => {
      const storeData: CreateRecycleStoreDto = {
        storeCode: 'CD001',
        storeName: '宝丰回收成都青羊店',
        province: '四川省',
        city: '成都市',
        district: '青羊区',
        detailedAddress: '赛格广场一楼5032-5033号',
        mainPhone: '***********'
      };

      // Mock findByStoreCode to return existing store
      const existingStore = {
        id: 1,
        store_code: 'CD001',
        store_name: '已存在的门店',
        // ... other fields
      };

      mockDb.raw.mockResolvedValueOnce([[existingStore]]);

      await expect(RecycleStoreModel.create(storeData)).rejects.toThrow('Store code already exists');
    });
  });

  describe('findById', () => {
    it('should return store when found', async () => {
      const mockStore = {
        id: 1,
        store_code: 'CD001',
        store_name: '宝丰回收成都青羊店',
        province: '四川省',
        city: '成都市',
        district: '青羊区',
        detailed_address: '赛格广场一楼5032-5033号',
        full_address: '四川省成都市青羊区赛格广场一楼5032-5033号',
        longitude: 104.0668,
        latitude: 30.6598,
        sales_phone: '***********',
        sales_wechat: '***********',
        recycle_phone: '***********',
        recycle_wechat: '***********',
        main_phone: '***********',
        email: '<EMAIL>',
        business_hours: null,
        business_status: 'OPEN',
        store_type: 'STANDARD',
        store_area: 120.5,
        employee_count: 0,
        service_radius: 10,
        supported_categories: null,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockDb.raw.mockResolvedValueOnce([[mockStore]]);

      const result = await RecycleStoreModel.findById(1);

      expect(result).toBeDefined();
      expect(result?.id).toBe(1);
      expect(result?.storeCode).toBe('CD001');
      expect(result?.storeName).toBe('宝丰回收成都青羊店');
    });

    it('should return null when store not found', async () => {
      mockDb.raw.mockResolvedValueOnce([[]]);

      const result = await RecycleStoreModel.findById(999);

      expect(result).toBeNull();
    });
  });

  describe('findAll', () => {
    it('should return stores with pagination', async () => {
      const mockStores = [
        {
          id: 1,
          store_code: 'CD001',
          store_name: '宝丰回收成都青羊店',
          province: '四川省',
          city: '成都市',
          district: '青羊区',
          detailed_address: '赛格广场一楼5032-5033号',
          full_address: '四川省成都市青羊区赛格广场一楼5032-5033号',
          longitude: 104.0668,
          latitude: 30.6598,
          sales_phone: '***********',
          sales_wechat: '***********',
          recycle_phone: '***********',
          recycle_wechat: '***********',
          main_phone: '***********',
          email: '<EMAIL>',
          business_hours: null,
          business_status: 'OPEN',
          store_type: 'STANDARD',
          store_area: 120.5,
          employee_count: 0,
          service_radius: 10,
          supported_categories: null,
          status: 1,
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      // Mock count query
      mockDb.raw.mockResolvedValueOnce([[{ total: 1 }]]);
      // Mock data query
      mockDb.raw.mockResolvedValueOnce([mockStores]);

      const result = await RecycleStoreModel.findAll({ page: 1, limit: 20 });

      expect(result.stores).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.stores[0].storeCode).toBe('CD001');
    });
  });

  describe('update', () => {
    it('should update store successfully', async () => {
      const updateData: UpdateRecycleStoreDto = {
        storeName: '更新后的门店名称',
        salesPhone: '***********'
      };

      // Mock findById to return existing store
      const existingStore = {
        id: 1,
        store_code: 'CD001',
        store_name: '宝丰回收成都青羊店',
        province: '四川省',
        city: '成都市',
        district: '青羊区',
        detailed_address: '赛格广场一楼5032-5033号',
        full_address: '四川省成都市青羊区赛格广场一楼5032-5033号',
        longitude: 104.0668,
        latitude: 30.6598,
        sales_phone: '***********',
        sales_wechat: '***********',
        recycle_phone: '***********',
        recycle_wechat: '***********',
        main_phone: '***********',
        email: '<EMAIL>',
        business_hours: null,
        business_status: 'OPEN',
        store_type: 'STANDARD',
        store_area: 120.5,
        employee_count: 0,
        service_radius: 10,
        supported_categories: null,
        status: 1,
        created_at: new Date(),
        updated_at: new Date()
      };

      mockDb.raw.mockResolvedValueOnce([[existingStore]]);

      // Mock update operation
      mockDb.raw.mockResolvedValueOnce([{ affectedRows: 1 }]);

      // Mock findById to return updated store
      const updatedStore = { ...existingStore, store_name: '更新后的门店名称', sales_phone: '***********' };
      mockDb.raw.mockResolvedValueOnce([[updatedStore]]);

      const result = await RecycleStoreModel.update(1, updateData);

      expect(result.storeName).toBe('更新后的门店名称');
      expect(result.salesPhone).toBe('***********');
    });

    it('should throw error if store not found', async () => {
      mockDb.raw.mockResolvedValueOnce([[]]);

      await expect(RecycleStoreModel.update(999, { storeName: '测试' })).rejects.toThrow('Store not found');
    });
  });

  describe('delete', () => {
    it('should soft delete store successfully', async () => {
      mockDb.raw.mockResolvedValueOnce([{ affectedRows: 1 }]);

      const result = await RecycleStoreModel.delete(1);

      expect(result).toBe(true);
    });

    it('should return false if store not found', async () => {
      mockDb.raw.mockResolvedValueOnce([{ affectedRows: 0 }]);

      const result = await RecycleStoreModel.delete(999);

      expect(result).toBe(false);
    });
  });
});
