import request from 'supertest';
import { createApp } from '../app';
import db from '../config/knex';

const app = createApp();

describe('Consecutive Trends API', () => {
  beforeAll(async () => {
    // 确保数据库连接正常
    await db.raw('SELECT 1');
  });

  afterAll(async () => {
    // 清理测试数据
    await db('price_trend_rankings').where('ranking_date', '>=', '2025-06-22').del();
    await db.destroy();
  });

  describe('GET /api/v1/categories/price-rankings/consecutive', () => {
    beforeEach(async () => {
      // 插入测试数据
      const testData = [
        {
          ranking_date: '2025-06-22',
          category_id: 101,
          category_level: 3,
          category_name: 'iPhone 15 Pro',
          brand_name: '苹果',
          model_name: 'iPhone 15',
          sub_model_name: 'iPhone 15 Pro',
          memory_size: '256GB',
          tag_name: '靓机',
          group_name: '成色',
          current_price: 7999.00,
          previous_price: 7799.00,
          price_change: 200.00,
          change_percentage: 2.56,
          trend_type: 'RISE',
          ranking_position: 1,
          ranking_type: 'RISE_RANKING',
          consecutive_rise_days: 3,
          consecutive_fall_days: 0,
          trend_signal: 'CONSECUTIVE_RISE_3',
          status: 1
        },
        {
          ranking_date: '2025-06-22',
          category_id: 102,
          category_level: 3,
          category_name: 'Mate 60 Pro',
          brand_name: '华为',
          model_name: 'Mate 60',
          sub_model_name: 'Mate 60 Pro',
          memory_size: '512GB',
          tag_name: '靓机',
          group_name: '成色',
          current_price: 5499.00,
          previous_price: 5899.00,
          price_change: -400.00,
          change_percentage: -6.78,
          trend_type: 'FALL',
          ranking_position: 1,
          ranking_type: 'FALL_RANKING',
          consecutive_rise_days: 0,
          consecutive_fall_days: 2,
          trend_signal: 'CONSECUTIVE_FALL_2',
          status: 1
        },
        {
          ranking_date: '2025-06-22',
          category_id: 103,
          category_level: 3,
          category_name: 'Xiaomi 14',
          brand_name: '小米',
          model_name: 'Xiaomi 14',
          sub_model_name: 'Xiaomi 14',
          memory_size: '256GB',
          tag_name: '靓机',
          group_name: '成色',
          current_price: 4299.00,
          previous_price: 4199.00,
          price_change: 100.00,
          change_percentage: 2.38,
          trend_type: 'RISE',
          ranking_position: 2,
          ranking_type: 'RISE_RANKING',
          consecutive_rise_days: 1,
          consecutive_fall_days: 0,
          trend_signal: 'NORMAL',
          status: 1
        }
      ];

      await db('price_trend_rankings').insert(testData);
    });

    afterEach(async () => {
      // 清理测试数据
      await db('price_trend_rankings').where('ranking_date', '2025-06-22').del();
    });

    it('should return consecutive rise items', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings/consecutive')
        .query({
          trend_type: 'RISE',
          min_days: 2,
          date: '2025-06-22'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(1);
      expect(response.body.data.list[0].category_name).toBe('iPhone 15 Pro');
      expect(response.body.data.list[0].consecutive_rise_days).toBe(3);
      expect(response.body.data.list[0].trend_signal).toBe('CONSECUTIVE_RISE_3');
    });

    it('should return consecutive fall items', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings/consecutive')
        .query({
          trend_type: 'FALL',
          min_days: 2,
          date: '2025-06-22'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(1);
      expect(response.body.data.list[0].category_name).toBe('Mate 60 Pro');
      expect(response.body.data.list[0].consecutive_fall_days).toBe(2);
      expect(response.body.data.list[0].trend_signal).toBe('CONSECUTIVE_FALL_2');
    });

    it('should return all consecutive items when no trend_type specified', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings/consecutive')
        .query({
          min_days: 2,
          date: '2025-06-22'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(2);
      expect(response.body.data.meta.trend_type).toBe('all');
    });

    it('should handle pagination correctly', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings/consecutive')
        .query({
          min_days: 2,
          date: '2025-06-22',
          page: 1,
          limit: 1
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(1);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(1);
      expect(response.body.data.pagination.total).toBe(2);
      expect(response.body.data.pagination.totalPages).toBe(2);
    });

    it('should return empty list when no consecutive items found', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings/consecutive')
        .query({
          min_days: 5,
          date: '2025-06-22'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(0);
      expect(response.body.data.pagination.total).toBe(0);
    });
  });

  describe('Enhanced getPriceRankings with consecutive fields', () => {
    beforeEach(async () => {
      // 插入测试数据
      const testData = [
        {
          ranking_date: '2025-06-22',
          category_id: 101,
          category_level: 3,
          category_name: 'iPhone 15 Pro',
          brand_name: '苹果',
          model_name: 'iPhone 15',
          sub_model_name: 'iPhone 15 Pro',
          memory_size: '256GB',
          tag_name: '靓机',
          group_name: '成色',
          current_price: 7999.00,
          previous_price: 7799.00,
          price_change: 200.00,
          change_percentage: 2.56,
          trend_type: 'RISE',
          ranking_position: 1,
          ranking_type: 'RISE_RANKING',
          consecutive_rise_days: 3,
          consecutive_fall_days: 0,
          trend_signal: 'CONSECUTIVE_RISE_3',
          status: 1
        }
      ];

      await db('price_trend_rankings').insert(testData);
    });

    afterEach(async () => {
      // 清理测试数据
      await db('price_trend_rankings').where('ranking_date', '2025-06-22').del();
    });

    it('should include consecutive fields in price rankings response', async () => {
      const response = await request(app)
        .get('/api/v1/categories/price-rankings')
        .query({
          type: 'RISE_RANKING',
          date: '2025-06-22'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.list).toHaveLength(1);
      
      const item = response.body.data.list[0];
      expect(item.consecutive_rise_days).toBe(3);
      expect(item.consecutive_fall_days).toBe(0);
      expect(item.trend_signal).toBe('CONSECUTIVE_RISE_3');
    });
  });
});
