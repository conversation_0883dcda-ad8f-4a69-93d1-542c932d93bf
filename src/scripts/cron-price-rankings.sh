#!/bin/bash

# 价格涨跌榜定时计算脚本
# 每天定时运行，计算价格涨跌榜数据和连续涨跌标识
# 
# 使用方法:
# 1. 给脚本添加执行权限: chmod +x src/scripts/cron-price-rankings.sh
# 2. 添加到 crontab: 
#    crontab -e
#    # 每天早上 8:00 运行
#    0 8 * * * /path/to/your/project/src/scripts/cron-price-rankings.sh
#
# 或者使用 PM2 定时任务:
# pm2 start src/scripts/cron-price-rankings.sh --cron "0 8 * * *" --name "price-rankings-cron"

# 设置项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
cd "$PROJECT_ROOT"

# 设置日志文件
LOG_FILE="$PROJECT_ROOT/logs/price-rankings-cron.log"
mkdir -p "$(dirname "$LOG_FILE")"

# 记录开始时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始执行价格涨跌榜计算任务" >> "$LOG_FILE"

# 检查 Node.js 环境
if ! command -v node &> /dev/null; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误: Node.js 未安装或不在 PATH 中" >> "$LOG_FILE"
    exit 1
fi

# 检查项目依赖
if [ ! -d "node_modules" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误: node_modules 目录不存在，请先运行 npm install" >> "$LOG_FILE"
    exit 1
fi

# 运行价格涨跌榜计算脚本
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始计算价格涨跌榜..." >> "$LOG_FILE"

# 使用 npm script 运行
if npm run calculate-rankings >> "$LOG_FILE" 2>&1; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 价格涨跌榜计算完成" >> "$LOG_FILE"
    exit 0
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 价格涨跌榜计算失败" >> "$LOG_FILE"
    exit 1
fi
